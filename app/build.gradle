apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'

repositories {
    mavenCentral()
    maven { url 'https://maven.fabric.io/public' }
}

android {
    compileSdkVersion 33
    defaultConfig {
        applicationId "fois.dailyreportsystem"
        minSdkVersion 16
        targetSdkVersion 33
        versionCode 28
        versionName "1.1.10.002"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        multiDexEnabled true
        ndkVersion "25.0.8775105"
        ndk {
            debugSymbolLevel 'FULL'
        }
    }
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            lintOptions {
                disable 'MissingTranslation'
                checkReleaseBuilds false
                abortOnError false
            }

        }
        debug {
            firebaseCrashlytics {
                mappingFileUploadEnabled false
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    androidTestImplementation('androidx.test.espresso:espresso-core:3.1.0', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:${kotlin_version}"
    implementation 'org.parceler:parceler-api:1.1.12'
    annotationProcessor  'org.parceler:parceler:1.1.12'

    implementation 'androidx.drawerlayout:drawerlayout:1.0.0'
    implementation 'me.mattak:moment:0.0.4'

    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'com.google.code.gson:gson:2.8.5'
    implementation 'com.google.android.material:material:1.0.0'
    implementation 'com.google.firebase:firebase-messaging:21.0.1'
    implementation 'com.google.firebase:firebase-core:18.0.1'
    implementation 'com.annimon:stream:1.2.1'
    testImplementation 'junit:junit:4.13-beta-3'
    implementation 'com.google.firebase:firebase-analytics:18.0.0'
    implementation 'com.google.firebase:firebase-crashlytics:17.3.0'

    implementation 'com.squareup:otto:1.3.8'
    implementation 'net.danlew:android.joda:2.6.0'
    implementation 'androidx.exifinterface:exifinterface:1.1.0-beta01'

    implementation 'com.rishabhharit.roundedimageview:RoundedImageView:0.8.4'
    implementation 'com.github.barteksc:android-pdf-viewer:3.2.0-beta.1'
}

androidExtensions {
    experimental = true
}
apply plugin: 'com.google.gms.google-services'
apply plugin: 'kotlin-android-extensions'
