package fois.dailyreportsystem.activity

// 作成日：2017/08/17
// 更新日：2018/04/12

import android.annotation.SuppressLint
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ListView
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.appcompat.app.ActionBarDrawerToggle
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.view.GravityCompat
import androidx.drawerlayout.widget.DrawerLayout
import androidx.fragment.app.Fragment
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import fois.dailyreportsystem.R
import fois.dailyreportsystem.activity.login.LoginActivity
import fois.dailyreportsystem.activity.message.MessageGroupListActivity
import fois.dailyreportsystem.activity.plan.PlanRegistrationActivity
import fois.dailyreportsystem.activity.setting.FirstSettingActivity
import fois.dailyreportsystem.activity.setting.SettingActivity
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.AreaData
import fois.dailyreportsystem.data.JsonData
import fois.dailyreportsystem.data.Worker
import fois.dailyreportsystem.data.adapter.AreaAdapter
import fois.dailyreportsystem.fragment.*
import fois.dailyreportsystem.util.*
import fois.dailyreportsystem.util.Constant.FRAGMENT_SCREEN_MAIN
import fois.dailyreportsystem.util.Constant.FRAGMENT_SCREEN_PLAN
import fois.dailyreportsystem.util.Constant.FRAGMENT_SCREEN_REQUEST
import fois.dailyreportsystem.util.Constant.FRAGMENT_SCREEN_REQUEST_LIST
import fois.dailyreportsystem.util.Constant.FRAGMENT_SCREEN_SURVEY
import fois.dailyreportsystem.util.Constant.FRAGMENT_SCREEN_WORKSCHEDULE
import kotlinx.android.synthetic.main.main_footer.*
import kotlinx.android.synthetic.main.main_layout.*
import org.json.JSONObject
import java.io.File
import java.lang.reflect.Type
import java.util.*
import kotlin.collections.ArrayList

// 邸一覧画面のアクティビティ（アプリ起動時に呼ばれる画面）
class MainActivity : BaseActivity() {

    var listPosition1: Int = 0        // リストビュー位置保存用
    var listPosition2: Int = 0        // リストビュー位置保存用

    var dir: File? = null                // キャッシュディレクトリ
    var deleteFileList: ArrayList<File> = ArrayList()    // 削除するファイルリスト

    private var mFirebaseAnalytics: FirebaseAnalytics? = null

    // 自動更新用
    private val updateReceiver: UpdateReceiver = UpdateReceiver()
    private val intentFilter: IntentFilter = IntentFilter()

    var selectFragment = 0

    var areaListView: ListView? = null

    private lateinit var fragment: Fragment

    /* -------------------- ライフサイクル -------------------- */
    /*
    起動：onCreate→onResume
    スリープから復帰、他画面からの復帰：onRestart→onResume
     */

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.main_layout)

        // 初回起動かどうか取得
        if (this.settings.getBoolean("FIRST_LAUNCH", true) && !this.settings.getBoolean("FIRST_SETTING")) {
            // 初回起動時は初期設定画面へ遷移させる
            val intent = Intent(applicationContext, FirstSettingActivity::class.java)
            startActivity(intent)
            return
        }

        this.settings.put("LOGIN_ACTIVITY", false)


        val setupFragment = intent.getIntExtra("RE_SETUP_FRAGMENT", -1)
        if(setupFragment != -1){
            this.settings.put("RE_SETUP_FRAGMENT",setupFragment)
        }

        // 期限切れのキャッシュファイル確認
        CacheCheck()


        mFirebaseAnalytics = FirebaseAnalytics.getInstance(this)
        FirebaseCrashlytics.getInstance().setUserId("00000000000")

        log(context?.javaClass!!.name, "onCreate")
    }

    override fun onResume() {
        super.onResume()

        // Android 8.0の場合はチャンネル登録
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // メッセージ機能のチャンネル登録
            createNotificationChannel()
        }

        // フッター描画
        makeFooter()

        // 初期設定が終わっているか判別
        if (!this.settings.getBoolean("FIRST_LAUNCH", true)) {

            // 現在の時間を保存
            this.settings.put("LAST_HANDLE", System.currentTimeMillis())

            // 未読件数の取得
            getUnreadCount()

            // tokenの送信
            if (this.settings.getBoolean("TOKEN_CHANGED", false)) {
                sendTokenAsync()
            }

            // 表示画面の切り替え
            selectFragment = this.settings.getInt("RE_SETUP_FRAGMENT", Constant.FRAGMENT_SCREEN_NONE)

            when (selectFragment) {
                Constant.FRAGMENT_SCREEN_MAIN -> dailyTap()
                Constant.FRAGMENT_SCREEN_PLAN -> scheduleTap()
                Constant.FRAGMENT_SCREEN_REQUEST -> surveyRequestTap()
                Constant.FRAGMENT_SCREEN_REQUEST_LIST -> orderRequestList()
                Constant.FRAGMENT_SCREEN_SURVEY -> actualPlaceSurveyTap()
                Constant.FRAGMENT_SCREEN_WORKSCHEDULE -> workScheduleTap()
                Constant.FRAGMENT_SCREEN_NONE -> {
                    when (this.settings.getString("COMPANY_ROLE")) {
                        Constant.WORKER -> dailyTap()
                        Constant.FCT -> dailyTap()
                        Constant.MAKER -> surveyRequestTap()
                    }
                }
            }
        }

        intentFilter.addAction("TaskListUpdate")
        registerReceiver(updateReceiver, intentFilter)
        updateReceiver.registerHandler(taskHandler)

        log(context?.javaClass!!.name, "onResume")
    }

    // スリープからの復帰、他画面からの復帰
    override fun onRestart() {
        super.onRestart()
        // リストビューの位置を取得する
        changeHeader()
        log(context?.javaClass!!.name, "onRestart")
    }

    override fun onPause() {
        super.onPause()
        // レシーバーの解放
        if (UpdateReceiver.handler != null) {
            unregisterReceiver(updateReceiver)
        }
        log(context?.javaClass!!.name, "onPause")
    }

    /* -------------------- Handler -------------------- */
    private val taskHandler = object : Handler() {
        override fun handleMessage(data: Message) {
            getUnreadCount()
        }
    }

    /* -------------------- キャッシュ制御 -------------------- */
    private fun CacheCheck() {
        dir = context!!.cacheDir
        val fileList: Array<File> = dir!!.listFiles()
        val date = Date()                // 現在日時
        val dateLong: Long = date.time        // 現在日時（比較用）
        var fileDate: Date                    // ファイルの更新日時
        var fileDateLong: Long = 0            // ファイルの更新日時（比較用）

        try {
            if (fileList.isNotEmpty()) {
                for (i in fileList.indices) {
                    // ファイルの更新日時を取得
                    fileDate = Date(fileList[i].lastModified())
                    fileDateLong = fileDate.time
                    // 更新日時と現在日時を比較し30日経過していたら削除リストに追加
                    if ((dateLong - fileDateLong) / (1000 * 60 * 60 * 24) >= 30) {
                        deleteFileList.add(fileList[i])
                    }
                }
            }

            // キャッシュファイルを削除
            if (deleteFileList.isNotEmpty()) {
                for (i in 0 until deleteFileList.count()) {
                    deleteFileList[i].delete()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * @changeHeader ヘッダー切替
     * 各画面ごとのヘッダーを作成する。
     *
     * */
    private fun changeHeader() {

        /// 切り替え対象フィールド
        val layout: LinearLayout = findViewById(R.id.header_contents)
        val view: View?

        val drawerContents: LinearLayout = findViewById(R.id.drawer_contents)

        layout.removeAllViews()
        drawerContents.removeAllViews()

        val permission = this.settings.getString("COMPANY_ROLE")

        when (selectFragment) {
            FRAGMENT_SCREEN_MAIN -> {
                view = layoutInflater.inflate(R.layout.main_header, null)
                layout.addView(view)
            }
            FRAGMENT_SCREEN_PLAN -> {
                view = layoutInflater.inflate(R.layout.plan_list_header, null)
                val drawerView = layoutInflater.inflate(R.layout.area_menu_layout, null)
                val toolBar = view.findViewById(R.id.toolbar) as Toolbar
                val headerView = view.findViewById(R.id.plan_header_title) as TextView

                val fragment = supportFragmentManager.findFragmentByTag("PlanSchedule") as PlanScheduleFragment
                fragment.setHeaderView(headerView)

                // エリアタップ処理
                val drawerToggle: ActionBarDrawerToggle = object : ActionBarDrawerToggle(
                        this,
                        main_drawer_layout,
                        toolBar,
                        R.string.navi_drawer_open,
                        R.string.navi_drawer_close
                ) {
                    override fun onDrawerClosed(mview: View) {
                        super.onDrawerClosed(mview)
                    }

                    override fun onDrawerOpened(mview: View) {
                        super.onDrawerOpened(mview)
                    }
                }

                main_drawer_layout!!.addDrawerListener(drawerToggle)
                drawerToggle.syncState()

                drawerToggle.setToolbarNavigationClickListener {
                    drawerToggle.isDrawerIndicatorEnabled = true
                    main_drawer_layout!!.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED)
                }

                drawerToggle.isDrawerIndicatorEnabled = false
                main_drawer_layout!!.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED)

                // エリア一覧
                areaListView = drawerView.findViewById(R.id.area_list) as ListView

                val naviArrow = view.findViewById(R.id.menu_cancel) as ImageView
                naviArrow.setOnSafeClickListener {
                    main_drawer_layout!!.openDrawer(GravityCompat.START)
                }

                //　予定ヘッダ　予定登録へ遷移.
                val addPlan = view.findViewById(R.id.add_plan) as ImageView

                addPlan.setOnSafeClickListener {
                    val intent = Intent(this, PlanRegistrationActivity::class.java)
                    startActivity(intent)
                    overridePendingTransition(R.anim.slide_in_up, R.anim.fade_out)
                }

                val menuAllowClose = drawerView.findViewById(R.id.area_menu_close) as ImageView

                menuAllowClose.setOnSafeClickListener {
                    main_drawer_layout!!.closeDrawer(GravityCompat.START)

                    val areaAdapter = areaListView!!.adapter as AreaAdapter
                    val planScheduleFragment = supportFragmentManager.findFragmentByTag("PlanSchedule") as PlanScheduleFragment
                    planScheduleFragment.getSchedules()
                }

                // エリアの一覧を表示する。
                getAreaList()

                layout.addView(view)
                drawerContents.addView(drawerView)
            }

            // 空き状況ヘッダー
            FRAGMENT_SCREEN_REQUEST -> {

                view = layoutInflater.inflate(R.layout.free_schedule_header, null)
                val select_city_name = view.findViewById<TextView>(R.id.select_city_name)
                val map_icon = view.findViewById<ImageView>(R.id.map_icon)
                val switch_list = view.findViewById<ImageView>(R.id.switch_list)

                val readjustment = view.findViewById<ImageView>(R.id.readjustment_icon)

                readjustment.setOnSafeClickListener {
                    val freeStatus = supportFragmentManager.findFragmentByTag("FreeStatusCalendar") as FreeStatusCalendarFragment
                    freeStatus.forwardReadjustment()
                }

                val city = context!!.loadCity()
                if (city == null) {
                    select_city_name.text = "未選択"
                    select_city_name.setTextColor(ContextCompat.getColor(this, R.color.system_orange))
                    map_icon.setColorFilter(ContextCompat.getColor(this, R.color.system_orange))
                } else {
                    select_city_name.text = city.PrefName + "\n" + city.CityName
                    select_city_name.setTextColor(ContextCompat.getColor(this, R.color.white))
                    map_icon.setColorFilter(ContextCompat.getColor(this, R.color.white))
                }

                select_city_name.setOnSafeClickListener {
                    val freeStatusCalendarFragment = supportFragmentManager.findFragmentByTag("FreeStatusCalendar") as FreeStatusCalendarFragment
                    freeStatusCalendarFragment.forwardPref()
                }

                map_icon.setOnSafeClickListener {
                    val freeStatusCalendarFragment = supportFragmentManager.findFragmentByTag("FreeStatusCalendar") as FreeStatusCalendarFragment
                    freeStatusCalendarFragment.forwardPref()
                }

                switch_list.setOnSafeClickListener {
                    orderRequestList()
                }

                layout.addView(view)

                researchTaskCount()

            }
            //
            FRAGMENT_SCREEN_REQUEST_LIST -> {
                view = layoutInflater.inflate(R.layout.order_request_list_header, null)

                val readjustment = view.findViewById<ImageView>(R.id.readjustment_icon)

                readjustment.setOnSafeClickListener {
                    val orderStatus = supportFragmentManager.findFragmentByTag("RequestStatusList") as OrderStatusListFragment
                    orderStatus.forwardReadjustment()
                }

                val switchList = view.findViewById<ImageView>(R.id.switch_list)

                switchList.setOnSafeClickListener {
                    surveyRequestTap()
                }

                this.position.put("ORDER_STATUS_LIST1", 0)
                this.position.put("ORDER_STATUS_LIST2", 0)

                layout.addView(view)

                researchTaskCount()
            }
            FRAGMENT_SCREEN_SURVEY -> {
                view = layoutInflater.inflate(R.layout.survey_header, null)

                val searchSurvey = view.findViewById<TextView>(R.id.search_survey)

                val headerLeftText = this.settings.getString("SELECT__NAME")
                if (headerLeftText != "") {
                    searchSurvey.text = headerLeftText
                }

                searchSurvey.setOnSafeClickListener {
                    val surveyFragment = supportFragmentManager.findFragmentByTag("ResearchTaskData") as SurveyFragment
                    surveyFragment.forwardSelectArea()
                }

                layout.addView(view)
            }
            FRAGMENT_SCREEN_WORKSCHEDULE -> {
                view = layoutInflater.inflate(R.layout.workschedule_header, null)

                val searchWorkSchedule = view.findViewById<TextView>(R.id.search_workschedule)

                searchWorkSchedule.setOnSafeClickListener {
                    val workScheduleFragment = supportFragmentManager.findFragmentByTag("WorkSchedule") as WorkScheduleFragment
                    workScheduleFragment.showResult()
                }

                layout.addView(view)
            }
        }
    }

    /* -------------------- フッター生成 -------------------- */

    private fun makeFooter() {
        /// 切り替え対象フィールド
        // 権限ごとにフッター変更
        when (this.settings.getString("COMPANY_ROLE")!!) {
            // 職長
            Constant.WORKER -> {
                footer_daily_report.visibility = View.VISIBLE
                footer_schedule.visibility = View.GONE
                footer_survey_request.visibility = View.GONE
                footer_actual_place_survey.visibility = View.GONE
                footer_message.visibility = View.VISIBLE
                footer_movie.visibility = View.VISIBLE
                footer_workschedule.visibility = View.VISIBLE
            }
            // 販売店
            Constant.MAKER -> {
                footer_daily_report.visibility = View.GONE
                footer_schedule.visibility = View.GONE
                footer_survey_request.visibility = View.VISIBLE
                footer_actual_place_survey.visibility = View.GONE
                footer_message.visibility = View.GONE
				footer_movie.visibility = View.GONE
				footer_workschedule.visibility = View.GONE
            }
            // フジケミ様
            Constant.FCT -> {
                footer_daily_report.visibility = View.VISIBLE
                footer_schedule.visibility = View.VISIBLE
                footer_survey_request.visibility = View.VISIBLE
                footer_actual_place_survey.visibility = View.VISIBLE
                footer_message.visibility = View.VISIBLE
				footer_movie.visibility = View.VISIBLE
				footer_workschedule.visibility = View.VISIBLE
            }
        }

        // 設定ボタンタップ
        footer_config.setOnSafeClickListener { settingTap() }
        // 日報ボタンタップ
        if(footer_daily_report.visibility == View.VISIBLE) footer_daily_report.setOnSafeClickListener { dailyTap() }
        // スケジュールボタンタップ
        if(footer_schedule.visibility == View.VISIBLE) footer_schedule.setOnSafeClickListener { scheduleTap() }
        // メッセージボタンタップ
        if(footer_message.visibility == View.VISIBLE) footer_message.setOnSafeClickListener { messageTap() }
        // 現地調査依頼
        if(footer_actual_place_survey.visibility == View.VISIBLE) footer_actual_place_survey.setOnSafeClickListener { actualPlaceSurveyTap() }
        // 調査依頼
        if(footer_actual_place_survey.visibility == View.VISIBLE) footer_survey_request.setOnSafeClickListener {
            when (this.settings.getInt("SELECT_REQUEST_FRAGMENT", Constant.FRAGMENT_SCREEN_REQUEST )) {
                Constant.FRAGMENT_SCREEN_REQUEST -> surveyRequestTap()
                Constant.FRAGMENT_SCREEN_REQUEST_LIST -> orderRequestList()
            }
        }
        // フジちゃんねる
        if(footer_movie.visibility == View.VISIBLE) footer_movie.setOnSafeClickListener { movieTap() }
        // 工程表検索
        if(footer_workschedule.visibility == View.VISIBLE) footer_workschedule.setOnSafeClickListener { workScheduleTap() }

    }

    /* -------------------- 画面遷移 -------------------- */
    // ログイン画面へ
    private fun loginRequest() {
        val intent = Intent(applicationContext, LoginActivity::class.java)
        startActivity(intent)
        overridePendingTransition(R.anim.slide_in_up, R.anim.fade_out)
    }

    // 設定画面へ
    private fun forwardSetting() {
        val intent = Intent(applicationContext, SettingActivity::class.java)
        startActivity(intent)
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
    }

    // 日報画面へ
    private fun forwardDaily() {
        selectFragment = FRAGMENT_SCREEN_MAIN

        this.settings.put("RE_SETUP_FRAGMENT", selectFragment)

        val fragment = MainFragment()

        val bundleFragment = Bundle()
        bundleFragment.putString("USER_ID", this.settings.getString("USER_ID"))
        fragment.arguments = bundleFragment

        supportFragmentManager.beginTransaction()
                .replace(R.id.main_contents, fragment, "Main")
                .commitNow()

        changeHeader()
    }

    // スケジュール画面へ
    private fun forwardSchedule() {
        selectFragment = FRAGMENT_SCREEN_PLAN
        val fragment = PlanScheduleFragment()


        this.settings.put("RE_SETUP_FRAGMENT", selectFragment)

        val bundleFragment = Bundle()
        bundleFragment.putString("USER_ID", this.settings.getString("USER_ID"))
        fragment.arguments = bundleFragment

        supportFragmentManager.beginTransaction()
                .replace(R.id.main_contents, fragment, "PlanSchedule")
                .commitNow()

        changeHeader()
    }

    //　調査依頼
    private fun forwardSurveyRequest() {
        selectFragment = FRAGMENT_SCREEN_REQUEST

        this.settings.put("RE_SETUP_FRAGMENT", selectFragment)
        this.settings.put("SELECT_REQUEST_FRAGMENT", selectFragment)

        val fragment = FreeStatusCalendarFragment()

        val bundleFragment = Bundle()
        bundleFragment.putString("USER_ID", this.settings.getString("USER_ID"))
        fragment.arguments = bundleFragment

        supportFragmentManager.beginTransaction()
                .replace(R.id.main_contents, fragment, "FreeStatusCalendar")
                .commitNow()

        changeHeader()
    }

    //　依頼状況
    private fun forwardOrderRequestList() {
        selectFragment = FRAGMENT_SCREEN_REQUEST_LIST

        this.settings.put("RE_SETUP_FRAGMENT", selectFragment)
        this.settings.put("SELECT_REQUEST_FRAGMENT", selectFragment)

        val fragment = OrderStatusListFragment()

        val bundleFragment = Bundle()
        bundleFragment.putString("USER_ID", this.settings.getString("USER_ID"))
        fragment.arguments = bundleFragment

        supportFragmentManager.beginTransaction()
                .replace(R.id.main_contents, fragment, "RequestStatusList")
                .commitNow()

        changeHeader()
    }

    // 現地調査画面へ
    private fun forwardActualPlaceSurvey() {
        selectFragment = FRAGMENT_SCREEN_SURVEY

        this.settings.put("RE_SETUP_FRAGMENT", selectFragment)

        val fragment = SurveyFragment()

        val bundleFragment = Bundle()
        bundleFragment.putString("USER_ID", this.settings.getString("USER_ID"))
        fragment.arguments = bundleFragment

        supportFragmentManager.beginTransaction()
                .replace(R.id.main_contents, fragment, "ResearchTaskData")
                .commitNow()

        changeHeader()
    }

    // メッセージグループ画面へ
    private fun forwardMessageGroup() {
        val intent = Intent(applicationContext, MessageGroupListActivity::class.java)
        startActivity(intent)
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
    }

    private fun forwardWorkSchedule() {
        selectFragment = FRAGMENT_SCREEN_WORKSCHEDULE

        this.settings.put("RE_SETUP_FRAGMENT", selectFragment)

        val fragment = WorkScheduleFragment()

        val bundleFragment = Bundle()
        bundleFragment.putString("USER_ID", this.settings.getString("USER_ID"))
        fragment.arguments = bundleFragment

        supportFragmentManager.beginTransaction()
                .replace(R.id.main_contents, fragment, "WorkSchedule")
                .commitNow()

        changeHeader()
    }

    /* -------------------- ビュー操作 -------------------- */
    // 未読件数表示・非表示
    @SuppressLint("SetTextI18n")
    private fun setUnreadCount(count: Int) {
        main_unread_badge_all.visibility = if (count > 0) {
            // 未読件数のセット（MAX99件まで）
            main_unread_badge_all.text = if (count > 99) {
                "99"
            } else {
                count.toString()
            }
            // 未読があるなら表示
            View.VISIBLE
        } else {
            // 未読が無い場合非表示
            View.GONE
        }
    }

    /* -------------------- タップイベント -------------------- */
    // 画面タッチイベント
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        try {
            if (this.settings.getInt("LIST_COUNT", 0) > 0) {

            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return super.dispatchTouchEvent(ev)
    }

    // 端末の戻るボタン
    override fun onBackPressed() {
        if (main_drawer_layout.isDrawerOpen(GravityCompat.START)) {
            main_drawer_layout.closeDrawer(GravityCompat.START)
        } else {
            finish()
        }
    }

    // 設定ボタンタップ
    private fun settingTap() {
        clearFooter()
        // 色を変更する
        footer_config_icon.setImageResource(R.drawable.menu_config)
        footer_config_text.setTextColor(ContextCompat.getColor(this, R.color.white))

        // 設定画面へ遷移
        forwardSetting()
    }

    // メッセージボタンタップ
    private fun messageTap() {
        clearFooter()
        // 色を変更する
        footer_message_icon.setImageResource(R.drawable.menu_message)
        footer_message_text.setTextColor(ContextCompat.getColor(this, R.color.white))

        // メッセージグループ画面へ遷移
        forwardMessageGroup()
    }

    // 日報ボタンタップ
    private fun dailyTap() {
        // メッセージ未読件数更新
        getUnreadCount()

        clearFooter()
        // 色を変更する
        footer_daily_report_icon.setImageResource(R.drawable.menu_report)
        footer_daily_report_text.setTextColor(ContextCompat.getColor(this, R.color.white))

        // 設定画面へ遷移
        forwardDaily()
    }

    // スケジュールボタンタップ
    private fun scheduleTap() {
        // メッセージ未読件数更新
        getUnreadCount()

        clearFooter()

        // 色を変更する
        footer_schedule_icon.setImageResource(R.drawable.menu_schedule)
        footer_schedule_text.setTextColor(ContextCompat.getColor(this, R.color.white))

        // 設定画面へ遷移
        forwardSchedule()
    }

    // 現地調査タンタップ
    private fun actualPlaceSurveyTap() {
        // メッセージ未読件数更新
        getUnreadCount()

        clearFooter()

        // 色を変更する
        footer_actual_place_survey_icon.setImageResource(R.drawable.menu_research)
        footer_actual_place_survey_text.setTextColor(ContextCompat.getColor(this, R.color.white))

        // 設定画面へ遷移
        forwardActualPlaceSurvey()
    }

    // 調査依頼タップ
    private fun surveyRequestTap() {
        // メッセージ未読件数更新
        getUnreadCount()

        clearFooter()

        // 色を変更する
        footer_survey_request_icon.setImageResource(R.drawable.menu_request)
        footer_survey_request_text.setTextColor(ContextCompat.getColor(this, R.color.white))

        // 設定画面へ遷移
        forwardSurveyRequest()
    }

    // フジちゃんねる
    private fun movieTap() {
        showMovie()
	}
	// 調査依頼タップ
    private fun workScheduleTap() {
        // メッセージ未読件数更新
        getUnreadCount()

        clearFooter()

        // 色を変更する
        footer_workschedule_icon.setImageResource(R.drawable.menu_work_schedule)
        footer_workschedule_text.setTextColor(ContextCompat.getColor(this, R.color.white))

        // 設定画面へ遷移
        forwardWorkSchedule()
    }

    // 依頼状況切り替え
    private fun orderRequestList() {
        // メッセージ未読件数更新
        getUnreadCount()

        clearFooter()

        // 色を変更する
        footer_survey_request_icon.setImageResource(R.drawable.menu_request)
        footer_survey_request_text.setTextColor(ContextCompat.getColor(this, R.color.white))

        // 設定画面へ遷移
        forwardOrderRequestList()
    }

    private fun clearFooter() {
        footer_config_icon.setImageResource(R.drawable.menu_config_hover)
        footer_config_text.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
        footer_message_icon.setImageResource(R.drawable.menu_message_hover)
        footer_message_text.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
        footer_daily_report_icon.setImageResource(R.drawable.menu_report_hover)
        footer_daily_report_text.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
        footer_schedule_icon.setImageResource(R.drawable.menu_schedule_hover)
        footer_schedule_text.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
        footer_survey_request_icon.setImageResource(R.drawable.menu_request_hover)
        footer_survey_request_text.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
        footer_actual_place_survey_icon.setImageResource(R.drawable.menu_research_hover)
        footer_actual_place_survey_text.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
        footer_workschedule_icon.setImageResource(R.drawable.menu_work_schedule_hover)
        footer_workschedule_text.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
    }

    /* -------------------- メッセージイベント -------------------- */
    @RequiresApi(Build.VERSION_CODES.O)
    private fun createNotificationChannel() {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        // 通知の詳細情報（通知設定画面に表示される情報）
        val notifyDescription = "この通知の詳細情報を設定します"

        // メッセージ
        var name = getString(R.string.channelName)
        var id = getString(R.string.channelID)
        // Channelの取得と生成
        if (notificationManager.getNotificationChannel(id) == null) {
            val mChannel = NotificationChannel(id, name, NotificationManager.IMPORTANCE_HIGH)
            mChannel.apply {
                description = notifyDescription
            }
            notificationManager.createNotificationChannel(mChannel)
        }

        // 調査依頼状況
        name = getString(R.string.surveyName)
        id = getString(R.string.surveyID)
        if (notificationManager.getNotificationChannel(id) == null) {
            val mChannel = NotificationChannel(id, name, NotificationManager.IMPORTANCE_HIGH)
            mChannel.apply {
                description = notifyDescription
            }
            notificationManager.createNotificationChannel(mChannel)
        }
    }

    /** ----------ヘッダー処理-------------- */
    private fun setAreaData(areaDataList: MutableList<AreaData>) {
        val adapter = AreaAdapter(context!!, areaDataList)
        areaListView!!.adapter = adapter
        adapter.notifyDataSetChanged()

        //areaListView!!.onItemClickListener = clickAreaSelect
    }

    /**
     * setBatchCount 再調整カウント数表示
     * */
    private fun setBatchCount(count: Int) {
        val layout: LinearLayout = findViewById(R.id.header_contents)

        val batchCount = layout.findViewById(R.id.readjustment_count) as TextView? ?: TextView(context)
        val readjustment = layout.findViewById(R.id.readjustment_icon) as ImageView? ?: ImageView(context)

        if (count > 0) {

            readjustment.visibility = View.VISIBLE

            batchCount.visibility = View.VISIBLE
            batchCount.text = count.toString()
        } else {
            val batchCount = layout.findViewById<TextView>(R.id.readjustment_count)
            val readjustment = layout.findViewById<ImageView>(R.id.readjustment_icon)

            readjustment.visibility = View.GONE
            batchCount.visibility = View.GONE
        }
    }

    /** ------- エリア選択 ---------- */

    // メッセージ未読件数の取得
    private fun getUnreadCount() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: JSONObject?) {
                clickPosition = null
                if (result == null) {
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val unreadCountObject = result.getJSONObject("MessageUnread")
                        val unreadCount = unreadCountObject.getInt("MessageUnreadCount")
                        // 未読件数を表示・非表示
                        setUnreadCount(unreadCount)
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.UnreadCountURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    // tokenの送信
    private fun sendTokenAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        context!!.settings.put("TOKEN_CHANGED", false)
                        log("result", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
                clickPosition = null
            }
        })

        // パラメータ作成
        val getList: String = this.settings.getString("USER_LIST" )
        val type: Type = object : TypeToken<ArrayList<Worker>>() {}.type
        val Users: ArrayList<Worker> = Gson().fromJson(getList, type)

        val jsonData: JsonData = JsonData()
        jsonData.NewDeviceID = this.settings.getString("NEW_DEVICE_ID" )
        jsonData.OldDeviceID = this.settings.getString("OLD_DEVICE_ID" )
        jsonData.DeviceType = "Android"
        jsonData.Users = Users

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID" )
        params.JsonData = Gson().toJson(jsonData)

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.UpdateDeviceIDURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    /** getAreaList エリア一覧
     *  操作ユーザが担当するエリアの一覧を取得する。
     *
     * */
    private fun getAreaList() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    var areaDataList = ArrayList<AreaData>()

                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val jsonList = result.getJSONArray("Areas")
                        // jsonList
                        areaDataList = AreaData.toArrayList(jsonList) ?: ArrayList<AreaData>()
                        areaDataList.add(0, AreaData.mySchedule())

                        // 保存データのチェック状況を反映
                        val loadArea = context!!.loadAreas()
                        if (loadArea != null) {
                            areaDataList.forEach { api ->
                                if (loadArea.find { save -> api.areaID == save.areaID && save.checked } != null) {
                                    api.checked = true
                                }
                            }
                        }
                        setAreaData(areaDataList)
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.OfficeID = this.settings.getString("OFFICE_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.UserAreas
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        log(context?.javaClass!!.name, url + jsonParams)
    }

    /** ResearchTaskCount 再調整カウント取得
     *
     *
     * */
    private fun researchTaskCount() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                var count = 0
                if (result == null) {
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        count = result.getInt("ResearchTaskCount")
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    setBatchCount(count)
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.SurveyStatusID = Constant.OrderStatusCode.READJUSTMENT.status

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskCount

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * 動画表示
     */
    private fun showMovie() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                var count = 0
                if (result == null) {
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") != 0) {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                        return
                    }

                    val url = result.getString("LinkMovieSite")
                    val uri = Uri.parse(url)
                    val intent = Intent(Intent.ACTION_VIEW, uri)
                    startActivity(intent)
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, "$ex")
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // パラメータ作成
        val params = SendParameter()
        val companyNumber = this.settings.getString("COMPANY_NUMBER_SELECT" )
        val phoneNumber = this.settings.getString("PHONE_NUMBER" )
        val password = this.settings.getString("PASSWORD" )
        if ((phoneNumber == "") || (password == "")) {
            // 旧バージョンからの継続セッションのため再ログインを促す
            showOKDialog("", context!!.getString(R.string.channel_param_error))
            return
        }

        params.CompanyNumber = companyNumber
        params.PhoneNumber = phoneNumber
        params.Password = password

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.LinkMovieSite

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}
