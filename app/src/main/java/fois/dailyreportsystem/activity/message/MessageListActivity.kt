package fois.dailyreportsystem.activity.message

// 作成日：2017/08/22
// 更新日：2018/10/18

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.text.Editable
import android.text.TextWatcher
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.*
import com.google.gson.Gson

import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.*
import fois.dailyreportsystem.data.adapter.MessageListAdapter
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.message_list_layout.*
import org.json.JSONObject
import java.util.*

// メッセージ一覧画面のアクティビティ（邸メッセージ、グループメッセージ共通）
class MessageListActivity : BaseActivity() {

    // 変数宣言
    private var listMessageList: ArrayList<MessageList> = ArrayList()       // メッセージ用配列
    private var listFirstCreate: Boolean = true   // リスト初期表示フラグ
    private var scrollEndPoint: Boolean = false  // スクロール終点フラグ
    private var scrollEndPointForUpdate: Boolean = false // 更新用スクロール終点フラグ
    private var messageSend: Boolean = false    // メッセージを送ったかどうか（メッセージ表示の際のみ使用）
    private var messageGroupName: String = ""   // グループ名（タイトル）

    private val updateReceiver: UpdateReceiver = UpdateReceiver()
    private val intentFilter: IntentFilter = IntentFilter()

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.message_list_layout)

        // クリックイベント
        // 戻るボタンタップ
        back_arrow.setOnClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            Back()
        }

        // 送信ボタンタップ
        message_send_button.setOnClickListener { Send() }
        // スクロール矢印タップ
        scroll_arrow.setOnClickListener {
            scroll_arrow.setImageResource(R.drawable.pagebottom_on)
            ScrollEnd()
        }

        // テキストイベント
        message_input.addTextChangedListener(object : TextWatcher {
            // 変更前
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            // 変更中
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            // 変更後
            override fun afterTextChanged(s: Editable) {
                Change()
            }
        })

        intentFilter.addAction("MessageListUpdate")
        registerReceiver(updateReceiver, intentFilter)
        updateReceiver.registerHandler(messageHandler)

        // リストビューの位置判定（終点か否か、のみ）
        message_detail_listview.setOnScrollListener(object : AbsListView.OnScrollListener {
            override fun onScrollStateChanged(view: AbsListView, scrollState: Int) {}
            // スクロール位置判定
            override fun onScroll(view: AbsListView, firstVisibleItem: Int, visibleItemCount: Int, totalItemCount: Int) {
                scrollEndPoint = totalItemCount <= firstVisibleItem + visibleItemCount + 4
                if (scrollEndPoint) {
                    scroll_arrow.visibility = View.GONE
                } else {
                    scroll_arrow.setImageResource(R.drawable.pagebottom)
                    scroll_arrow.visibility = View.VISIBLE
                }
            }
        })
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        // 現在の時間を保存
        this.settings.put("LAST_HANDLE", System.currentTimeMillis())
        // メッセージを取得
        GetMessageAsync()
    }

    override fun onRestart() {
        super.onRestart()
        // スリープから戻った時リストを空にする
        listMessageList.clear()
    }

    /* -------------------- Handler -------------------- */
    private val messageHandler = object : Handler() {
        override fun handleMessage(msg: Message) {
            if (!scrollEndPoint) {
                ShowMessages().Show(context, context!!.getString(R.string.dialog_new_message))
            }
            GetMessageAsync()
        }
    }

    /* -------------------- 画面遷移 -------------------- */

    // 前の画面へ戻る
    private fun Back() {
        if(clickPosition == null) {
            clickPosition = true
            if (this.settings.getString("BEFORE_ACTIVITY") == "MessageGroupListActivity") {
                val intent = Intent(this, MessageGroupListActivity::class.java)
                startActivity(intent)
            }
            // ビューの位置を元に戻す
            this.position.put("MESSAGE_LIST1", 0)
            this.position.put("MESSAGE_LIST2", 0)
            finish()
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)

            // レシーバーの解放
            if (UpdateReceiver.handler != null) {
                unregisterReceiver(updateReceiver)
            }
        }
    }

    /* -------------------- リストビュー -------------------- */
    // リストビューの表示
    private fun ListSet() {
        // グループ名の表示
        toolbar_title.text = messageGroupName

        if (messageSend) {
            message_input.setText("")
        }

        if (listMessageList.count() > 0) {
            val messageDetailAdapter = MessageListAdapter(this, listMessageList)
            message_detail_listview.adapter = messageDetailAdapter
            if (listFirstCreate) {
                // 未読から表示
                if (this.settings.getInt("MESSAGE_UNREAD_COUNT", 0) <= 1) {
                    message_detail_listview.setSelection(listMessageList.count() - 1)
                } else {
                    var selection: Int = 0
                    // メッセージを最後から確認
                    for (i in 0..listMessageList.count()-1) {
                        if (listMessageList[listMessageList.count() - (1+i)].layoutType != 0) {
                            // メッセージのとき
                            selection++
                        }
                        if (this.settings.getInt("MESSAGE_UNREAD_COUNT", 2) == selection || i == listMessageList.count()-1) {
                            // 最後から数えたメッセージ件数が未読と一致したらその位置を覚える
                            selection = (listMessageList.count()-1) - i
                            break
                        }
                    }
                    // 未読メッセージが一番上に来るように表示
                    message_detail_listview.setSelection(selection)
                }
                listFirstCreate = false
            } else if (messageSend || scrollEndPointForUpdate) {
                // メッセージ送信時、または更新時にリストの一番下が表示されていたら更新したメッセージを表示
                message_detail_listview.setSelection(listMessageList.count() - 1)
                messageSend = false
            } else {
                // リストの一番下が表示されていない場合、リストに追加するだけで表示位置は現在位置にする
                message_detail_listview.setSelectionFromTop(this.position.getInt("MESSAGE_LIST1", 0), this.position.getInt("MESSAGE_LIST2", 0))
            }
        }
    }

    /* -------------------- タップイベント -------------------- */
    // 端末のバックボタン制御
    override fun onBackPressed() {
        Back()
    }

    // 画面タッチイベント
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(main_layout.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す
        main_layout.requestFocus()

        try {
            if (listMessageList.size > 0) {
                // 現在のビューの位置を保存する
                this.position.put("MESSAGE_LIST1", message_detail_listview.firstVisiblePosition)
                this.position.put("MESSAGE_LIST2", message_detail_listview.getChildAt(0).top)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return super.dispatchTouchEvent(ev)
    }

    // 送信ボタン制御
    private fun Change() {
        val str = message_input.text.toString()
        if (str.replace("[\n 　]".toRegex(), "") == "") {
            message_send_button.setImageResource(R.drawable.send_off)
        } else {
            message_send_button.setImageResource(R.drawable.send)
        }
    }

    // 送信
    private fun Send() {
        // 入力項目を取得
        val str = message_input.text.toString()
        // 改行、半角スペース、全角スペースを空文字に変換しチェック
        if (str.replace("[\n 　]".toRegex(), "") != "") {
            // 変換後の入力項目が空ではない場合、メッセージを送信する
            message_send_button.setImageResource(R.drawable.send_on)
            SendMessageAsync(str)
        }
    }

    // スクロールさせる
    private fun ScrollEnd() {
        message_detail_listview.setSelection(listMessageList.count() - 1)
    }

    /* -------------------- 通信処理 -------------------- */
    // メッセージ取得
    private fun GetMessageAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {
                if (listFirstCreate) {
                    showHub()
                }
            }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 成功時
                        listMessageList.clear()
                        val messageGroups = result.getJSONArray("MessageGroups")
                        val groups = messageGroups.getJSONObject(0)
                        context!!.settings.put("MESSAGE_GROUP_ID", groups.getString("MessageGroupID"))
                        messageGroupName = groups.getString("MessageGroupName")
                        val messageItems = groups.getJSONArray("MessageItems")
                        var layoutType: Int = 0
                        var datetime: String = ""
                        var time: String = ""
                        // 比較用
                        var date1: String = ""
                        var date2: String = ""
                        var userId1: String = ""
                        var userId2: String = ""
                        for (i in 0..messageItems.length()-1) {
                            val data = messageItems.getJSONObject(i)
                            datetime = data.getString("Datetime")
                            date1 = datetime.substring(0, datetime.indexOf(" "))
                            time = datetime.substring(datetime.indexOf(" ")+1)
                            if (i == 0 || date1 != date2) {
                                layoutType = 0
                                val messageDetail = MessageList()
                                messageDetail.MessageDate = date1
                                messageDetail.layoutType = layoutType
                                listMessageList.add(messageDetail)
                                date2 = date1
                            }
                            val messageDetail = MessageList()
                            messageDetail.TaskName = groups.getString("MessageGroupName")
                            messageDetail.MessageTime = time
                            messageDetail.MessageAttachUrl = data.getString("MessageAttatchUrl")
                            messageDetail.MessageID = data.getString("MessageID")
                            messageDetail.MessageImageUrl = data.getString("MessageImageUrl")
                            messageDetail.MessageStampUrl = data.getString("MessageStampUrl")
                            messageDetail.MessageText = data.getString("MessageText")
                            messageDetail.MessageType = data.getString("MessageType")
                            val sender = data.getJSONObject("Sender")
                            userId1 = sender.getString("UserID")
                            if (userId1 == context!!.settings.getString("USER_ID")) {
                                // 自分のメッセージ
                                layoutType = 1
                            } else if (layoutType == 0 || userId1 != userId2) {
                                // 名前表示あり
                                layoutType = 2
                            } else {
                                // 名前表示なし
                                layoutType = 3
                            }
                            userId2 = userId1
                            messageDetail.CompanyID = sender.getString("CompanyID")
                            messageDetail.CompanyName = sender.getString("CompanyName")
                            messageDetail.CompanyNumber = sender.getString("CompanyNumber")
                            messageDetail.UserID = sender.getString("UserID")
                            messageDetail.UserName = sender.getString("UserName")
                            messageDetail.UserPhoneNumber = sender.getString("UserPhoneNumber")
                            messageDetail.layoutType = layoutType
                            listMessageList.add(messageDetail)
                        }
                        if (messageItems.length() > 0) {
                            // リスト表示
                            ListSet()
                        }
                    } else {
                        // 失敗時
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // 通信時に更新用スクロールフラグを保存
        scrollEndPointForUpdate = scrollEndPoint

        // URL、パラメータ作成
        var url: String = Constant.URL
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        if (this.settings.getString("BEFORE_ACTIVITY") == context!!.getString(R.string.message_group_list_activity)) {
            // メッセージグループ一覧から遷移した場合
            url += Constant.MessagesURL
            params.MessageGroupID = this.settings.getString("MESSAGE_GROUP_ID")
        } else {
            // 邸一覧から遷移した場合
            url += Constant.TaskMessagesURL
            params.TaskID = this.settings.getString("TASK_ID")
        }
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    // メッセージ送信
    private fun SendMessageAsync(MessageText: String) {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 成功時 メッセージ追加
                        // 追加分が表示されるように調整
                        messageSend = true
                        GetMessageAsync()
                    } else {
                        // 失敗時
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // URL、パラメータ作成
        val url: String = Constant.URL + Constant.AddMessageURL
        val jsonData: JsonData = JsonData()
        // グループタイプ（1：ユーザ（未使用）、2：邸、3：グループ）
        if (this.settings.getString("BEFORE_ACTIVITY") == context!!.getString(R.string.main_activity)) {
            jsonData.MessageGroupType = "2"
            jsonData.TaskID = this.settings.getString("TASK_ID")
        } else {
            jsonData.MessageGroupType = "3"
            jsonData.MessageGroupID = this.settings.getString("MESSAGE_GROUP_ID")
            jsonData.TaskID = ""
        }

        // 送信者
        val sender: Sender = Sender()
        sender.UserID = this.settings.getString("USER_ID")

        // メッセージタイプ、内容
        val MessageItems: ArrayList<MessageItems> = ArrayList()

        val messageItems: MessageItems = MessageItems()
        // メッセージタイプ（1：テキスト、2：スタンプ、3：ファイル）
        messageItems.MessageType = "1"
        messageItems.MessageText = MessageText
        messageItems.Sender = sender
        MessageItems.add(messageItems)

        jsonData.MessageItems = MessageItems

        val JsonData: ArrayList<JsonData> = ArrayList()
        JsonData.add(jsonData)

        val params: SendParameter = SendParameter()

        params.UserID = this.settings.getString("USER_ID")
        params.JsonData = Gson().toJson(JsonData)

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}
