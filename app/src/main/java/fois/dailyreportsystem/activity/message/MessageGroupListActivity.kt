package fois.dailyreportsystem.activity.message

// 作成日：2017/08/21
// 更新日：2018/10/18

import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.view.MotionEvent
import android.view.View
import android.widget.AdapterView
import com.google.gson.Gson

import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.MessageGroupList
import fois.dailyreportsystem.data.adapter.MessageGroupListAdapter
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.message_group_list_layout.*
import org.json.JSONObject

// メッセージグループ画面のアクティビティ
class MessageGroupListActivity : BaseActivity() {

    // 変数宣言
    private var listMessageGroupList: ArrayList<MessageGroupList> = ArrayList()       // 邸メッセージ一覧の配列

    private val updateReceiver: UpdateReceiver = UpdateReceiver()
    private val intentFilter: IntentFilter = IntentFilter()

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.message_group_list_layout)

        // クリックイベント
        // 戻るボタンタップ
        back_arrow.setOnClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            BackMain()
        }

        intentFilter.addAction("MessageGroupListUpdate")
        registerReceiver(updateReceiver, intentFilter)
        updateReceiver.registerHandler(messageHandler)
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        // メッセージリストを取得
        GetMessageListAsync()

    }

    override fun onRestart() {
        super.onRestart()
        // スリープから戻った時リストを空にする
        listMessageGroupList.clear()
    }

    /* -------------------- Handler -------------------- */
    private val messageHandler = object : Handler() {
        override fun handleMessage(data: Message) {
            val bundle: Bundle = data.data
            val messageGroupID: String? = bundle.getString("MessageGroupID")
            val unreadCount: String? = bundle.getString("UnreadCount")
            for (i in 0..listMessageGroupList.size-1) {
                if (listMessageGroupList[i].MessageGroupID == messageGroupID) {
                    listMessageGroupList[i].messageUnreadCount = unreadCount!!.toInt()
                    break
                }
            }
            ListSet()
        }
    }

    /* -------------------- 画面遷移 -------------------- */

    // 邸一覧画面へ戻る
    private fun BackMain() {
        // ビューの位置を戻す
        this.position.put("MESSAGE_GROUP_LIST1", 0)
        this.position.put("MESSAGE_GROUP_LIST2", 0)
        finish()
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
        // レシーバーの解放
        if (UpdateReceiver.handler != null) {
            unregisterReceiver(updateReceiver)
        }
    }

    /* -------------------- リストビュー -------------------- */
    private fun ListSet() {
        if (listMessageGroupList.size != 0) {
            // 表示できるメッセージグループあり
            message_list_listview.visibility = View.VISIBLE
            message_group_list_no_data.visibility = View.GONE

            val messageListAdapter = MessageGroupListAdapter(this, listMessageGroupList)
            message_list_listview.adapter = messageListAdapter
            // リストビューを保存した位置から表示する
            message_list_listview.setSelectionFromTop(this.position.getInt("MESSAGE_GROUP_LIST1", 0), this.position.getInt("MESSAGE_GROUP_LIST2", 0))
            message_list_listview.onItemClickListener = ClickItem
        } else {
            // 表示できるメッセージグループなし
            message_list_listview.visibility = View.GONE
            message_group_list_no_data.visibility = View.VISIBLE
        }
    }

    // リスト選択
    private val ClickItem = AdapterView.OnItemClickListener { parent, view, position, id ->
        val messageList = this@MessageGroupListActivity.message_list_listview.getItemAtPosition(position) as MessageGroupList

        this.settings.put("MESSAGE_GROUP_ID", messageList.MessageGroupID!!)
        this.settings.put("MESSAGE_GROUP_NAME", messageList.MessageGroupName!!)
        this.settings.put("MESSAGE_UNREAD_COUNT", messageList.messageUnreadCount)
        val intent = Intent(application!!, MessageListActivity::class.java)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)

        // レシーバーの解放
        if (UpdateReceiver.handler != null) {
            unregisterReceiver(updateReceiver)
        }
    }

    /* -------------------- タップイベント -------------------- */
    // 端末のバックボタン制御
    override fun onBackPressed() {
        BackMain()
    }

    // 画面タッチイベント
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        try {
            if (listMessageGroupList.size > 0) {
                // 現在のビューの位置を保存する
                this.position.put("MESSAGE_GROUP_LIST1", message_list_listview.firstVisiblePosition)
                this.position.put("MESSAGE_GROUP_LIST2", message_list_listview.getChildAt(0).top)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return super.dispatchTouchEvent(ev)
    }

    /* -------------------- 通信処理 -------------------- */
    private fun GetMessageListAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 成功時
                        listMessageGroupList.clear()
                        val data = result.getJSONArray("MessageGroups")
                        for (i in 0..data.length()-1) {
                            val data_row = data.getJSONObject(i)
                            val messageList = MessageGroupList()
                            messageList.MessageGroupID = data_row.getString("MessageGroupID")
                            messageList.MessageGroupName = data_row.getString("MessageGroupName")
                            messageList.messageUnreadCount = Integer.parseInt(data_row.getString("MessageUnreadCount"))
                            listMessageGroupList.add(messageList)
                        }
                        ListSet()
                    } else {
                        // 失敗時
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.MessagesGroupsURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}
