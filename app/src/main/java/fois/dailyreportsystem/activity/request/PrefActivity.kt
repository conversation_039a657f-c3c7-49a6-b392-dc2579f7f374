package fois.dailyreportsystem.activity.request

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.AdapterView
import android.widget.TextView
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.Pref
import fois.dailyreportsystem.data.adapter.PrefAdapter
import fois.dailyreportsystem.data.Region
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.pref_city_list_layout.*
import org.json.JSONObject

class PrefActivity : BaseActivity() {

    private var prefList: ArrayList<Pref> = ArrayList<Pref>()

    private var regionIndexMap: MutableMap<String, Int> = HashMap<String, Int>()

    private var regionList: ArrayList<Region> = ArrayList()

    override fun onCreate(savedInstanceState: Bundle?) {
        setContentView(R.layout.pref_city_list_layout)
        super.onCreate(savedInstanceState)

        close_arrow.setOnSafeClickListener { backFreeSchedule() }
        back_arrow.visibility = View.GONE

        toolbar_title.text = this.resources.getText(R.string.pref_title)


    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }
        getRegion()
    }

    override fun onRestart() {
        super.onRestart()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }
    }

    // 端末の戻るボタン
    override fun onBackPressed() {
        backFreeSchedule()
    }

    // 空き状況へ遷移
    private fun backFreeSchedule() {
        finish()
        overridePendingTransition(R.anim.fade_in, R.anim.slide_out_down)
    }

    // リストビューセット
    private fun setPrefListView() {
        val itemAdapter = PrefAdapter(this, prefList)
        pref_city_name.adapter = itemAdapter

        //Click item event
        pref_city_name.onItemClickListener = clickPrefItem
    }

    /**
     *  setSideIndex IndexViewを作成
     *
     */
    private fun setSideIndex() {
        val inflater = this.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        for (region in regionList) {
            // TextViewを生成し、side viewにインデックスとして追加する。
            val textView = inflater.inflate(R.layout.side_index_item, null) as TextView
            textView.text = region.RegionName
            textView.setOnClickListener(indexItemClick)
            index_name.addView(textView)
        }

        index_name.refreshDrawableState()
    }

    /**
     *  pref item click
     *
     */
    private val clickPrefItem = AdapterView.OnItemClickListener { parent, view, position, id ->
        val prefData = pref_city_name.getItemAtPosition(position) as Pref

        if (prefData.IsHeader) {
            return@OnItemClickListener
        }

        this.settings.put("PREF_ID", prefData.PrefID)

        val intent = Intent(this, CityActivity::class.java)
        intent.putExtra("PREF_ID", prefData.PrefID)
        intent.putExtra("PREF_NAME", prefData.PrefName)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
    }

    /**
     *  index item click
     *
     */
    private val indexItemClick = View.OnClickListener {
        var textView = it as TextView
        var regionName = textView.text.toString()
        var selectRegionId = ""

        for (region in regionList) {
            if (region.RegionName == regionName) {
                selectRegionId = region.RegionID
                break
            }
        }

        pref_city_name.setSelection(regionIndexMap[selectRegionId]!!)
    }

    private fun getRegion() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {

                        regionList.clear()

                        // 認証成功
                        val prefArray = result.getJSONArray("Regions")

                        for (i in 0 until prefArray.length()) {
                            val row = prefArray.getJSONObject(i)
                            val region = Region(
                                    RegionID = row.getString("RegionID"),
                                    RegionName = row.getString("RegionName")
                            )

                            regionList.add(region)

                        }

                        getPrefs()

                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {

                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.Regions

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    private fun getPrefs() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        var regionName = ""
                        val prefArray = result.getJSONArray("Prefs")
                        prefList.clear()

                        for (i in 0 until prefArray.length()) {
                            val row = prefArray.getJSONObject(i)
                            val pref = Pref(
                                    PrefID = row.getString("PrefID"),
                                    PrefKana = row.getString("PrefKana"),
                                    PrefName = row.getString("PrefName"),
                                    RegionID = row.getString("RegionID"),
                                    RegionName = "",
                                    IsHeader = false
                            )

                            // 新しいRegionIDの場合はヘッダーを設定したうえで、インデックス位置を保持する。
                            if (regionIndexMap[pref.RegionID] == null) {
                                regionIndexMap[pref.RegionID] = i

                                for (region in regionList) {
                                    if (pref.RegionID == region.RegionID) {
                                        val prefRegionHeader = Pref(PrefID = "", PrefKana = "", PrefName = "", RegionID = pref.RegionID, RegionName = region.RegionName, IsHeader = true)
                                        prefList.add(prefRegionHeader)
                                    }
                                }
                            }

                            prefList.add(pref)
                        }

                        setPrefListView()
                        setSideIndex()

                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {

                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.RegionID = ""

        // RegionIDは空文字で送信し、全量取得対象とする.

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.Prefs

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

}