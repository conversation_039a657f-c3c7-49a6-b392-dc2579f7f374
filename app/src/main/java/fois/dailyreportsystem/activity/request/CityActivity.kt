package fois.dailyreportsystem.activity.request

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.AdapterView
import android.widget.TextView
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.*
import fois.dailyreportsystem.data.adapter.CityAdapter
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.pref_city_list_layout.*
import org.json.JSONObject

class CityActivity : BaseActivity() {

    private var cityList: ArrayList<City> = ArrayList<City>()

    private var indexMap: MutableMap<String, Int> = HashMap<String, Int>()

    private var prefId = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        setContentView(R.layout.pref_city_list_layout)
        super.onCreate(savedInstanceState)

        prefId = intent.getStringExtra("PREF_ID").toString()
        val prefName = intent.getStringExtra("PREF_NAME")

        back_arrow.setOnSafeClickListener { backPref() }
        close_arrow.visibility = View.GONE

        toolbar_title.text = prefName
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }
        getRegion()
    }

    // 端末の戻るボタン
    override fun onBackPressed() {
        backPref()
    }

    /**
     *  backPref 都道府県へ戻る
     *
     */
    private fun backPref() {
        val intent = Intent(this, PrefActivity::class.java)
        startActivity(intent)
        overridePendingTransition(R.anim.slide_out_right, R.anim.slide_in_left)
        finish()
    }

    /**
     *  setCityListView 市町村リストを設定
     *
     */
    private fun setCityListView() {
        cityList.sortBy { city: City -> city.CityKana }

        val itemAdapter = CityAdapter(this, cityList)
        pref_city_name.adapter = itemAdapter

        //Click item event
        pref_city_name.onItemClickListener = clickItem
    }

    /**
     *  setSideIndex IndexViewを作成
     *
     */
    private fun setSideIndex() {

        val sortMap = indexMap.toSortedMap()

        val inflater = this.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        for (cityNameHead in sortMap.keys) {
            // TextViewを生成し、side viewにインデックスとして追加する。
            val textView = inflater.inflate(R.layout.side_index_item, null) as TextView
            textView.text = cityNameHead
            textView.setOnSafeClickListener{
                val headName = it.text.toString()
                pref_city_name.setSelection(indexMap[headName]!!)
            }
            index_name.addView(textView)
        }

        index_name.refreshDrawableState()
    }


    /**
     *  pref item click
     *
     */
    private val clickItem = AdapterView.OnItemClickListener { _, _, position, _ ->
        val cityData = pref_city_name.getItemAtPosition(position) as City
        context!!.saveCity(cityData)
        finish()
        overridePendingTransition(R.anim.fade_in, R.anim.slide_out_down)
    }

    private fun getRegion() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {


                        // 認証成功
                        val cityArray = result.getJSONArray("Citys")

                        for (i in 0 until cityArray.length()) {
                            var jsonObject = cityArray.getJSONObject(i)
                            var city = City(
                                    AreaID = jsonObject.getString("AreaID"),
                                    CityCode = jsonObject.getString("CityCode"),
                                    CityKana = jsonObject.getString("CityKana"),
                                    CityName = jsonObject.getString("CityName"),
                                    PrefID = jsonObject.getString("PrefID"),
                                    PrefKana = jsonObject.getString("PrefKana"),
                                    PrefName = jsonObject.getString("PrefName"),
                                    RegionID = jsonObject.getString("RegionID"),
                                    RegionName = jsonObject.getString("RegionName"))

                            val cityKanaHead = city.CityKana.substring(0, 1)
                            if (indexMap[cityKanaHead] == null) {
                                indexMap[cityKanaHead] = i
                            }

                            cityList.add(city)
                        }

                        setSideIndex()
                        setCityListView()

                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {

                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.PrefID = prefId

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.Citys

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }


}