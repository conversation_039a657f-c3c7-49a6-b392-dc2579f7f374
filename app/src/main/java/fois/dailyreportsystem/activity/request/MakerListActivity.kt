package fois.dailyreportsystem.activity.request

import android.content.Intent
import android.os.Bundle
import android.widget.AdapterView
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.adapter.OrderResearchPersonListAdapter
import fois.dailyreportsystem.data.ResearchTaskMakers
import fois.dailyreportsystem.data.Schedule
import fois.dailyreportsystem.data.adapter.MakersAdapter
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.maker_list.*
import org.json.JSONObject

class MakerListActivity : BaseActivity() {

    var researchTaskMakers = ArrayList<ResearchTaskMakers>()

    var schedule: Schedule? = null

    /* -------------------- ライフサイクル -------------------- */
    /*
    起動：onCreate→onResume
    スリープから復帰、他画面からの復帰：onRestart→onResume
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.maker_list)

        back_arrow.setOnSafeClickListener { onBackPressed() }

        log(context?.javaClass!!.name, "onCreate")
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }
        getResearchList()

        log(context?.javaClass!!.name, "onResume")
    }

    // スリープからの復帰、他画面からの復帰
    override fun onRestart() {
        super.onRestart()
        // リストビューの位置を取得する
        log(context?.javaClass!!.name, "onRestart")
    }

    override fun onPause() {
        super.onPause()

        log(context?.javaClass!!.name, "onPause")
    }


    /* -------------------- 画面遷移 -------------------- */

    /* -------------------- リストビューイベント -------------------- */
    private fun setListView() {
        val listAdapter = MakersAdapter(this, researchTaskMakers)

        research_maker_list.adapter = listAdapter
        research_maker_list.onItemClickListener = itemClick
    }

    private val itemClick = AdapterView.OnItemClickListener { adapter, _, position, _ ->
        val userData = research_maker_list.adapter.getItem(position) as ResearchTaskMakers
        val intent = Intent(context, MakerUserListActivity::class.java)
        this.settings.put("EDIT_RESEARCH_TASK_COMPANY_ID", userData.CompanyID)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)

    }

    // 端末の戻るボタン
    override fun onBackPressed() {

        var intent = Intent()
        var test = this.settings.getString("RESEARCH_BEFORE_ACTIVITY")
        when( this.settings.getString("RESEARCH_BEFORE_ACTIVITY") ){
            "OrderRegistrationActivity" -> {
                intent = Intent(context, OrderRegistrationActivity::class.java)
            }
            "OrderEditActivity" -> {
                intent = Intent(context, OrderEditActivity::class.java)
            }
        }
        this.settings.remove("RESEARCH_BEFORE_ACTIVITY")
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
    }

    /* -------------------- 通信処理 -------------------- */

    /**
     * getResearchList 調査者一覧取得.
     *
     * */
    private fun getResearchList() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        researchTaskMakers.clear()
                        val array = result.getJSONArray("ResearchTaskMakers")

                        for (i in 0 until array.length()) {
                            val row = array.getJSONObject(i)
                            val user = ResearchTaskMakers(
                                    CompanyID = row.getString("CompanyID"),
                                    CompanyName = row.getString("CompanyName"),
                                    CompanyNumber = row.getString("CompanyNumber")
                            )
                            researchTaskMakers.add(user)
                        }
                        setListView()
                        log("result", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
                hideHub()
            }
        })

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.OfficeID = this.settings.getString("OFFICE_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskMakers

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

}

