package fois.dailyreportsystem.activity.request

import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.Window
import android.widget.AdapterView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.adapter.OrderResearchPersonListAdapter
import fois.dailyreportsystem.data.ResearchTask.Companion.setResearchTask
import fois.dailyreportsystem.data.ResearchTaskUser
import fois.dailyreportsystem.data.Schedule
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.research_person_list.*
import org.json.JSONObject

class OrderResearchPersonListActivity : BaseActivity() {

    var researchUsers = ArrayList<ResearchTaskUser>()

    var schedule: Schedule? = null

    /* -------------------- ライフサイクル -------------------- */
    /*
    起動：onCreate→onResume
    スリープから復帰、他画面からの復帰：onRestart→onResume
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.research_person_list)

        back_arrow.setOnSafeClickListener { onBackPressed() }

        log(context?.javaClass!!.name, "onCreate")
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        getResearch()

        log(context?.javaClass!!.name, "onResume")
    }

    // スリープからの復帰、他画面からの復帰
    override fun onRestart() {
        super.onRestart()
        // リストビューの位置を取得する
        log(context?.javaClass!!.name, "onRestart")
    }

    override fun onPause() {
        super.onPause()

        log(context?.javaClass!!.name, "onPause")
    }


    /* -------------------- 画面遷移 -------------------- */

    /* -------------------- リストビューイベント -------------------- */
    private fun setListView() {
        val listAdapter = OrderResearchPersonListAdapter(this, researchUsers, schedule!!)

        research_person_list.adapter = listAdapter
        research_person_list.onItemClickListener = itemClick
    }

    private val itemClick = AdapterView.OnItemClickListener { adapter, _, position, _ ->
        val userData = research_person_list.adapter.getItem(position) as ResearchTaskUser

        // 選択したユーザーと現ユーザが同じ場合は何もしない
        if (userData.UserID == schedule!!.userID) {
            return@OnItemClickListener
        }

        // 現在調査ユーザがいなければ登録する。
        if (schedule!!.userID == "") {
            openDialog(userData)
            return@OnItemClickListener
        }

        // 上記以外は更新
        openDialog(userData, updateFlg = true)
    }

    /**
     *  openDialog ダイアログ表示処理
     *
     *  @param userData 調査するユーザーオブジェクト
     *  @param updateFlg 更新フラグ
     *
     * */
    private fun openDialog(userData: ResearchTaskUser, updateFlg :Boolean = false) {

        var alertDialog: AlertDialog? = null
        val alertBuilder = AlertDialog.Builder(this, R.style.Theme_AppCompat_Dialog_MinWidth)
        val mAlertLayout = this.layoutInflater.inflate(R.layout.confirm_popup_window, null)

        val titleText = mAlertLayout.findViewById<TextView>(R.id.title_text)
        val detailText = mAlertLayout.findViewById<TextView>(R.id.detail_text)
        val cancelText = mAlertLayout.findViewById<TextView>(R.id.cancel)
        val positiveText = mAlertLayout.findViewById<TextView>(R.id.positive)

        if (updateFlg) {
            titleText.text = resources.getString(R.string.confirm_research_person_change_dialog_title)
        } else {
            titleText.text = resources.getString(R.string.confirm_research_person_choice_dialog_title)
        }
        detailText.text = resources.getString(R.string.confirm_research, schedule!!.researchTask!!.TaskName, userData.UserName)

        positiveText.setOnSafeClickListener {

            if (updateFlg) {
                // 現在の調査ユーザーを取り消し後、再度選択したユーザで登録する。
                deleteResearchTaskUser(userData.UserID, schedule!!.userID)
            } else {
                addResearchUser(userData.UserID)
            }

            if (alertDialog!!.isShowing) {
                alertDialog!!.dismiss()
            }
        }

        cancelText.setOnClickListener {
            if (alertDialog!!.isShowing) {
                alertDialog!!.dismiss()
            }
        }

        alertBuilder.setView(mAlertLayout)
        alertDialog = alertBuilder.create()

        alertDialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        val dialogLayoutParam = alertDialog.window!!.attributes

        dialogLayoutParam.gravity = Gravity.CENTER
        alertDialog.window!!.attributes = dialogLayoutParam
        alertDialog.window!!.setLayout(resources.displayMetrics.widthPixels, LinearLayout.LayoutParams.WRAP_CONTENT)

        alertDialog.show()
    }

    // 端末の戻るボタン
    override fun onBackPressed() {
        val intent = Intent(context, OrderDetailActivity::class.java)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
    }

    /* -------------------- 通信処理 -------------------- */

    /**
     * getResearchList 調査者一覧取得.
     *
     * */
    private fun getResearchList() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        researchUsers.clear()


                        val taskArray = result.getJSONArray("ResearchTaskUsers")

                        for (i in 0 until taskArray.length()) {
                            val row = taskArray.getJSONObject(i)

                            val user = ResearchTaskUser(
                                    CompanyID = row.getString("CompanyID"),
                                    CompanyName = row.getString("CompanyName"),
                                    CompanyNumber = row.getString("CompanyNumber"),
                                    CompanyRole = row.getString("CompanyRole"),
                                    FlgCandidacy = row.getString("FlgCandidacy"),
                                    FlgResearch = row.getString("FlgResearch"),
                                    FlgStaff = row.getString("FlgStaff"),
                                    OfficeID = row.getString("OfficeID"),
                                    RequestDate = row.getString("RequestDate"),
                                    UserCode = row.getString("UserCode"),
                                    UserID = row.getString("UserID"),
                                    UserName = row.getString("UserName"),
                                    UserPhoneMail = row.getString("UserPhoneMail"),
                                    UserPhoneNumber = row.getString("UserPhoneNumber"))

                            researchUsers.add(user)
                        }

                        setListView()

                        log("result", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
                hideHub()
            }
        })

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ResearchTaskID = this.settings.getString("RESEARCH_TASK_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskUsers

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * addResearchUser 調査員登録.
     *
     * */
    private fun addResearchUser(selectUserId: String?) {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        log("result", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()

                    // 前画面へ遷移する.
                    onBackPressed()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
                hideHub()
            }
        })

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ResearchTaskID = this.settings.getString("RESEARCH_TASK_ID")
        params.TargetUserID = selectUserId

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskUserChoice

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * deleteResearchTaskUser 調査員更新.
     *
     * */
    private fun deleteResearchTaskUser(selectUserId: String?, deleteUserId : String?) {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")

                    if (status.getInt("StatusCode") == 0) {
                        // 追加のユーザー処理を実行する。
                        addResearchUser(selectUserId)

                        log("result", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
                hideHub()
            }
        })

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ResearchTaskID = this.settings.getString("RESEARCH_TASK_ID")
        params.TargetUserID = deleteUserId

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskUserDelete

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * getResearch 調査依頼情報取得.
     *
     * */
    private fun getResearch() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val taskArray = result.getJSONArray("Schedules")

                        for (i in 0 until taskArray.length()) {
                            val row = taskArray.getJSONObject(i)

                            val researchTask = setResearchTask(row.getJSONObject("ResearchTask"))

                            schedule = Schedule(allDay = row.getString("AllDay"),
                                    endDateTime = row.getString("EndDateTime"),
                                    endRepetition = row.getString("EndRepetition"),
                                    latitude = row.getString("Latitude"),
                                    location = row.getString("Location"),
                                    longitude = row.getString("Longitude"),
                                    remarks = row.getString("Remarks"),
                                    repetitionID = row.getString("Remarks"),
                                    repetitionName = row.getString("RepetitionName"),
                                    researchTask = researchTask,
                                    researchTaskID = row.getString("ResearchTaskID"),
                                    scheduleID = row.getString("ScheduleID"),
                                    startDateTime = row.getString("StartDateTime"),
                                    surveyStatusID = row.getString("SurveyStatusID"),
                                    surveyStatusName = row.getString("SurveyStatusName"),
                                    title = row.getString("Title"),
                                    travelTime = row.getString("TravelTime"),
                                    userID = row.getString("UserID"),
                                    userName = row.getString("UserName"))

                        }

                        // 完了後にユーザー一覧呼び出し
                        getResearchList()

                        log("result", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub()}
        })

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ResearchTaskID = this.settings.getString("RESEARCH_TASK_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTask

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

}

