package fois.dailyreportsystem.activity.request

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.MotionEvent
import android.view.View
import android.widget.Toast
import androidx.core.content.ContextCompat
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.*
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.order_registration_layout.*
import org.json.JSONObject
import java.util.*


class OrderRegistrationActivity : BaseActivity() {

    var researchTask: ResearchTask = ResearchTask.init()
    var requestSearchDay1: String? = ""
    var role: String = ""

    /* -------------------- ライフサイクル -------------------- */
    /*
    起動：onCreate→onResume
    スリープから復帰、他画面からの復帰：onRestart→onResume
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.order_registration_layout)

        initData()
        validation()
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }
        // 一時保存データがあれば処理
        researchTask = context?.loadResearchTask() ?: return

        // 一時保存データを入れこむ
        task_name.setText(researchTask.TaskName)
        address.setText(researchTask.TaskAddress)
        if (researchTask.PreferredDate2.isNotBlank()) {
            research_date2.text = researchTask.PreferredDate2.toDate("yyyy-MM-dd HH:mm")?.toString("yyyy/MM/dd(E) HH:mm")
        }
        if( researchTask.PreferredDate3.isNotBlank()) {
            research_date3.text = researchTask.PreferredDate3.toDate("yyyy-MM-dd HH:mm")?.toString("yyyy/MM/dd(E) HH:mm")
        }
        remarks.setText(researchTask.Remarks)

        initDesign()
        // 一時保存データを削除
        context?.removeResearchTask()

    }

    // スリープからの復帰、他画面からの復帰
    override fun onRestart() {
        super.onRestart()
        // リストビューの位置を取得する
        log(context?.javaClass!!.name, "onRestart")
    }

    override fun onPause() {
        super.onPause()

        log(context?.javaClass!!.name, "onPause")
    }


    // 画面タッチでキーボードを閉じる
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        hideKeyboard(order_registration_layout)
        return super.dispatchTouchEvent(ev)
    }

    /**
     * 初期設定
     */
    private fun initData(){

        // キャンセルボタン
        menu_cancel.setOnSafeClickListener { onBackPressed() }
        val city = context!!.loadCity() ?: return

        role = this.settings.getString("COMPANY_ROLE")

        requestSearchDay1 = this.settings.getString("SELECT_REQUEST_DAY")
        if(requestSearchDay1 == ""){ onBackPressed() }

        researchTask.AreaID = city.AreaID
        researchTask.CityCode = city.CityCode
        researchTask.CityName = city.CityName
        researchTask.MakerID = if (role == Constant.MAKER){this.settings.getString("COMPANY_ID")}else{""}
        researchTask.MakerName = if (role == Constant.MAKER){this.settings.getString("COMPANY_NAME")}else{""}
        researchTask.MakerUserID= if (role == Constant.MAKER){this.settings.getString("USER_ID")}else{""}
        researchTask.MakerUserName= if (role == Constant.MAKER){this.settings.getString("USER_NAME")}else{""}
        researchTask.OfficeID= this.settings.getString("OFFICE_ID")
        researchTask.PrefID= city.PrefID
        researchTask.PrefName= city.PrefName
        researchTask.PreferredDate1= requestSearchDay1!!.toDate("yyyy/MM/dd(E) HH:mm")!!.toString("yyyy-MM-dd HH:mm")

        confirm_header.setOnSafeClickListener { addResearchTask() }
        confirm_button.setOnSafeClickListener { addResearchTask() }

        task_name.addTextChangedListener(object : TextWatcher{
            override fun afterTextChanged(p0: Editable?) { validation() }
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) { validation() }
            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) { validation() }
        })
        //  エリア名設定
        area_name.text = researchTask.PrefName + " " + researchTask.CityName

        research_date1.text = requestSearchDay1

        val selectedDay = requestSearchDay1!!.toDate("yyyy/MM/dd(E) HH:mm")
        if(selectedDay != null){
            research_date2.setOnSafeClickListener { DateDialogUtil.defaultDate(selectedDay).showDateDialog(context!!, research_date2) }
            research_date3.setOnSafeClickListener { DateDialogUtil.defaultDate(selectedDay).showDateDialog(context!!, research_date3) }
        }

        initDesign()
    }

    private fun initDesign(){

        // 販売店：自分で登録
        if (role == Constant.MAKER){
            maker_user_layout.visibility = View.GONE
        }

        // フジケミ：販売店を選択
        else if(role == Constant.FCT){
            maker_user_layout.visibility = View.VISIBLE

            maker_user_column.setOnSafeClickListener { forwardMakerList() }

            // 販売店未選択
            if (researchTask.MakerUserID.isBlank()){
                maker_name_layout.visibility = View.GONE
                maker_honor.visibility = View.GONE
                do_maker_staff_list.visibility = View.VISIBLE

            }
            // 販売店選択済
            else{
                maker_name_layout.visibility = View.VISIBLE
                maker_honor.visibility = View.VISIBLE
                do_maker_staff_list.visibility = View.GONE
                maker_company_name.text = researchTask.MakerName
                maker_user_name.text = researchTask.MakerUserName
            }
        }
    }

    /* -------------------- 入力確認 -------------------- */
    // OK: true NG: false
    private fun validation(): Boolean{
        var check: Boolean = true
        when {
            task_name.text.isBlank() -> {
                check = false
            }
            role != Constant.MAKER && researchTask.MakerID.isBlank() ->{
                check = false
            }
        }
        // デザイン変更
        if(check) {
            confirm_button.isEnabled = true
            confirm_button.setBackgroundResource(R.drawable.radius_frame_blue)
            confirm_header.setTextColor(ContextCompat.getColor(this, R.color.white))
        }else{
            confirm_button.isEnabled = false
            confirm_button.setBackgroundResource(R.drawable.radius_frame_gray)
            confirm_header.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
        }
        return check
    }

    /* -------------------- 画面遷移 -------------------- */


    // 端末の戻るボタン
    override fun onBackPressed() {
        finish()
    }

    // 販売店一覧ヘ遷移
    private fun forwardMakerList() {
        val intent = Intent(context, MakerListActivity::class.java)

        val activityName = localClassName.substring(localClassName.lastIndexOf(".")+1)
        this.settings.put("RESEARCH_BEFORE_ACTIVITY", activityName)

        researchTask.TaskName = task_name.text.toString()
        researchTask.TaskAddress = address.text.toString()
        researchTask.PreferredDate2 = if(research_date2.text.isNotBlank()){ research_date2.text.toString().toDate("yyyy/MM/dd(E) HH:mm")!!.toString("yyyy-MM-dd HH:mm") }else{ ""}
        researchTask.PreferredDate3 = if(research_date3.text.isNotBlank()){ research_date3.text.toString().toDate("yyyy/MM/dd(E) HH:mm")!!.toString("yyyy-MM-dd HH:mm") }else{ ""}
        researchTask.Remarks = remarks.text.toString()

        context?.saveResearchTask(researchTask)

        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
    }

    /* -------------------- 通信処理 -------------------- */

    /**
     * orderRegistration 調査依頼登録.
     *
     * */
    private fun addResearchTask() {
        if (!validation()) return

        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        log("result", "ok")
                        onBackPressed()
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    Toast.makeText(context, context!!.getString(R.string.no_selected_area), Toast.LENGTH_LONG).show()
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        researchTask.TaskName = task_name.text.toString()
        researchTask.TaskAddress = address.text.toString()
        researchTask.PreferredDate2 = if(research_date2.text.isNotBlank()){ research_date2.text.toString().toDate("yyyy/MM/dd(E) HH:mm")!!.toString("yyyy-MM-dd HH:mm") }else{ ""}
        researchTask.PreferredDate3 = if(research_date3.text.isNotBlank()){ research_date3.text.toString().toDate("yyyy/MM/dd(E) HH:mm")!!.toString("yyyy-MM-dd HH:mm") }else{ ""}
        researchTask.Remarks = remarks.text.toString()
        val researchTask = researchTask.getBaseAddData()

        val list = ArrayList<MutableMap<String, String>>()
        list.add(researchTask)

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.JsonData = Gson().toJson(list)

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskAdd

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}
