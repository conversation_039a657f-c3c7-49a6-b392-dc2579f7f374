package fois.dailyreportsystem.activity.request

import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.Window
import android.widget.LinearLayout.LayoutParams
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.AreaData
import fois.dailyreportsystem.data.Schedule
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.order_detail_layout.*
import org.json.JSONObject
import kotlin.Boolean

class OrderDetailActivity : BaseActivity() {

    private var countHub = 0

    var schedule: Schedule? = null
    var areas: ArrayList<AreaData>? = null
    private var isIncluded: Boolean = false

    /* -------------------- ライフサイクル -------------------- */
    /*
    起動：onCreate→onResume
    スリープから復帰、他画面からの復帰：onRestart→onResume
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.order_detail_layout)

        this.settings.put("RESEARCH_NAME", "")

        if(intent.getStringExtra("NotificationCode") != null){



        }

        hideMenu()

        no_choice.setDrawable(ContextCompat.getDrawable(context!!, R.drawable.ripple_white))
        no_choice.setOnSafeClickListener { forwardSearchPersonList() }

        order_edit.setOnSafeClickListener { forwardOrderEdit() }

        readjustment.setOnSafeClickListener { it -> openDialog(it.id) }

        cancel_text.setOnSafeClickListener { it -> openDialog(it.id) }

        footer.setOnSafeClickListener { it -> openDialog(it.id) }

        back_title.setOnSafeClickListener { onBackPressed() }


        log(context?.javaClass!!.name, "onCreate")
    }

    override fun onResume() {
        super.onResume()
        getSchedule()
        getUserAreas()
        getResearchTaskUserIsIncluded()
        log(context?.javaClass!!.name, "onResume")
    }

    // スリープからの復帰、他画面からの復帰
    override fun onRestart() {
        super.onRestart()
        // リストビューの位置を取得する
        log(context?.javaClass!!.name, "onRestart")
    }

    override fun onPause() {
        super.onPause()

        log(context?.javaClass!!.name, "onPause")
    }


    /* -------------------- 画面遷移 -------------------- */


    // 端末の戻るボタン
    override fun onBackPressed() {
        finish()
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
    }

    /**
     * hideMenu 権限で項目を変更する
     */
    private fun hideMenu(){
        if (this.settings.getString("COMPANY_ROLE") == Constant.MAKER) {
            survey_info_layout.visibility = View.GONE
        }else if (this.settings.getString("COMPANY_ROLE") == Constant.FCT){
            survey_info_layout.visibility = View.VISIBLE
        }
    }

    /**
     * setupItem 画面セットアップ.
     *
     * */
    private fun setupItem() {
        if (schedule == null) return
        val researchTask = schedule!!.researchTask ?: return

        // 調査者情報
        if (schedule!!.userID.isNullOrBlank()){
            survey_user_title.visibility = View.GONE
            survey_user_name.visibility = View.GONE
            do_survey_staff_list.visibility = View.VISIBLE
        }else{
            survey_user_title.visibility = View.VISIBLE
            survey_user_name.visibility = View.VISIBLE
            survey_user_name.text = schedule!!.userName
            do_survey_staff_list.visibility = View.GONE
        }

        survey_status.text = schedule!!.surveyStatusName
        survey_status.setBackgroundColor(ColorUtil.getSurveyStatusColor(schedule!!.surveyStatusID))

        order_company.text = researchTask.MakerName
        order_staff.text = researchTask.MakerUserName

        task_name.text = researchTask.TaskName
        address.text = researchTask.PrefName + " " +researchTask.CityName + " " + researchTask.TaskAddress

        if (!researchTask.PreferredDate1.isBlank()) {
            val date = researchTask.PreferredDate1.toDate()
            if (date != null) research_date1.text = date.toString("yyyy/MM/dd(E) HH:mm")
        }
        if (!researchTask.PreferredDate2.isBlank()) {
            val date = researchTask.PreferredDate2.toDate()
            if (date != null) research_date2.text = date.toString("yyyy/MM/dd(E) HH:mm")
        }
        if (!researchTask.PreferredDate3.isBlank()) {
            val date = researchTask.PreferredDate3.toDate()
            if (date != null) research_date3.text = date.toString("yyyy/MM/dd(E) HH:mm")
        }

        remarks.text = schedule!!.remarks


        log(context?.javaClass!!.name, "setupItem")
    }


    /**
     * setupMenu メニューの表示.
     *
     * */
    private fun setupMenu(){
        if (countHub > 0) return
        if (schedule == null) return
        val researchTask = schedule!!.researchTask ?: return
        if (areas == null) return

        // 販売店
        if (this.settings.getString("COMPANY_ROLE") == Constant.MAKER) {
            // 8～4：キャンセル・編集可　承認前まで
            if (listOf("8", "7", "6", "5", "4").find { it == schedule!!.surveyStatusID } != null) {
                cancel_layout.visibility = View.VISIBLE
                order_edit.visibility = View.VISIBLE
            }else{
                cancel_layout.visibility = View.GONE
                order_edit.visibility = View.GONE
            }
            // 8～3：再調整可　調査前まで
            if (listOf("8", "7", "6", "5", "4", "3").find { it == schedule!!.surveyStatusID } != null) {
                readjustment_layout.visibility = View.VISIBLE
            }else{
                readjustment_layout.visibility = View.GONE
            }
        }
        // フジケミ
        else if (this.settings.getString("COMPANY_ROLE") == Constant.FCT){
            if(this.settings.getString("FLAG_STAFF") == "1"){
                cancel_layout.visibility = View.VISIBLE
                order_edit.visibility = View.VISIBLE
                // 報告書を作成するまで再調整可
                if (listOf("8", "7", "6", "5", "4", "3", "2").find { it == schedule!!.surveyStatusID } != null) {
                    readjustment_layout.visibility = View.VISIBLE
                }else{
                    readjustment_layout.visibility = View.GONE
                }
            }else{
                cancel_layout.visibility = View.GONE
                order_edit.visibility = View.GONE
                readjustment_layout.visibility = View.GONE
            }
        }
        // 7～4：決定するまで行けます
        if (this.settings.getString("FLAG_RESEARCH") == "1" && schedule!!.userID.isNullOrBlank() && isAreaTask()){
            if (listOf("7", "6", "5", "4").find { it == schedule!!.surveyStatusID } != null) {
                confirm_layout.visibility = View.VISIBLE
            }else{
                confirm_layout.visibility = View.GONE
            }
        }else{
            confirm_layout.visibility = View.GONE
        }

        hideHub()
    }

    /**
     * 現地調査対応可能デザイン変更
     */
    private fun isIncluded(included: Boolean) {
        isIncluded = included
        if ( included ) {
            confirm_do_research.text = context!!.getString(R.string.research_order_cancel)
            confirm_do_research.setBackgroundResource(R.drawable.radius_frame_red)
        }else{
            confirm_do_research.text = context!!.getString(R.string.order_confirm_do_research)
            confirm_do_research.setBackgroundResource(R.drawable.radius_frame_blue)
        }
    }

    /**
     * ログインユーザがAreaIDの担当をしているかどうか
     */
    private fun isAreaTask(): Boolean {
        if(schedule == null)  return false
        if(areas == null)  return false
        if(areas!!.count() == 0) return false
        if(areas!!.find { it.areaID == schedule!!.researchTask!!.AreaID } == null) return false
        return true
    }



    /*------------------ 画面遷移処理 ------------------*/

    private fun forwardSearchPersonList() {
        if(context!!.settings.getString("RESEARCH_TASK_ID") == "") {
            return
        }
        val intent = Intent(context, OrderResearchPersonListActivity::class.java)
        this.settings.put("RESEARCH_NAME", schedule!!.researchTask.TaskName)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
    }


    private fun forwardOrderEdit() {
        context?.removeResearchTask()
        val intent = Intent(context, OrderEditActivity::class.java)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_up, R.anim.fade_out)
    }

    /*------------------ 各イベント処理 ------------------*/

    /**
     *  openDialog ダイアログ表示処理
     * */
    private fun openDialog(clickId: Int) {
        var alertDialog: AlertDialog? = null
        val alertBuilder = AlertDialog.Builder(this, R.style.MyDialog)
        val mAlertLayout = this.layoutInflater.inflate(R.layout.guide_popup_window, null)

        val actionTextView = mAlertLayout.findViewById<TextView>(R.id.action_text)
        val cancelTextView = mAlertLayout.findViewById<TextView>(R.id.popup_cancel)
        cancelTextView.setOnSafeClickListener {
            if (alertDialog!!.isShowing) alertDialog!!.dismiss()
        }

        when (clickId) {
            footer.id -> {
                // 登録状況により表示する名称を変更する。
                if (isIncluded) {
                    actionTextView.text = this.resources.getString(R.string.research_order_cancel)
                    actionTextView.setTextColor(ContextCompat.getColor(this, R.color.red))
                    actionTextView.setOnSafeClickListener {
                        deleteResearchTaskUser()
                        if (alertDialog!!.isShowing) alertDialog!!.dismiss()
                    }
                } else {
                    actionTextView.text = this.resources.getString(R.string.order_confirm_do_research)
                    actionTextView.setTextColor(ContextCompat.getColor(this, R.color.light_blue))
                    actionTextView.setOnSafeClickListener {
                        addResearchTaskUser()
                        if (alertDialog!!.isShowing) alertDialog!!.dismiss()
                    }
                }
            }
            readjustment.id -> {
                actionTextView.text = this.resources.getString(R.string.order_readjustment)
                actionTextView.setTextColor(ContextCompat.getColor(this, R.color.light_blue))
                actionTextView.setOnSafeClickListener {
                    rescheduleResearchTask()
                    if (alertDialog!!.isShowing) alertDialog!!.dismiss()
                }
            }
            cancel_text.id -> {
                actionTextView.text = this.resources.getString(R.string.order_cancel)
                actionTextView.setTextColor(ContextCompat.getColor(this, R.color.red))
                actionTextView.setOnSafeClickListener {
                    deleteCalenderSchedule()
                    if (alertDialog!!.isShowing) alertDialog!!.dismiss()
                }
            }
        }

        alertBuilder.setView(mAlertLayout)
        alertDialog = alertBuilder.create()

        alertDialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        val dialogLayoutParam = alertDialog.window!!.attributes

        dialogLayoutParam.gravity = Gravity.BOTTOM
        alertDialog.window!!.attributes = dialogLayoutParam
        alertDialog.window!!.setLayout(resources.displayMetrics.widthPixels, LayoutParams.WRAP_CONTENT)

        alertDialog.show()
    }

    /* -------------------- 通信処理 -------------------- */

    /**
     * getResearchTask 調査依頼情報取得.
     *
     * */
    private fun getSchedule() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) return
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val scheduleArrayList = result.getJSONArray("Schedules")
                        val array = JsonHandling.toScheduleArrayList(scheduleArrayList)
                        if (array != null && array.count() > 0){
                            schedule = array[0]
                            context!!.settings.put("RESEARCH_TASK_ID", schedule!!.researchTask.ResearchTaskID)
                        }
                        setupItem()
                        log("result", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                }
                setupMenu()
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() { hideHub()}
        })
        val params = SendParameter()
        params.UserID = context!!.settings.getString("USER_ID")
        params.ScheduleID = context!!.settings.getString("SCHEDULE_ID")
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.CalenderSchedule
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * getUserAreas エリアユーザ一覧情報取得.
     *
     * */
    private fun getUserAreas() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) return
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val jsonArray = result.getJSONArray("Areas")
                        areas = AreaData.toArrayList(jsonArray)
                        log("getUserAreas", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                }
                setupMenu()
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() { hideHub()}
        })
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.OfficeID = this.settings.getString("OFFICE_ID")
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.UserAreas
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * addResearchTaskUser 対応可能追加.
     *
     * */
    private fun addResearchTaskUser() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) return
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        showAlertDialog(context!!.getString(R.string.research_order_do_result),"","OK",
                                DialogInterface.OnClickListener { _, _ ->
                                    isIncluded(true)
                                })
                        log("addResearchTaskUser", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                }
                setupMenu()
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() {}
        })
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ResearchTaskID = this.settings.getString("RESEARCH_TASK_ID")
        params.TargetUserID = this.settings.getString("USER_ID")
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskUserAdd
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        log(context?.javaClass!!.name, url + jsonParams)
    }


    /**
     * addResearchTaskUser 対応可能削除.
     *
     * */
    private fun deleteResearchTaskUser() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) return
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        showAlertDialog(context!!.getString(R.string.research_order_cancel_result),"","OK",
                                DialogInterface.OnClickListener { _, _ ->
                                    isIncluded(false)
                                })
                        log("addResearchTaskUser", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                }
                setupMenu()
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() { hideHub()}
        })
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ResearchTaskID = this.settings.getString("RESEARCH_TASK_ID")
        params.TargetUserID = this.settings.getString("USER_ID")
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskUserDelete
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        log(context?.javaClass!!.name, url + jsonParams)
    }



    /**
     * getResearchTaskUserIsIncluded 対応可能状況情報取得.
     * ResearchTaskUserIsIncluded: 1-通知済み 0-通知なし
     * */
    private fun getResearchTaskUserIsIncluded() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) return
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val result = result.getString("ResearchTaskUserIsIncluded")
                        isIncluded(result == "1")
                        log("getResearchTaskUserIsIncluded", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                }
                setupMenu()
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() { hideHub()}
        })
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ResearchTaskID = this.settings.getString("RESEARCH_TASK_ID")
        params.TargetUserID = this.settings.getString("USER_ID")
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskUserIsIncluded
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * deleteCalenderSchedule カレンダー情報削除.
     * 依頼状況削除
     * */
    private fun deleteCalenderSchedule() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) return
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 削除成功
                        onBackPressed()
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                }
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() { hideHub()}
        })
        val params = SendParameter()
        params.UserID = context!!.settings.getString("USER_ID")
        params.ScheduleID = schedule!!.scheduleID
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.CalenderDeleteScheduleURL
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * rescheduleResearchTask 再調整
     * ResearchTaskIDに該当する、現地調査物件を再調整へ変更します。
     * スケジュールに設定されたユーザー、開始日時、終了日時、現地調査希望日１～３が空の状態となります。
     * */
    private fun rescheduleResearchTask() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) return
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 再調整成功
                        onBackPressed()
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                }
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() { hideHub()}
        })
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ResearchTaskID = this.settings.getString("RESEARCH_TASK_ID")
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskReschedule
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        log(context?.javaClass!!.name, url + jsonParams)
    }
}
