package fois.dailyreportsystem.activity.request

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.MotionEvent
import android.view.View
import android.widget.Toast
import androidx.core.content.ContextCompat
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.DateDialogUtil
import fois.dailyreportsystem.data.ResearchTask
import fois.dailyreportsystem.data.Schedule
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.order_edit_layout.*
import kotlinx.android.synthetic.main.order_edit_layout.address
import kotlinx.android.synthetic.main.order_edit_layout.area_name
import kotlinx.android.synthetic.main.order_edit_layout.do_maker_staff_list
import kotlinx.android.synthetic.main.order_edit_layout.maker_company_name
import kotlinx.android.synthetic.main.order_edit_layout.maker_honor
import kotlinx.android.synthetic.main.order_edit_layout.maker_name_layout
import kotlinx.android.synthetic.main.order_edit_layout.maker_user_layout
import kotlinx.android.synthetic.main.order_edit_layout.maker_user_name
import kotlinx.android.synthetic.main.order_edit_layout.menu_cancel
import kotlinx.android.synthetic.main.order_edit_layout.remarks
import kotlinx.android.synthetic.main.order_edit_layout.research_date1
import kotlinx.android.synthetic.main.order_edit_layout.research_date2
import kotlinx.android.synthetic.main.order_edit_layout.research_date3
import kotlinx.android.synthetic.main.order_edit_layout.task_name
import org.json.JSONObject
import java.util.*

class OrderEditActivity : BaseActivity() {

    var schedule: Schedule? = Schedule.init()
    var researchTask = schedule?.researchTask ?: ResearchTask.init()

    var requestSearchDay1: String? = ""
    var dateUtil = DateUtil()
    var role: String = ""


    /* -------------------- ライフサイクル -------------------- */
    /*
    起動：onCreate→onResume
    スリープから復帰、他画面からの復帰：onRestart→onResume
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.order_edit_layout)

        // キャンセルボタン
        menu_cancel.setOnSafeClickListener { onBackPressed() }

        // 一時保存データがあれば処理
        val data = context?.loadResearchTask()
        if(data != null ) {
            researchTask = data
            setupItem()

            // 一時保存データを削除
            context?.removeResearchTask()

        }else {
            getResearch()
        }

        validation()

        log(context?.javaClass!!.name, "onCreate")
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }


        log(context?.javaClass!!.name, "onResume")
    }

    // スリープからの復帰、他画面からの復帰
    override fun onRestart() {
        super.onRestart()
        // リストビューの位置を取得する
        log(context?.javaClass!!.name, "onRestart")
    }

    override fun onPause() {
        super.onPause()

        log(context?.javaClass!!.name, "onPause")
    }


    // 画面タッチでキーボードを閉じる
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        hideKeyboard(order_edit_layout)
        return super.dispatchTouchEvent(ev)
    }

    /* -------------------- 入力確認 -------------------- */
    // OK: true NG: false
    private fun validation(): Boolean {
        var check: Boolean = true
        when {
            task_name.text.isBlank() -> {
                check = false
            }

        }
        // デザイン変更
        if (check) {
            footer_update.isEnabled = true
            footer_update.setBackgroundResource(R.drawable.radius_frame_blue)
            update_header.setTextColor(ContextCompat.getColor(this, R.color.white))
        } else {
            footer_update.isEnabled = false
            footer_update.setBackgroundResource(R.drawable.radius_frame_gray)
            update_header.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
        }
        return check
    }


    /**
     * setupItem 画面セットアップ.
     *
     * */
    private fun setupItem() {

        task_name.setText(researchTask.TaskName)

        area_name.text = researchTask.PrefName + researchTask.CityName
        address.setText(researchTask.TaskAddress)

        task_name.setText(researchTask.TaskName)
        address.setText(researchTask.TaskAddress)

        research_date1.text = dateUtil.stringDateToFormat(researchTask.PreferredDate1, "yyyy/MM/dd(E) HH:mm")

        if (researchTask.PreferredDate2 != "") {
            research_date2.text = researchTask.PreferredDate2.toDate()!!.toString("yyyy/MM/dd(E) HH:mm")
        }

        if (researchTask.PreferredDate3 != "") {
            research_date3.text = researchTask.PreferredDate3.toDate()!!.toString("yyyy/MM/dd(E) HH:mm")
        }

        remarks.setText(schedule!!.remarks)

        role = this.settings.getString("COMPANY_ROLE")

        update_header.setOnSafeClickListener { researchTaskModify() }
        footer_update.setOnSafeClickListener { researchTaskModify() }

        task_name.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(p0: Editable?) {  validation() }
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {  validation() }
            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {  validation() }
        })

        var requestSearchDay1 = researchTask!!.PreferredDate1

        research_date2.setOnSafeClickListener { DateDialogUtil.defaultDate(requestSearchDay1!!.toDate()!!).showDateDialog(context!!, research_date2) }
        research_date3.setOnSafeClickListener { DateDialogUtil.defaultDate(requestSearchDay1!!.toDate()!!).showDateDialog(context!!, research_date3) }

        initDesign()

        log(context?.javaClass!!.name, "setupItem")
    }

    private fun initDesign(){

        // 販売店：自分で登録
        if (role == Constant.MAKER) {
            maker_user_layout.visibility = View.GONE
        }
        // フジケミ：販売店を選択
        else if (role == Constant.FCT) {
            maker_user_layout.visibility = View.VISIBLE

            maker_user_column.setOnSafeClickListener { forwardMakerList() }

            // 販売店未選択
            if (researchTask.MakerUserID.isBlank()) {
                maker_name_layout.visibility = View.GONE
                maker_honor.visibility = View.GONE
                do_maker_staff_list.visibility = View.VISIBLE
            }
            // 販売店選択済
            else {
                maker_name_layout.visibility = View.VISIBLE
                maker_honor.visibility = View.VISIBLE
                do_maker_staff_list.visibility = View.GONE
                maker_company_name.text = researchTask.MakerName
                maker_user_name.text = researchTask.MakerUserName
            }
        }
    }

    /* -------------------- 画面遷移 -------------------- */


    // 端末の戻るボタン
    override fun onBackPressed() {
        val intent = Intent(context, OrderDetailActivity::class.java)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.fade_in, R.anim.slide_out_down)
    }

    // 販売店一覧ヘ遷移
    private fun forwardMakerList() {
        if(context!!.settings.getString("RESEARCH_TASK_ID") == "") {
            return
        }
        val intent = Intent(context, MakerListActivity::class.java)

        val activityName = localClassName.substring(localClassName.lastIndexOf(".")+1)
        this.settings.put("RESEARCH_BEFORE_ACTIVITY", activityName)

        researchTask.TaskName = task_name.text.toString()
        researchTask.TaskAddress = address.text.toString()
        researchTask.PreferredDate2 = if(research_date2.text.isNotBlank()){ research_date2.text.toString().toDate("yyyy/MM/dd(E) HH:mm")!!.toString("yyyy-MM-dd HH:mm") }else{ ""}
        researchTask.PreferredDate3 = if(research_date3.text.isNotBlank()){ research_date3.text.toString().toDate("yyyy/MM/dd(E) HH:mm")!!.toString("yyyy-MM-dd HH:mm") }else{ ""}
        researchTask.Remarks = remarks.text.toString()

        context?.saveResearchTask(researchTask)

        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
    }

    /* -------------------- 通信処理 -------------------- */

    /**
     * getResearch 調査依頼情報取得.
     *
     * */
    private fun getResearch() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {showHub()}

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val taskArray = result.getJSONArray("Schedules")

                        for (i in 0 until taskArray.length()) {
                            val row = taskArray.getJSONObject(i)
                            schedule = Schedule.setSchedule(row)
                        }

                        researchTask = schedule!!.researchTask
                        setupItem()

                        log("result", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                }finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {hideHub()}
        })

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ResearchTaskID = this.settings.getString("RESEARCH_TASK_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTask

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * researchTaskModify 調査依頼更新.
     *
     * */
    private fun researchTaskModify() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {

                        onBackPressed()

                        log("result", "ok")
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    Toast.makeText(context, context!!.getString(R.string.unexpected_error), Toast.LENGTH_LONG).show()
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        var researchDate2 = ""
        if (research_date2.text.toString() != "" ) {
            researchDate2 = research_date2.text.toString().toDate("yyyy/MM/dd(E) HH:mm")!!.toString(Constant.FORMAT_DATETIME_HYPHEN)
        }

        var researchDate3 = ""
        if (research_date3.text.toString() != "") {
            researchDate3 = research_date3.text.toString().toDate("yyyy/MM/dd(E) HH:mm")!!.toString(Constant.FORMAT_DATETIME_HYPHEN)
        }

        schedule!!.researchTask = researchTask
        schedule!!.researchTask.TaskName = task_name.text.toString()
        schedule!!.researchTask.TaskAddress = address.text.toString()
        schedule!!.researchTask.PreferredDate2 = researchDate2
        schedule!!.researchTask.PreferredDate3 = researchDate3

        // Schedule変更データの作成
        val scheduleModifyData = schedule!!.getBaseModifyData()
        val researchTask = schedule!!.researchTask!!.getBaseModifyData()

        scheduleModifyData["Remarks"] = remarks.text.toString()
        scheduleModifyData["ResearchTask"] = researchTask

        val list = ArrayList<MutableMap<String, Any?>>()
        list.add(scheduleModifyData)

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.JsonData = Gson().toJson(list)

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskModify

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}
