package fois.dailyreportsystem.activity.plan

import android.content.Context
import android.graphics.Paint
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.DateDialogUtil
import fois.dailyreportsystem.data.JsonData
import fois.dailyreportsystem.util.*
import fois.dailyreportsystem.util.Constant.travelTime
import kotlinx.android.synthetic.main.plan_registration_layout.*
import org.json.JSONObject
import java.util.*

class PlanRegistrationActivity : BaseActivity() {

    var beforeStartDate: Date? = null
    var beforeEndDate: Date? = null
    private var loopSelectId = 0
    private var travelTimeSelectId = 0

    var FORMAT_DATETIME_SLASH = "yyyy/MM/dd(E) HH:mm"
    var FORMAT_DATE_SLASH = "yyyy/MM/dd(E)"
    var FORMAT_TIME = "HH:mm"

    var startDateFormat = FORMAT_DATETIME_SLASH
    var endDateFormat = FORMAT_DATETIME_SLASH

    /* -------------------- ライフサイクル -------------------- */
    /*
    起動：onCreate→onResume
    スリープから復帰、他画面からの復帰：onRestart→onResume
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.plan_registration_layout)

        val startDate = Date().roundTime()
        val endDate = startDate.addMinutes(120)
        // 表示更新
        start_date.text = startDate.toString(startDateFormat)
        end_date.text = endDate.toString(endDateFormat)

        add_cancel.setOnSafeClickListener { onBackPressed() }

        // タイトルテキストビューの変更イベント
        plan_title_edit_text.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                validation()
            }
        })

        // 場所テキストビューの変更イベント
        plan_place_edit_text.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                validation()
            }
        })

        // 終日スイッチ切り替え
        end_switch.setOnCheckedChangeListener { _, isChecked ->
            changeViewAllDay(isChecked)
            validation()
        }

        // 開始時間クリック時の処理
        start_column.setOnSafeClickListener {
            DateDialogUtil
                    .defaultDate(startDate)
                    .format(startDateFormat)
                    .isClear(false)
                    .showDateDialog(context!!, start_date)
        }

        // 終了時間クリック時の処理
        end_column.setOnSafeClickListener {
            DateDialogUtil
                    .defaultDate(endDate)
                    .format(endDateFormat)
                    .isClear(false)
                    .showDateDialog(context!!, end_date)
        }

        // 終日切り替えた際にフォーマットが変わるため、変更前の日付を保持しておく
        start_date.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
                if (DateDialogUtil.timeContains(startDateFormat)) {
                    beforeStartDate = start_date.text.toString().toDate(startDateFormat) // 変更前の日付を保持
                }
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                validation()
            }
        })

        end_date.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
                if (DateDialogUtil.timeContains(endDateFormat)) {
                    beforeEndDate = end_date.text.toString().toDate(endDateFormat) // 変更前の日付を保持
                }
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                validation()
            }
        })

        // 繰り返しのクリックイベント(選択ロールを表示)
        loop_column.setOnSafeClickListener { showListSelectDialog(loop_select_label) }
        loop_select_label.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                if (loopSelectId != 0) {
                    if (end_loop_select_label.text.isBlank()) {
                        end_loop_select_label.text = end_date.text.toString().toDate(endDateFormat)!!.toString(FORMAT_DATE_SLASH)
                    }
                    end_loop_layout.visibility = View.VISIBLE
                } else {
                    end_loop_select_label.text = ""
                    end_loop_layout.visibility = View.GONE
                }
                validation()
            }
        })

        // 繰り返し終了
        end_loop_column.setOnSafeClickListener {
            DateDialogUtil
                    .defaultDate(endDate)
                    .format(FORMAT_DATE_SLASH)
                    .isClear(false)
                    .showDateDialog(context!!, end_loop_select_label)
        }
        end_loop_select_label.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                validation()
            }
        })

        // 移動時間のクリックイベント(選択ロールを表示)
        move_select_label.setOnSafeClickListener { showListSelectDialog(move_select_label) }

        // 追加するボタン押下時の処理
        do_add_button.setOnSafeClickListener { addSchedule() }

        // 追加ボタン押下時の処理
        add_text.setOnSafeClickListener { addSchedule() }

        changeViewAllDay(isChecked = false)
        validation()
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        log(context?.javaClass!!.name, "onResume")
    }

    // スリープからの復帰、他画面からの復帰
    override fun onRestart() {
        super.onRestart()
        // リストビューの位置を取得する
        log(context?.javaClass!!.name, "onRestart")
    }

    override fun onPause() {
        super.onPause()

        log(context?.javaClass!!.name, "onPause")
    }

    // 終日にチェックを入れている場合は開始と終日は日付にする。
    private fun changeViewAllDay(isChecked: Boolean) {
        var startDate = start_date.text.toString().toDate(startDateFormat) ?: return
        var endDate = end_date.text.toString().toDate(endDateFormat) ?: return
        if (isChecked) {
            // 時分を保存
            beforeStartDate = start_date.text.toString().toDate(startDateFormat) // 変更前の日付を保持
            beforeEndDate = end_date.text.toString().toDate(endDateFormat) // 変更前の日付を保持
            //
            travel_layout.visibility = View.GONE
            startDateFormat = FORMAT_DATE_SLASH
            endDateFormat = FORMAT_DATE_SLASH
        } else {
            // 時分を読込
            if (beforeStartDate != null) {
                startDate = startDate.setHour(beforeStartDate!!.toInt("H")).setMinute(beforeStartDate!!.toInt("m"))
                beforeStartDate = null
            }
            if (beforeEndDate != null) {
                endDate = endDate.setHour(beforeEndDate!!.toInt("H")).setMinute(beforeEndDate!!.toInt("m"))
                beforeEndDate = null
            }
            travel_layout.visibility = View.VISIBLE
            startDateFormat = FORMAT_DATETIME_SLASH
            endDateFormat = FORMAT_DATETIME_SLASH
        }
        start_date.text = startDate.toString(startDateFormat)
        end_date.text = endDate.toString(endDateFormat)
    }

    //キーボードを非表示にする
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(plan_regist_layout!!.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す（EditTextからフォーカスを外す）
        plan_regist_layout!!.requestFocus()

        return super.dispatchTouchEvent(ev)
    }

    /**
     * 必須テキスト項目変更時に
     * 全てのテキスト項目に値が入っていればボタンを活性化する.
     */
    private fun validation(): Boolean {
        var check: Boolean = true
        if (plan_title_edit_text.text.isBlank()) {
            check = false
        }
        val startDate = start_date.text.toString().toDate(startDateFormat)
        val endDate = end_date.text.toString().toDate(endDateFormat)

        if (startDate != null && endDate != null) {
            if (startDate <= endDate) {
                end_date.setTextColor(ContextCompat.getColor(this, R.color.black))
                val paint = end_date.paint
                paint?.flags = end_date.paintFlags and Paint.STRIKE_THRU_TEXT_FLAG.inv()
                paint?.isAntiAlias = true
            } else {
                end_date.setTextColor(ContextCompat.getColor(this, R.color.red))
                val paint = end_date.paint
                paint?.flags = end_date.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
                paint?.isAntiAlias = true
                check = false
            }
            val endLoopDate = end_loop_select_label.text.toString().toDate(FORMAT_DATE_SLASH)
            if (endLoopDate != null && end_loop_select_label.text.isNotBlank()) {
                if (endDate.startTime() <= endLoopDate.startTime()) {
                    end_loop_select_label.setTextColor(ContextCompat.getColor(this, R.color.black))
                    val paint = end_loop_select_label.paint
                    paint?.flags = end_loop_select_label.paintFlags and Paint.STRIKE_THRU_TEXT_FLAG.inv()
                    paint?.isAntiAlias = true
                } else {
                    end_loop_select_label.setTextColor(ContextCompat.getColor(this, R.color.red))
                    val paint = end_loop_select_label.paint
                    paint?.flags = end_loop_select_label.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
                    paint?.isAntiAlias = true
                    check = false
                }
            }
        } else {
            check = false
        }

        // デザイン変更
        if (check) {
            do_add_button.isEnabled = true
            do_add_button.setBackgroundResource(R.drawable.radius_frame_blue)
            add_text.isEnabled = true
            add_text.setTextColor(ContextCompat.getColor(this, R.color.white))
        } else {
            do_add_button.isEnabled = false
            do_add_button.setBackgroundResource(R.drawable.radius_frame_gray)
            add_text.isEnabled = false
            add_text.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
        }
        return check
    }


    // リスト選択型ダイアログを表示
    private fun showListSelectDialog(textView: TextView) {
        val listDialog = AlertDialog.Builder(context!!, android.R.style.Theme_Holo_Light_Dialog_NoActionBar)
        when (textView) {
            loop_select_label -> {
                // 繰り返し項目の場合
                var loopList = context!!.resources.getStringArray(R.array.plan_loop_select_list)
                listDialog.setTitle(R.string.plan_regist_loop)
                listDialog.setItems(loopList) { _, which ->
                    loopSelectId = which
                    textView.text = loopList[which]
                }
            }
            move_select_label -> {
                // 移動時間項目の場合
                var moveList = context!!.resources.getStringArray(R.array.plan_move_hour_select_list)
                listDialog.setTitle(R.string.plan_regist_move_hour)
                listDialog.setItems(moveList) { _, which ->
                    travelTimeSelectId = which
                    textView.text = moveList[which]
                }
            }
        }
        listDialog.create().show()
    }

    /**
     * JSON形式の送信データを生成します
     */
    private fun createJsonData(): String {
        val json = JsonData()
        val jsonData: ArrayList<JsonData> = ArrayList()

        json.AllDay = if (end_switch.isChecked) "1" else "0" // Boolean値変換
        json.StartDateTime = start_date.text.toString().toDate(startDateFormat)!!.toString(Constant.FORMAT_DATETIME_HYPHEN)
        json.EndDateTime = end_date.text.toString().toDate(endDateFormat)!!.toString(Constant.FORMAT_DATETIME_HYPHEN)
        json.Title = plan_title_edit_text.text.toString()
        json.Remarks = plan_memo_multi_text.text.toString()
        json.Location = plan_place_edit_text.text.toString()
        json.TravelTime = travelTime[travelTimeSelectId]
        json.RepetitionID = loopSelectId.toString()
        json.EndRepetition = if (end_loop_select_label.text.toString() != "") {
            end_loop_select_label.text.toString().toDate(FORMAT_DATE_SLASH)!!.toString(Constant.FORMAT_DATETIME_HYPHEN)
        } else {
            ""
        }
        json.UserID = this.settings.getString("USER_ID")
        jsonData.add(json)

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.JsonData = Gson().toJson(jsonData)

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        return jsonParams
    }


    /* -------------------- 画面遷移 -------------------- */

    // 端末の戻るボタン
    override fun onBackPressed() {
        finish()
        overridePendingTransition(R.anim.fade_in, R.anim.slide_out_down)
    }

    // tokenの送信
    private fun addSchedule() {
        if (!validation()) return
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    return
                }
                try {
                    finish()
                    overridePendingTransition(R.anim.fade_in, R.anim.slide_out_down)
                } catch (ex: Exception) {
                    Toast.makeText(context, context!!.getString(R.string.unexpected_error), Toast.LENGTH_LONG).show()
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })
        // URL作成
        val url: String = Constant.URL + Constant.CalendarAddScheduleURL
        val jsonParams = createJsonData()

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}
