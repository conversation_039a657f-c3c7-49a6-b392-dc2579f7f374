package fois.dailyreportsystem.activity.plan

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Paint
import android.os.Bundle
import android.os.Handler
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.Window
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import android.widget.LinearLayout.LayoutParams
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.DateDialogUtil
import fois.dailyreportsystem.data.JsonData
import fois.dailyreportsystem.data.Schedule
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.plan_edit_layout.*
import org.json.JSONObject
import java.util.*

/**
 * 予定編集画面のアクティビティクラス
 */
class PlanEditActivity : BaseActivity() {

    private lateinit var scheduleData: Schedule

    var beforeStartDate: Date? = null
    var beforeEndDate: Date? = null

    private var loopSelectId = 0
    private var schedule: Schedule? = null
    private var travelTimeSelectId: Int = 0


    var FORMAT_DATETIME_SLASH = "yyyy/MM/dd(E) HH:mm"
    var FORMAT_DATE_SLASH = "yyyy/MM/dd(E)"
    var FORMAT_TIME = "HH:mm"

    var startDateFormat = FORMAT_DATETIME_SLASH
    var endDateFormat = FORMAT_DATETIME_SLASH

    /* -------------------- ライフサイクル -------------------- */
    /*
    起動：onCreate→onResume
    スリープから復帰、他画面からの復帰：onRestart→onResume
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.plan_edit_layout)

        scheduleData = try {
            intent!!.getParcelableExtra<Schedule>("SCHEDULE") as Schedule
        } catch (e: Exception) {
            Schedule.init()
        }

        val customUncaughtExceptionHandler = CustomUncaughtExceptionHandler(context!!)
        Thread.setDefaultUncaughtExceptionHandler(customUncaughtExceptionHandler)

        cancel.setOnSafeClickListener { onBackPressed() }

        // 移動時間のクリックイベント(選択ロールを表示)
        move_select_label.setOnSafeClickListener { showListSelectDialog(move_select_label) }

        // 完了ボタン押下時の処理
        complete_text.setOnSafeClickListener {
            // 入力確認をして更新処理を実行
            if (validation()) { modifyScheduleAsync()  }
        }

        delete_plan_button.setOnSafeClickListener { openDialog(it.id) }
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        getScheduleAsync()

        log(context?.javaClass!!.name, "onResume")
    }

    // スリープからの復帰、他画面からの復帰
    override fun onRestart() {
        super.onRestart()
        // リストビューの位置を取得する
        log(context?.javaClass!!.name, "onRestart")
    }

    override fun onPause() {
        super.onPause()

        log(context?.javaClass!!.name, "onPause")
    }

    //キーボードを非表示にする
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(main_layout!!.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す（EditTextからフォーカスを外す）
        main_layout!!.requestFocus()

        return super.dispatchTouchEvent(ev)
    }

    /* -------------------- 画面遷移 -------------------- */

    /**
     * 必須テキスト項目変更時に
     * 全てのテキスト項目に値が入っていればボタンを活性化する.
     */
    private fun validation(): Boolean {
        var check: Boolean = true
        if (plan_title_edit_text.text.isBlank()) {
            check = false
        }

        val startDate = start_date.text.toString().toDate(startDateFormat)
        val endDate = end_date.text.toString().toDate(endDateFormat)

        if (startDate != null && endDate != null) {
            if (startDate <= endDate) {
                end_date.setTextColor(ContextCompat.getColor(this, R.color.black))
                val paint = end_date.paint
                paint?.flags = end_date.paintFlags and Paint.STRIKE_THRU_TEXT_FLAG.inv()
                paint?.isAntiAlias = true
            } else {
                end_date.setTextColor(ContextCompat.getColor(this, R.color.red))
                val paint = end_date.paint
                paint?.flags = end_date.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
                paint?.isAntiAlias = true
                check = false
            }
            val endLoopDate = end_loop_select_label.text.toString().toDate(FORMAT_DATE_SLASH)
            if (endLoopDate != null && end_loop_select_label.text.isNotBlank()) {
                if (endDate.startTime() <= endLoopDate.startTime()) {
                    end_loop_select_label.setTextColor(ContextCompat.getColor(this, R.color.black))
                    val paint = end_loop_select_label.paint
                    paint?.flags = end_loop_select_label.paintFlags and Paint.STRIKE_THRU_TEXT_FLAG.inv()
                    paint?.isAntiAlias = true
                } else {
                    end_loop_select_label.setTextColor(ContextCompat.getColor(this, R.color.red))
                    val paint = end_loop_select_label.paint
                    paint?.flags = end_loop_select_label.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
                    paint?.isAntiAlias = true
                    check = false
                }
            }
        } else {
            check = false
        }
        if(schedule!!.allDay == if (end_switch.isChecked) {"1"} else {"0"}
                && schedule!!.startDateTime == try{ start_date.text.toString().toDate(startDateFormat)!!.toString(Constant.FORMAT_DATETIME_HYPHEN)}catch (e:Exception){""}
                && schedule!!.endDateTime == try{ end_date.text.toString().toDate(endDateFormat)!!.toString(Constant.FORMAT_DATETIME_HYPHEN)}catch (e:Exception){""}
                && schedule!!.title == plan_title_edit_text.text.toString()
                && schedule!!.remarks == plan_memo_multi_text.text.toString()
                && schedule!!.location == plan_place_edit_text.text.toString()
                && schedule!!.travelTime == try{ Constant.travelTime[travelTimeSelectId]}catch (e:Exception){""}
                && schedule!!.repetitionID == loopSelectId.toString()
                && schedule!!.endRepetition == try{ if (end_loop_select_label.text.toString() != "") { end_loop_select_label.text.toString().toDate(FORMAT_DATE_SLASH)!!.toString(Constant.FORMAT_DATETIME_HYPHEN) } else{""}}catch (e:Exception){""}
                ) {
            check = false
        }

        // デザイン変更
        if (check) {
            complete_text.isEnabled = true
            complete_text.setTextColor(ContextCompat.getColor(this, R.color.white))
        } else {
            complete_text.isEnabled = false
            complete_text.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
        }
        return check
    }

    // 終日にチェックを入れている場合は開始と終日は日付にする。
    private fun changeViewAllDay(isChecked: Boolean) {
        var startDate = start_date.text.toString().toDate(startDateFormat) ?: return
        var endDate = end_date.text.toString().toDate(endDateFormat) ?: return
        if (isChecked) {
            // 時分を保存
            beforeStartDate = start_date.text.toString().toDate(startDateFormat) // 変更前の日付を保持
            beforeEndDate = end_date.text.toString().toDate(endDateFormat) // 変更前の日付を保持

            travel_layout.visibility = View.GONE
            startDateFormat = FORMAT_DATE_SLASH
            endDateFormat = FORMAT_DATE_SLASH
        } else {
            // 時分を読込
            if (beforeStartDate != null) {
                startDate = startDate.setHour(beforeStartDate!!.toInt("H")).setMinute(beforeStartDate!!.toInt("m"))
                beforeStartDate = null
            }
            if (beforeEndDate != null) {
                endDate = endDate.setHour(beforeEndDate!!.toInt("H")).setMinute(beforeEndDate!!.toInt("m"))
                beforeEndDate = null
            }
            travel_layout.visibility = View.VISIBLE
            startDateFormat = FORMAT_DATETIME_SLASH
            endDateFormat = FORMAT_DATETIME_SLASH
        }
        start_date.text = startDate.toString(startDateFormat)
        end_date.text = endDate.toString(endDateFormat)
    }

    // リスト選択型ダイアログを表示
    private fun showListSelectDialog(textView: TextView) {
        val listDialog = AlertDialog.Builder(context!!, android.R.style.Theme_Holo_Light_Dialog_NoActionBar)
        when (textView) {
            loop_select_label -> {
                // 繰り返し項目の場合
                val loopList = context!!.resources.getStringArray(R.array.plan_loop_select_list)
                listDialog.setTitle(R.string.plan_regist_loop)
                listDialog.setItems(loopList) { _, which ->
                    loopSelectId = which
                    textView.text = loopList[which]
                }
            }
            move_select_label -> {
                // 移動時間項目の場合
                val moveList = context!!.resources.getStringArray(R.array.plan_move_hour_select_list)
                listDialog.setTitle(R.string.plan_regist_move_hour)
                listDialog.setItems(moveList) { _, which ->
                    travelTimeSelectId = which
                    textView.text = moveList[which]
                }
            }
        }
        listDialog.create().show()
    }

    // 端末の戻るボタン
    override fun onBackPressed() {
        val intent = Intent(this, PlanDetailActivity::class.java)
        intent.putExtra("SCHEDULE_ID", schedule!!.scheduleID)
        intent.putExtra("SCHEDULE", schedule)
        startActivityForResult(intent, 1)
        finish()
        overridePendingTransition(R.anim.fade_in, R.anim.slide_out_down)
    }

    /**
     * カレンダー側から送信された情報を元に項目を設定する。
     */
    private fun setViewData() {

        end_switch.isChecked = schedule!!.allDay.equals("1")

        // 予定編集から渡されているスケジュールの開始日付、終了日付を比較し繰り返しの予定かを判断する
        if (scheduleData!!.startDateTime != schedule!!.startDateTime!! &&
                scheduleData!!.endDateTime != schedule!!.endDateTime) {
            val toast = Toast.makeText(context, context!!.getString(R.string.warning_plan_repeat_edit), Toast.LENGTH_SHORT)
            toast.setGravity(Gravity.CENTER, 0, 0)
            toast.show()
        }

        start_date.text = schedule!!.startDateTime!!.toDate()!!.toString(startDateFormat)
        end_date.text = schedule!!.endDateTime!!.toDate()!!.toString(endDateFormat)

        plan_title_edit_text.setText(schedule!!.title, TextView.BufferType.NORMAL)
        plan_memo_multi_text.setText(schedule!!.remarks, TextView.BufferType.NORMAL)
        plan_place_edit_text.setText(schedule!!.location, TextView.BufferType.NORMAL)

        loop_select_label.text = context!!.resources.getStringArray(R.array.plan_loop_select_list)[schedule!!.repetitionID!!.toInt()]

        // 移動時間のindex位置を求めて、初期表示の値を設定する。
        var index = 0
        if (schedule!!.travelTime!!.isNotBlank() && schedule!!.travelTime!! != "null") {
            val selectTimeID = schedule!!.travelTime!!
            val valueList = Constant.travelTime.values

            for (value in valueList) {
                if (value == selectTimeID) {
                    break
                }
                index++
            }
        }

        move_select_label.text = context!!.resources.getStringArray(R.array.plan_move_hour_select_list)[index]

        // 繰り返し項目の復元
        loopSelectId = schedule!!.repetitionID!!.toInt()

        if (schedule!!.endRepetition!! != "") {
            end_loop_select_label.text = schedule!!.endRepetition!!.toDate()!!.toString(FORMAT_DATE_SLASH)
        }

        // 繰り返し項目の復元
        var selectTravelValue = schedule!!.travelTime

        run loop@{
            Constant.travelTime.keys.forEach { key ->
                var value = Constant.travelTime[key]

                travelTimeSelectId = key

                if (value == selectTravelValue) {
                    return@loop
                }
            }
        }

        setView()
    }

    /**
     * UIパーツのイベント、パーツ設定を行う。
     */
    private fun setView() {

        val startDate = schedule!!.startDateTime!!.toDate()!!
        val endDate = schedule!!.endDateTime!!.toDate()!!

        // タイトルテキストビューの変更イベント
        plan_title_edit_text.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                validation()
            }
        })

        // 場所テキストビューの変更イベント
        plan_place_edit_text.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                validation()
            }
        })

        // 終日スイッチ切り替え
        end_switch.setOnCheckedChangeListener { _, isChecked ->
            changeViewAllDay(isChecked)
            validation()
        }

        // 開始時間クリック時の処理
        start_column.setOnSafeClickListener {
            DateDialogUtil
                    .defaultDate(startDate)
                    .format(startDateFormat)
                    .isClear(false)
                    .showDateDialog(context!!, start_date)
        }

        // 終了時間クリック時の処理
        end_column.setOnSafeClickListener {
            DateDialogUtil
                    .defaultDate(endDate)
                    .format(endDateFormat)
                    .isClear(false)
                    .showDateDialog(context!!, end_date)
        }

        // 終日切り替えた際にフォーマットが変わるため、変更前の日付を保持しておく
        start_date.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
                if (DateDialogUtil.timeContains(startDateFormat)) {
                    beforeStartDate = start_date.text.toString().toDate(startDateFormat) // 変更前の日付を保持
                }
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                validation()
            }
        })

        end_date.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
                if (DateDialogUtil.timeContains(endDateFormat)) {
                    beforeEndDate = end_date.text.toString().toDate(endDateFormat) // 変更前の日付を保持
                }
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                validation()
            }
        })

        // 繰り返しのクリックイベント(選択ロールを表示)

        loop_column.setOnSafeClickListener { showListSelectDialog(loop_select_label) }
        loop_select_label.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                if (loopSelectId != 0) {
                    if (end_loop_select_label.text.isBlank()) {
                        end_loop_select_label.text = end_date.text.toString().toDate(endDateFormat)!!.toString(FORMAT_DATE_SLASH)
                    }
                    end_loop_layout.visibility = View.VISIBLE
                } else {
                    end_loop_select_label.text = ""
                    end_loop_layout.visibility = View.GONE
                }
                validation()
            }
        })

        if (schedule!!.endRepetition != "" && schedule!!.repetitionID != "0") {
            end_loop_layout.visibility = View.VISIBLE
        } else {
            end_loop_layout.visibility = View.GONE
        }

        // 繰り返し終了
        end_loop_column.setOnSafeClickListener {
            end_date.text.toString().toDate(endDateFormat)?.let { it1 ->
                DateDialogUtil
                        .defaultDate(it1)
                        .format(FORMAT_DATE_SLASH)
                        .isClear(false)
                        .showDateDialog(context!!, end_loop_select_label)
            }
        }
        end_loop_select_label.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                validation()
            }
        })

        changeViewAllDay(end_switch.isChecked)
    }


    /**
     *  openDialog ダイアログ表示処理
     * */
    private fun openDialog(clickId: Int) {
        var alertDialog: AlertDialog? = null
        val alertBuilder = AlertDialog.Builder(this, R.style.MyDialog)
        val mAlertLayout = this.layoutInflater.inflate(R.layout.guide_popup_window, null)

        val actionTextView = mAlertLayout.findViewById<TextView>(R.id.action_text)
        val cancelTextView = mAlertLayout.findViewById<TextView>(R.id.popup_cancel)
        cancelTextView.setOnSafeClickListener {
            if (alertDialog!!.isShowing) alertDialog!!.dismiss()
        }

        when (clickId) {
            delete_plan_button.id -> {
                actionTextView.text = this.resources.getString(R.string.plan_edit_delete)
                actionTextView.setTextColor(ContextCompat.getColor(this, R.color.red))
                actionTextView.setOnSafeClickListener {
                    deleteScheduleAsync()
                    if (alertDialog!!.isShowing) alertDialog!!.dismiss()
                }
            }
        }

        alertBuilder.setView(mAlertLayout)
        alertDialog = alertBuilder.create()

        alertDialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        val dialogLayoutParam = alertDialog.window!!.attributes

        dialogLayoutParam.gravity = Gravity.BOTTOM
        alertDialog.window!!.attributes = dialogLayoutParam
        alertDialog.window!!.setLayout(resources.displayMetrics.widthPixels, LayoutParams.WRAP_CONTENT)

        alertDialog.show()
    }

    /**
     * JSON形式の送信データを生成します
     */
    private fun createJsonData(): String {
        val json = JsonData()
        val jsonData: ArrayList<JsonData> = ArrayList()

        json.ScheduleID = schedule!!.scheduleID
        json.AllDay = if (end_switch.isChecked) "1" else "0" // Boolean値変換
        json.StartDateTime = start_date.text.toString().toDate(startDateFormat)!!.toString(Constant.FORMAT_DATETIME_HYPHEN)
        json.EndDateTime = end_date.text.toString().toDate(endDateFormat)!!.toString(Constant.FORMAT_DATETIME_HYPHEN)
        json.Title = plan_title_edit_text.text.toString()
        json.Remarks = plan_memo_multi_text.text.toString()
        json.Location = plan_place_edit_text.text.toString()
        json.TravelTime = Constant.travelTime[travelTimeSelectId]
        json.RepetitionID = loopSelectId.toString()
        json.EndRepetition = if (end_loop_select_label.text.toString() != "") {
            end_loop_select_label.text.toString().toDate(FORMAT_DATE_SLASH)!!.toString(Constant.FORMAT_DATETIME_HYPHEN)
        } else {
            ""
        }
        json.UserID = this.settings.getString("USER_ID")
        jsonData.add(json)

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.JsonData = Gson().toJson(jsonData)

        return Gson().toJson(params)
    }

    /**
     * スケジュールを情報取得処理
     */
    private fun getScheduleAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    return
                }

                try {
                    // 認証成功
                    val scheduleArrayList = result.getJSONArray("Schedules")
                    val array = JsonHandling.toScheduleArrayList(scheduleArrayList)
                    if (array != null && array.count() > 0) {
                        schedule = array[0]
                    }

                    // 画面に表示する情報を設定
                    setViewData()

                } catch (ex: Exception) {
                    Toast.makeText(context, context!!.getString(R.string.unexpected_error), Toast.LENGTH_LONG).show()
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // URL作成
        val url: String = Constant.URL + Constant.CalenderSchedule
        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ScheduleID = this.settings.getString("SCHEDULE_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * スケジュールを削除します。
     */
    private fun deleteScheduleAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    return
                }

                val status = result.getJSONObject("Status")
                val statusCode = status.getString("StatusCode")

                // 削除成功の場合、自身のアクティビティを終了する。
                // 予定一覧へ戻る
                if (statusCode == "0") {
                    finish()
                }

                try {

                } catch (ex: Exception) {
                    Toast.makeText(context, context!!.getString(R.string.unexpected_error), Toast.LENGTH_LONG).show()
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // URL作成
        val url: String = Constant.URL + Constant.CalenderDeleteScheduleURL
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ScheduleID = schedule!!.scheduleID

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * スケジュールを更新処理
     */
    private fun modifyScheduleAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    return
                }

                try {
                    onBackPressed()
                } catch (ex: Exception) {
                    Toast.makeText(context, context!!.getString(R.string.unexpected_error), Toast.LENGTH_LONG).show()
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // URL作成
        val url: String = Constant.URL + Constant.CalenderModifyScheduleURL
        val jsonParams = createJsonData()

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    class CustomUncaughtExceptionHandler(_context: Context) : Thread.UncaughtExceptionHandler {

        var mContext = _context
        lateinit var mDefaultUncaughtExceptionHandler: Thread.UncaughtExceptionHandler

        init {
            // デフォルト例外ハンドラを保持する。
            mDefaultUncaughtExceptionHandler = Thread.getDefaultUncaughtExceptionHandler()
        }


        override fun uncaughtException(thread: Thread, ex: Throwable) {
            // スタックトレースを文字列にします。
            ex.printStackTrace()
        }
    }

}
