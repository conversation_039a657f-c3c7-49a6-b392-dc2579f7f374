package fois.dailyreportsystem.activity.plan

import android.os.Bundle
import android.widget.AdapterView
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.AreaData
import fois.dailyreportsystem.data.adapter.AreaSurveyAdapter
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.area_list_layout.*
import org.json.JSONObject

class AreaActivity : BaseActivity() {

    private var areaList: ArrayList<AreaData> = ArrayList<AreaData>()

    private var checkAreaId = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        setContentView(R.layout.area_list_layout)
        super.onCreate(savedInstanceState)

        checkAreaId = this.settings.getString("SELECT_AREA_ID", "0")!!


        val areaName =  this.settings.getString("SELECT__NAME")
        if (areaName != "") {
            back_title.text = areaName
        }

        back_title.setOnSafeClickListener { onBackPressed() }
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }
    }


    override fun onStart() {
        super.onStart()

        getAreaList()
    }

    // 端末の戻るボタン
    override fun onBackPressed() {
        finish()
        overridePendingTransition(R.anim.fade_in, 0)
    }

    // リストビューセット
    private fun setPrefListView() {
        val itemAdapter = AreaSurveyAdapter(this, areaList)
        area_list.adapter = itemAdapter

        //Click item event
        area_list.onItemClickListener = clickItem
    }

    /**
     *  pref item click
     *
     */
    private val clickItem = AdapterView.OnItemClickListener { _, _, position, _ ->
        val areaData = area_list.getItemAtPosition(position) as AreaData

        this.settings.put("SELECT_AREA_ID", areaData.areaID)
        this.settings.put("SELECT__NAME", areaData.areaName)

        onBackPressed()
    }

    /**
     * getAreaList エリアリスト取得
     *
     **/
    private fun getAreaList() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        val areaJsonArray = result.getJSONArray("Areas")

                        // 選択がすべての場合はチェックをつける
                        var checked = false
                        if ("0" == checkAreaId) {
                            checked = true
                        }


                        val topAreaData = AreaData(areaID = "0",
                                areaName = resources.getString(R.string.shooting_all),
                                officeID = "0",
                                checked = checked)

                        areaList.add(topAreaData)

                        for (i in 0 until areaJsonArray.length()) {
                            val row = areaJsonArray.getJSONObject(i)

                            val areaData = AreaData(areaID = row.getString("AreaID"),
                                                    areaName = row.getString("AreaName"),
                                                    officeID = row.getString("OfficeID"),
                                                    checked = false)

                            if (areaData.areaID == checkAreaId) {
                                areaData.checked = true
                            }

                            areaList.add(areaData)
                        }

                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    setPrefListView()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.OfficeID = this.settings.getString("OFFICE_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.UserAreas

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

}