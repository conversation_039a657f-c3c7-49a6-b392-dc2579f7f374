package fois.dailyreportsystem.activity.plan

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.core.content.ContextCompat
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.Schedule
import fois.dailyreportsystem.util.*
import fois.dailyreportsystem.util.weekview.*
import kotlinx.android.synthetic.main.plan_detail_layout.*
import org.json.JSONObject
import java.util.*


class PlanDetailActivity : BaseActivity(), MonthLoader.MonthChangeListener {

    lateinit var scheduleData: Schedule

    // イベントがないと使っているライブラリが落ちるので、生成をしておく
    var event: WeekViewEvent? = null
    // Populate the week view with some events.
    private val eventList = ArrayList<WeekViewEvent>()

    private lateinit var scheduleId: String
    private lateinit var schedule: Schedule
    private var startDate: Date? = null
    private var endDate: Date? = null

    /* -------------------- ライフサイクル -------------------- */
    /*
    起動：onCreate→onResume
    スリープから復帰、他画面からの復帰：onRestart→onResume
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.plan_detail_layout)

        val intent = this.intent
        scheduleId = intent!!.getStringExtra("SCHEDULE_ID").toString()
        schedule = try{ intent!!.getParcelableExtra<Schedule>("SCHEDULE") as Schedule } catch (e: Exception){ Schedule.init() }

        back_arrow.setOnSafeClickListener {
            back_arrow.setImageResource(R.drawable.yellow_arrow_l_hover)
            onBackPressed()
        }

        edit_text.setOnSafeClickListener {
            val intent = Intent(this, PlanEditActivity::class.java)
            intent.putExtra("SCHEDULE", schedule)
            intent.putExtra("SCHEDULE_ID", scheduleId)
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_up, R.anim.fade_out)
        }

        weekView.isHorizontalFlingEnabled = false
        weekView.isVerticalFlingEnabled = false
        weekView.monthChangeListener = this

        setupDateTimeInterpreter(true)

        log(context?.javaClass!!.name, "onCreate")
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        setItem(schedule)

        // MainActivityから遷移してきた場合は、APIからデータ取得しない。
        val className = callingActivity?.className
        if(className != "fois.dailyreportsystem.activity.MainActivity") {
            // スケジュールの取得
            getScheduleAsync()
        }


        log(context?.javaClass!!.name, "onResume")
    }


    /**
     * Set up a date time interpreter which will show short date values when in week view and long
     * date values otherwise.
     * @param shortDate True if the date values should be short.
     */
    private fun setupDateTimeInterpreter(shortDate: Boolean) {
        weekView!!.dateTimeInterpreter = object : DateTimeInterpreter {
            override fun interpretDate(date: Calendar): String {
                return date.time.toString("d")
            }
            override fun interpretTime(hour: Int): String {
                return "$hour:00"
            }
            override fun interpretDayOfWeek(date: Calendar): String {
                return date.time.toString("EEE")
            }
        }
    }

    // スリープからの復帰、他画面からの復帰
    override fun onRestart() {
        super.onRestart()
        log(context?.javaClass!!.name, "onRestart")
    }

    override fun onPause() {
        super.onPause()

        log(context?.javaClass!!.name, "onPause")
    }

    @SuppressLint("SetTextI18n")
    private fun setItem(schedule: Schedule) {
        // 調査依頼のスケジュールか
        val researchTaskFlag = !schedule.researchTaskID.isNullOrBlank()
        val travelFlag = !schedule.travelTime.isNullOrEmpty()

        // 自身の予定の場合は編集可能
        val userId = this.settings.getString("USER_ID")
        edit_text.visibility = if (schedule.userID == userId && schedule.researchTaskID.isNullOrBlank()) { View.VISIBLE } else { View.GONE }

        plan_title.text = schedule.title
        if(researchTaskFlag) {
            plan_title.setTextColor(ContextCompat.getColor(this, R.color.defaultColor))
        }else{
            plan_title.setTextColor(ContextCompat.getColor(this, R.color.black))
        }

        plan_staff.text = schedule.userName

        startDate = (schedule.startDateTime ?: "").toDate()
        endDate = (schedule.endDateTime ?: "").toDate()
        if(startDate != null && endDate != null){
            if( schedule.allDay == "1"){
                plan_date.text = startDate!!.toString("yyyy年MM月dd日(E)から終日")
                plan_time.text = endDate!!.toString("終了：yyyy年MM月dd日(E)")
                // カレンダー非表示
                weekview_layout.visibility = View.GONE
            }else{
                // 当日終了
                if( startDate!!.startTime() == endDate!!.startTime()){
                    plan_date.text = startDate!!.toString("yyyy年MM月dd日 E曜日")
                    plan_time.text = startDate!!.toString("HH:mm") + "～" + endDate!!.toString("HH:mm")
                    // カレンダー日付：非表示
                    weekView.headerShow = false
                }
                // 別日終了
                else{
                    plan_date.text = startDate!!.toString("開始：yyyy年MM月dd日(E) HH:mm")
                    plan_time.text = endDate!!.toString("終了：yyyy年MM月dd日(E) HH:mm")
                    // カレンダー日付：表示
                    weekView.headerShow = true
                }

                var showDate = startDate!!
                // weekViewに表示するためイベントを設定する。
                eventList.add(WeekViewEvent(schedule))

                // 移動時間をカレンダーに追加
                if(travelFlag && schedule.travelTime != "0"){
                    // 空のスケジュール
                    val travel = Schedule.init()
                    showDate = startDate!!.addMinutes( - schedule.travelTime!!.toInt())
                    travel.startDateTime = showDate.toString(Constant.FORMAT_DATETIME_HYPHEN)
                    travel.endDateTime = schedule.startDateTime
                    eventList.add(WeekViewEvent(travel))
                }

                // カレンダーの表示初期値を設定
                weekView.showDay =  showDate.toCalendar()
                weekView.goToHour(showDate.hour().toDouble())
                // カレンダー非表示
                weekview_layout.visibility = View.VISIBLE
            }
        }

        // 開始時刻、終了時刻を編集して表示
        // 繰り返しが設定していれば、表示する (NULL "" 0)以外
        if (!schedule.repetitionID.isNullOrBlank() && schedule.repetitionID != "0" && schedule.repetitionID != "null"){
            loop_time.text = "繰り返し：" + context!!.resources.getStringArray(R.array.plan_loop_select_list)[schedule.repetitionID!!.toInt()]
            loop_time.visibility = View.VISIBLE
        }else{
            loop_time.visibility = View.GONE
        }

        if (!schedule.location.isNullOrBlank()) {
            plan_location.text = schedule.location
            plan_location.visibility = View.VISIBLE
        }else if(researchTaskFlag){
            plan_location.text = schedule.researchTask!!.PrefName + " " + schedule.researchTask!!.CityName + " " + schedule.researchTask!!.TaskAddress
            plan_location.visibility = View.VISIBLE
        }else{
            plan_location.visibility = View.GONE
        }

        // 移動時間が設定していれば、編集して表示する。
        if (!schedule.travelTime.isNullOrEmpty()) {
            travel_time.text = schedule.travelTime!!.toInt().toTravel()
            travel_time.visibility = View.VISIBLE
        }else{
            travel_time.visibility = View.GONE
        }


        // メモ
        plan_memo.text = schedule.remarks

    }

    /** WeekView UIパーツのリスナー処理  */
    override fun onMonthChange(newYear: Int, newMonth: Int): List<WeekViewEvent> {
        return eventList
    }


    /* -------------------- 画面遷移 -------------------- */

    // 端末の戻るボタン
    override fun onBackPressed() {
        finish()
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
    }

    /* -------------------- 通信処理 -------------------- */

    /**
     * スケジュールを更新処理
     */
    private fun getScheduleAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    return
                }
                try {
                    // 認証成功
                    val scheduleArrayList = result.getJSONArray("Schedules")
                    val array = JsonHandling.toScheduleArrayList(scheduleArrayList)
                    if (array != null && array.count() > 0){
                        scheduleData = array[0]
                    }
                    // setItem(scheduleData)

                } catch (ex: Exception) {
                    Toast.makeText(context, context!!.getString(R.string.unexpected_error), Toast.LENGTH_LONG).show()
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() {hideHub()}
        })

        // URL作成
        val url: String = Constant.URL + Constant.CalenderSchedule
        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ScheduleID = scheduleId

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

}
