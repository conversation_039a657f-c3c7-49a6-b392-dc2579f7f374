package fois.dailyreportsystem.activity.report

// 作成日：2017/08/23
// 更新日：2018/02/16

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.appcompat.app.AlertDialog
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.lifecycle.Transformations.map
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.*
import fois.dailyreportsystem.data.adapter.SubContentAdapter
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.report_result3_layout.*
import org.json.JSONObject
import java.lang.reflect.Type

// 実績登録画面3のアクティビティ
class ReportResult3Activity : BaseActivity() {

    // 変数宣言
    var daysToEnd: Int = -1
    private val listDaysToEnd: Array<String> = context!!.resources.getStringArray(R.array.days_to_end_list)
    val listDaysToEndValue: Array<Int> = context!!.resources.getStringArray(R.array.days_to_end_list_value)
            .map{it.toIntOrNull()?: 0}.toTypedArray()
    var listSubContent: ArrayList<SubContent> = ArrayList()
    var listWorkContent: ArrayList<WorkContent> = ArrayList()
    var WorkContents: ArrayList<WorkContent> = ArrayList()
    private var firstSet: Boolean = false

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.report_result3_layout)

        // クリックイベント
        // 戻るボタンタップ
        back_arrow.setOnClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            Back()
        }

        // ツールバー登録ボタン
        register.setOnClickListener {
            register.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
            SaveTempData()
        }

        // フッター登録ボタン
        report_register.setOnClickListener {
            report_register_button.setImageResource(R.drawable.com_regist_on)
            SaveTempData()
        }

        // 完了まで
        report_to_complete_area.setOnClickListener {
            ShowListSelectDialog(report_to_complete_select)
        }
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        // 邸名の表示
        toolbar_title.text = this.settings.getString("TASK_NAME")

        // データの受け取り
        registrationInfo = RegistrationInfo()
        registrationInfo = intent.getParcelableExtra<RegistrationInfo>(ConstantParameters.INTENT_KEY_TASKINFO)

        if (!firstSet) {
            SetDataForFirstTimeDisplay(registrationInfo!!.WorkResultStatus!!)
            firstSet = true
        }
    }

    /* -------------------- 読み込み・表示 -------------------- */
    fun SetDataForFirstTimeDisplay(mode: Int) {
        if (registrationInfo!!.ResultDayToEnd != null) {
            daysToEnd = registrationInfo!!.ResultDayToEnd!!.toInt()
        }
        // 実績変更時コメントと残り日数をセット
        var target = listDaysToEndValue.indexOfFirst { it == daysToEnd }
        target = if (target < 0) {0} else {target}
        report_to_complete_select.text = listDaysToEnd[target]
        if (mode == ConstantParameters.EDIT_MODE) {
            report_comment_input.setText(registrationInfo!!.ResultComment)
        }

        if (registrationInfo!!.ListWorkContent.isNullOrEmpty()) {
            listWorkContent = JsonHandling.toWorkContentArrayList(registrationInfo!!.ListWorkContent!!) as ArrayList<WorkContent>
        }

        // 作業項目取得
        GetSubContentList(registrationInfo!!.ListWorkContentID.toString())
    }

    /* -------------------- 画面遷移 -------------------- */

    // 実績登録画面1もしくは2へ戻る
    fun Back() {
        if(clickPosition == null) {
            clickPosition = true
            val intent: Intent
            if (registrationInfo!!.IsWorkStopResult!!) {
                intent = Intent(this, ReportResult1Activity::class.java)
            } else {
                intent = Intent(this, ReportResult2Activity::class.java)
            }
            intent.putExtra(ConstantParameters.INTENT_KEY_TASKINFO, registrationInfo)
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
        }
    }

    // 日報一覧画面へ戻る
    fun BackReportList() {
        if(clickPosition == null) {
            clickPosition = true
            ShowMessages().Show(context, context!!.getString(R.string.report_registered))
            val intent = Intent(this, ReportListActivity::class.java)
            intent.putExtra(ConstantParameters.INTENT_KEY_TASKID, registrationInfo?.TaskID)
            intent.putExtra(ConstantParameters.INTENT_KEY_TASKNAME, registrationInfo?.TaskName)
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
        }
    }

    /* -------------------- リストビューイベント -------------------- */
    fun SetListViewSubContent() {
        // 一時チェックしたサブ内容を設定
        if (!registrationInfo!!.ListSelectedWorkSubContent.isNullOrEmpty()) {
            val type: Type = object : TypeToken<java.util.ArrayList<SubContent>>() {}.type
            val subContentArray: ArrayList<SubContent> = Gson().fromJson(registrationInfo!!.ListSelectedWorkSubContent, type)
            for (i in 0..subContentArray.size-1) {
                for (j in 0..listSubContent.size-1) {
                    if (subContentArray[i].ContentID == listSubContent[j].ContentID && subContentArray[i].SubContentID == listSubContent[j].SubContentID) {
                        listSubContent[j].isCheck = true
                        if (listSubContent[j].usedFlag == 2) {
                            listSubContent[j].UsedAmountLarge = subContentArray[i].UsedAmountLarge
                            listSubContent[j].UsedAmountSmall = subContentArray[i].UsedAmountSmall
                        }
                    }
                }
            }
        }

        val itemAdapter = SubContentAdapter(this, listSubContent)
        report_result3_listview.adapter = itemAdapter

        var totalHeight = 0
        val desiredWidth = View.MeasureSpec.makeMeasureSpec(report_result3_listview.width, View.MeasureSpec.AT_MOST)
        for (i in 0..itemAdapter.count-1) {
            val listItem = itemAdapter.getView(i, null, report_result3_listview)
            listItem.measure(desiredWidth, View.MeasureSpec.UNSPECIFIED)
            totalHeight += listItem.measuredHeight
        }

        val params = report_result3_listview.layoutParams
        params.height = totalHeight + report_result3_listview.dividerHeight * (itemAdapter.count - 1)
        report_result3_listview.layoutParams = params
        report_result3_listview.requestLayout()
    }

    // ビューの取得
    fun GetViewByPosition(position: Int, listView: ListView): View {
        val firstListItemPosition = listView.firstVisiblePosition
        val lastListItemPosition = firstListItemPosition + listView.childCount-1
        if (position < firstListItemPosition || lastListItemPosition < position) {
            return listView.adapter.getView(position, null, listView)
        } else {
            val childIndex = position - firstListItemPosition
            return listView.getChildAt(childIndex)
        }
    }

    /* -------------------- タップイベント -------------------- */
    // 端末の戻るボタン
    override fun onBackPressed() {
        Back()
    }

    //キーボードを非表示にする
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(main_layout!!.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す（EditTextからフォーカスを外す）
        main_layout!!.requestFocus()

        return super.dispatchTouchEvent(ev)
    }

    /* -------------------- タップイベント -------------------- */
    private fun ShowListSelectDialog(textView: TextView) {
        val listDialog = AlertDialog.Builder(context!!, android.R.style.Theme_Holo_Light_Dialog_NoActionBar)
        listDialog.setTitle(R.string.report_to_complete)
        listDialog.setItems(listDaysToEnd) { _, which ->
            daysToEnd = listDaysToEndValue[which]
            textView.text = listDaysToEnd[which]
        }
        listDialog.create().show()
    }

    /* -------------------- データ保存、入力チェック -------------------- */
    private fun SaveTempData() {

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        // 現在の時間を設定ファイルに保存
        this.settings.put("LAST_HANDLE", System.currentTimeMillis())

        var errMsg: String = ""

        // 残り日数
        this.report.put("DAYS_TO_END", daysToEnd.toString())
        log("DaysToEnd", this.report.getString("DAYS_TO_END"))

        if (daysToEnd < 0) {
            errMsg = context!!.getString(R.string.report_days_not_select)
            showMessage(errMsg)
            return
        }

        // コメント
        this.report.put("COMMENT", report_comment_input.text.toString())
        /*if (report_comment_input.text.toString() == "") {
            errMsg = context!!.getString(R.string.report_comment_nothing)
        }
        log("Comment", reportTemporary!!.getString("COMMENT", ""))*/

        // 内容
        var parentView: View
        WorkContents.clear()
        var eachCount: Int = 0  // 選択した工程内の内容チェック数
        for (i in 0 until listSubContent.size) {
            parentView = GetViewByPosition(i, report_result3_listview)
            if (parentView.findViewById(R.id.report_sub_content_check) as? CheckBox != null) {
                val checkBox: CheckBox = parentView.findViewById(R.id.report_sub_content_check) as CheckBox
                // チェックボックスが存在する行のチェックボックスを取得
                if (checkBox.isChecked) {
                    // チェックされた行のデータを取得し保存
                    val workContent: WorkContent = WorkContent()
                    workContent.ContentID = listSubContent[i].ContentID
                    workContent.ContentName = listSubContent[i].ContentName
                    workContent.SubContentID = listSubContent[i].SubContentID
                    workContent.SubContentName = listSubContent[i].SubContentName
                    if (listSubContent[i].usedFlag == 2) {
                        val large: TextView = parentView.findViewById(R.id.report_used_large_select) as TextView
                        val small: TextView = parentView.findViewById(R.id.report_used_small_select) as TextView
                        var usedLarge: String = large.text.toString()
                        var usedSmall: String = small.text.toString()
                        if (usedLarge == "") {
                            usedLarge = "0"
                        }
                        if (usedSmall == "") {
                            usedSmall = "0"
                        }
                        workContent.UsedAmountLarge = usedLarge
                        workContent.UsedAmountSmall = usedSmall
                    } else {
                        // 使用量の大小が存在しない場合
                        workContent.UsedAmountLarge = "0"
                        workContent.UsedAmountSmall = "0"
                    }
                    workContent.UsedFlg = listSubContent[i].usedFlag.toString()
                    workContent.WorkerCompanyID = this.settings.getString("COMPANY_ID")
                    WorkContents.add(workContent)
                    eachCount++
                }
            } else {
                // チェックボックスが無い行で、そこまでにチェックがあったか判別
                if (i != 0 && eachCount == 0) {
                    errMsg = context!!.getString(R.string.report_sub_content_not_select)
                } else {
                    eachCount = 0
                }
            }
            // 最後にもチェックがされているか判別
            if (i == listSubContent.size - 1 && eachCount == 0) {
                errMsg = context!!.getString(R.string.report_sub_content_not_select)
            }
        }
        log("WorkContents", Gson().toJson(WorkContents))

        if (errMsg != "") {
            showMessage(errMsg)
        } else {
            // 通信してデータ保存
            AddResultAsync()
        }
    }

    private fun showMessage(errMsg: String) {
        // エラーメッセージの表示
        ShowMessages().Show(context, errMsg)
        // 入力内容にエラーがある場合、登録せず文字・ボタン色を元に戻す
        register.setTextColor(ContextCompat.getColor(this, R.color.white))
        report_register_button.setImageResource(R.drawable.com_regist)
    }

    /* -------------------- 通信処理 -------------------- */
    // 作業項目の取得
    private fun GetSubContentList(listContentID: String) {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    Toast.makeText(context, context!!.getString(R.string.connect_error), Toast.LENGTH_LONG).show()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val content_array = result.getJSONArray("Contents")
                        listSubContent.clear()
                        //内容一覧
                        for (i in 0..content_array.length()-1) {
                            // 工程の取得
                            val content_row = content_array.getJSONObject(i)
                            val content = SubContent()
                            content.ContentID = content_row.getString("ContentID")
                            content.ContentName = content_row.getString("ContentName")
                            content.type = 1
                            listSubContent.add(content)
                            val sub = content_row.getJSONArray("SubContents")
                            for (j in 0..sub.length()-1) {
                                // 内容の取得
                                val sub_row = sub.getJSONObject(j)
                                val subContent = SubContent()
                                subContent.ContentID = content_row.getString("ContentID")
                                subContent.ContentName = content_row.getString("ContentName")
                                subContent.SubContentID = sub_row.getString("SubContentID")
                                subContent.SubContentName = sub_row.getString("SubContentName")
                                subContent.usedFlag = sub_row.getInt("UsedFlg")
                                subContent.type = 2
                                listSubContent.add(subContent)
                            }
                        }

                        SetListViewSubContent()
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    Toast.makeText(context, context!!.getString(R.string.unexpected_error), Toast.LENGTH_LONG).show()
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.TaskID = registrationInfo!!.TaskID
        params.ContentID = listContentID
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.SubContentsURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        log(context?.javaClass!!.name, url + jsonParams)
    }

    // 実績登録
    private fun AddResultAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    Toast.makeText(context, context!!.getString(R.string.connect_error), Toast.LENGTH_LONG).show()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        BackReportList()
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    Toast.makeText(context, context!!.getString(R.string.unexpected_error), Toast.LENGTH_LONG).show()
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成

        // 作業者リスト
        val workerList: String = this.report.getString("LIST_WORKER")
        val type: Type = object : TypeToken<java.util.ArrayList<Worker>>() {}.type
        val workers: ArrayList<Worker> = Gson().fromJson(workerList, type)

        val result: WorkData = WorkData()
        result.Comment = this.report.getString("COMMENT")
        result.Date = this.report.getString("DATE")
        result.DaysToEnd = this.report.getString("DAYS_TO_END")
        result.EndTime = this.report.getString("END_TIME")
        result.StartTime = this.report.getString("START_TIME")
        result.Temperature = this.report.getString("TEMPERATURE")
        result.WeatherID = this.report.getString("WEATHER_ID")
        result.WorkContents = WorkContents
        result.WorkerCount = this.report.getString("WORKER_COUNT")
        result.Workers = workers

        val json: JsonData = JsonData()
        json.ReportDate = this.report.getString("DATE")
        json.WorkResult = result

        val JsonData: ArrayList<JsonData> = ArrayList()
        JsonData.add(json)

        val data: String = Gson().toJson(JsonData)

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.TaskID = this.settings.getString("TASK_ID")
        params.JsonData = data

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.AddResultURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        log(context?.javaClass!!.name, url + jsonParams)
    }
}