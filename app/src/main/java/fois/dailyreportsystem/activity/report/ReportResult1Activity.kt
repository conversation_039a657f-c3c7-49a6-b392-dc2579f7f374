package fois.dailyreportsystem.activity.report

// 作成日：2017/08/23
// 更新日：2018/04/06

import android.app.DatePickerDialog
import android.app.TimePickerDialog
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.appcompat.app.AlertDialog
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.*
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.report_result1_layout.*
import org.json.JSONObject
import java.util.*

// 実績登録画面1のアクティビティ
class ReportResult1Activity : BaseActivity() {

    // 変数宣言
    private val WEATHER: Array<String> = context!!.resources.getStringArray(R.array.weather_list)
    private var weatherId: Int = 1
    private var temperature: String = ""
    private val WORKER_COUNT: Array<String> = context!!.resources.getStringArray(R.array.worker_count_list)
    private var workerCount: Int = 0
    private var startTime: String = "0800"
    private var endTime: String = "1700"
    private var workStop: Boolean = false
    private var editMode: Int = 0
    private var reportInfo: RegistrationInfo? = null
    private var firstSet: Boolean = false
    var listWorkContent: ArrayList<WorkContent> = ArrayList()
    var listWorker: ArrayList<Worker> = ArrayList()

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.report_result1_layout)

        // クリックイベント
        // バックボタンタップ
        back_arrow.setOnClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            BackReportList()
        }

        // ツールバー次へボタンタップ
        next.setOnClickListener {
            next.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
            Forward()
        }

        // フッター次へボタンタップ
        report_next.setOnClickListener {
            report_next_button.setImageResource(R.drawable.com_next_on)
            Forward()
        }

        // 気温アイコンタップ
        report_temperature_input_icon.setOnClickListener {
            // 気温入力フィールドにフォーカスを当て、キーボードを表示
            report_temperature_input.requestFocus()
            val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            inputMethodManager.toggleSoftInput(1, InputMethodManager.SHOW_IMPLICIT)
        }

        // 各ダイアログの設定
        // 天気タップ
        report_weather_select.setOnClickListener { ShowListSelectDialog(report_weather_select) }
        report_weather_select_icon.setOnClickListener { ShowListSelectDialog(report_weather_select) }
        // 人数タップ
        report_worker_count_select.setOnClickListener { ShowListSelectDialog(report_worker_count_select) }
        report_worker_count_select_icon.setOnClickListener { ShowListSelectDialog(report_worker_count_select) }
        // 開始タップ
        report_start_time_select.setOnClickListener { ShowTimePickerDialog(report_start_time_select) }
        report_start_time_select_icon.setOnClickListener { ShowTimePickerDialog(report_start_time_select) }
        // 終了タップ
        report_end_time_select.setOnClickListener { ShowTimePickerDialog(report_end_time_select) }
        report_end_time_select_icon.setOnClickListener { ShowTimePickerDialog(report_end_time_select) }

        // 作業中止フィールドタップ
        report_work_stop_area.setOnClickListener {
            // チェックボックス切替
            report_work_stop_checkbox.isChecked = !report_work_stop_checkbox.isChecked
        }
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        // 邸名の表示
        toolbar_title.text = this.settings.getString("TASK_NAME")

        // Intentパラメータ取得
        reportInfo = intent.getParcelableExtra<RegistrationInfo>(ConstantParameters.INTENT_KEY_TASKINFO)
        editMode = reportInfo!!.WorkResultStatus!!.toInt()
        registrationInfo = reportInfo

        // 気温最大桁数の設定（2桁まで）
        report_temperature_input.addTextChangedListener(object: TextWatcher {
            // テキスト変更前
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            // テキスト変更中
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                if (!s.isNullOrEmpty()) {
                    if (s[0].toString() == "-") {
                        report_temperature_input.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(3))
                    } else {
                        report_temperature_input.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(2))
                    }
                }
            }
            // テキスト変更後
            override fun afterTextChanged(s: Editable?) {}
        })
        // データを表示
        if (!firstSet) {
            if (intent.getBooleanExtra(ConstantParameters.INTENT_KEY_TASKINFO_TEMP, false)) {
                SetTempData()
            } else {
                SetDataForFirstTimeDisplay(editMode)
            }
            firstSet = true
        }
    }

    /* -------------------- 読み込み、表示 -------------------- */
    // 初期表示
    fun SetDataForFirstTimeDisplay(mode: Int) {
        report_task_name.text = registrationInfo!!.TaskName
        report_date_select.text = registrationInfo!!.ReportDate
        report_work_stop_checkbox.isChecked = registrationInfo!!.IsWorkStopResult as Boolean

        when (mode) {
            ConstantParameters.EDIT_MODE -> {
                // 変更
                report_date_select_icon.visibility = View.INVISIBLE
                weatherId = registrationInfo!!.WeatherIDResult!!.toInt()
                report_weather_select.text = WEATHER[weatherId-1]
                report_temperature_input.setText(registrationInfo!!.TemperatureResult.toString())
                workerCount = registrationInfo!!.WorkerCountResult ?: 0
                report_worker_count_select.text = WORKER_COUNT[workerCount]
                report_start_time_select.text = DateTimeFormat.formatTime(registrationInfo!!.StartTimeResult!!)
                report_end_time_select.text = DateTimeFormat.formatTime(registrationInfo!!.EndTimeResult!!)
                GetResultAsync(registrationInfo!!.TaskID!!, registrationInfo!!.WorkResultID!!)
            }
            ConstantParameters.NEW_MODE -> {
                // 新規登録
                if (this.report.getString("WORK_ID") == "") {
                    // 予定登録されていない場合（デフォルトの値をセット）
                    report_weather_select.text = context!!.getString(R.string.report_weather_default)
                    report_temperature_input.setText("")
                    report_start_time_select.text = context!!.getString(R.string.report_start_time_default)
                    report_end_time_select.text = context!!.getString(R.string.report_end_time_default)
                } else {
                    // 予定登録されていた場合、予定のデータを取得
                    GetScheduleAsync()
                }
                report_date_select.setOnClickListener { ShowDatePickerDialog() }
                report_date_select_icon.setOnClickListener { ShowDatePickerDialog() }
            }
        }
    }

    // 予定登録済みの際の初期セット
    private fun SetData() {
        report_weather_select.text = WEATHER[weatherId-1]
        report_temperature_input.setText(temperature)
        report_worker_count_select.text = WORKER_COUNT[workerCount]
        report_start_time_select.text = DateTimeFormat.formatTime(startTime)
        report_end_time_select.text = DateTimeFormat.formatTime(endTime)
        report_work_stop_checkbox.isChecked = workStop
    }

    // Tempデータ表示処理
    fun SetTempData() {
        report_task_name.text = registrationInfo!!.TaskName
        report_date_select.text = registrationInfo!!.ReportDate

        when (registrationInfo!!.WorkResultStatus) {
            ConstantParameters.EDIT_MODE -> {
                report_date_select_icon.visibility = View.INVISIBLE
            }
            ConstantParameters.NEW_MODE -> {
                report_date_select.setOnClickListener { ShowDatePickerDialog() }
                report_date_select_icon.setOnClickListener { ShowDatePickerDialog() }
            }
        }
        weatherId = registrationInfo!!.WeatherIDResult!!.toInt()
        report_weather_select.text = WEATHER[weatherId-1]
        report_temperature_input.setText(registrationInfo!!.TemperatureResult.toString())
        workerCount = registrationInfo!!.WorkerCountResult ?: 0
        report_worker_count_select.text = WORKER_COUNT[workerCount]
        report_start_time_select.text = DateTimeFormat.formatTime(registrationInfo!!.StartTimeResult!!)
        report_end_time_select.text = DateTimeFormat.formatTime(registrationInfo!!.EndTimeResult!!)
        report_work_stop_checkbox.isChecked = registrationInfo!!.IsWorkStopResult as Boolean
    }

    /* -------------------- 画面遷移 -------------------- */
    // 日報一覧画面へ戻る
    private fun BackReportList() {
        val intent = Intent(this, ReportListActivity::class.java)
        intent.putExtra(ConstantParameters.INTENT_KEY_TASKID, registrationInfo?.TaskID)
        intent.putExtra(ConstantParameters.INTENT_KEY_TASKNAME, registrationInfo?.TaskName)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
    }

    // 日報実績登録画面2へ進む、もしくは作業中止の工程IDの取得
    private fun Forward() {
        // データ保存
        this.SaveTempData()
        if (registrationInfo!!.IsWorkStopResult!!) {
            // 作業中止の場合は作業中止用ContentIDを取得する
            GetWorkStopIDAsync()
        } else {
            // 入力チェック
            if (InputCheck()) {
                if(clickPosition == null) {
                    clickPosition = true
                    val intent: Intent = Intent(this, ReportResult2Activity::class.java)
                    intent.putExtra(ConstantParameters.INTENT_KEY_TASKINFO, registrationInfo)
                    startActivity(intent)
                    finish()
                    overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
                }
            } else {
                // 入力内容にエラーがある場合、遷移せず文字・ボタン色を元に戻す
                next.setTextColor(ContextCompat.getColor(this, R.color.white))
                report_next_button.setImageResource(R.drawable.com_next)
            }
        }
    }

    // 日報実績登録画面3へ進む
    private fun ForwardReportResult3() {
        if(clickPosition == null) {
            clickPosition = true
            val intent: Intent = Intent(this, ReportResult3Activity::class.java)
            intent.putExtra(ConstantParameters.INTENT_KEY_TASKINFO, registrationInfo)
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
        }
    }

    /* -------------------- ダイアログ、スピナー -------------------- */
    //DatePickerDialogを表示
    fun ShowDatePickerDialog() {
        val calendar: Calendar = Calendar.getInstance()

        val datePickerDialog = DatePickerDialog(
                this,
                DatePickerDialog.OnDateSetListener { _, year, month, dayOfMonth ->
                    var dayOfTheWeek: String = ""
                    val cal: Calendar = Calendar.getInstance()
                    cal.set(year, month, dayOfMonth)
                    // 曜日の取得
                    when (cal.get(Calendar.DAY_OF_WEEK)) {
                        Calendar.SUNDAY -> dayOfTheWeek = "(日)"
                        Calendar.MONDAY -> dayOfTheWeek = "(月)"
                        Calendar.TUESDAY -> dayOfTheWeek = "(火)"
                        Calendar.WEDNESDAY -> dayOfTheWeek = "(水)"
                        Calendar.THURSDAY -> dayOfTheWeek = "(木)"
                        Calendar.FRIDAY -> dayOfTheWeek = "(金)"
                        Calendar.SATURDAY -> dayOfTheWeek = "(土)"
                    }
                    // 日付を表示
                    report_date_select.text = (month+1).toString() + "月" + dayOfMonth.toString() + "日" + dayOfTheWeek
                    this.report.put("DATE", year.toString() + String.format("%02d", (month+1)) + String.format("%02d", dayOfMonth))
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DATE)
        )

        datePickerDialog.show()
    }

    // リスト選択型ダイアログを表示
    private fun ShowListSelectDialog(textView: TextView) {
        val listDialog = AlertDialog.Builder(context!!, android.R.style.Theme_Holo_Light_Dialog_NoActionBar)
        when (textView) {
            report_weather_select -> {
                // 天気の場合
                listDialog.setTitle(R.string.report_weather)
                listDialog.setItems(WEATHER, { _, which ->
                    weatherId = which + 1
                    textView.text = WEATHER[which]
                })
            }
            report_worker_count_select -> {
                // 作業人数の場合
                listDialog.setTitle(R.string.report_worker_count)
                listDialog.setItems(WORKER_COUNT, { _, which ->
                    workerCount = which
                    textView.text = WORKER_COUNT[which]
                })
            }
        }
        listDialog.create().show()
    }

    //TimePickerDialogを表示
    fun ShowTimePickerDialog(tvTimeSet: TextView) {
        val time: String = tvTimeSet.text.toString()
        val hour: Int = time.substring(0, time.indexOf(":")).toInt()
        val minute: Int = time.substring(time.lastIndexOf(":")+1).toInt()

        val timePickerDialog: TimePickerDialog = TimePickerDialog(
                this,
                TimePickerDialog.OnTimeSetListener { _, hh, mm ->
                    if (hh in 8..19) {
                        // 時間をセット
                        tvTimeSet.text = String.format("%02d:%02d", hh, mm)
                    } else {
                        // 00:00～7:59,20:00～24:00の場合エラー、再度選択し直し
                        ShowMessages().Show(context, context!!.getString(R.string.report_time_overtime))
                        ShowTimePickerDialog(tvTimeSet)
                    }
                },
                hour,
                minute,
                true
        )

        timePickerDialog.show()
    }

    /* -------------------- タップイベント -------------------- */
    //キーボードを非表示にする
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(main_layout.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す（EditTextからフォーカスを外す）
        main_layout.requestFocus()

        return super.dispatchTouchEvent(ev)
    }

    //端末の戻るボタン
    override fun onBackPressed() {
        BackReportList()
    }

    /* -------------------- データ保存 -------------------- */
    // 入力した情報を保持
    fun SaveTempData() {
        // 日付
        registrationInfo!!.ReportDate = report_date_select.text.toString()
        // 天気
        registrationInfo!!.WeatherIDResult = weatherId
        this.report.put("WEATHER_ID", weatherId.toString())
        // 気温
        var temperature: String = ""
        if (!report_temperature_input.text.toString().isNullOrEmpty()) {
            temperature = report_temperature_input.text.toString()
        }
        registrationInfo!!.TemperatureResult = temperature
        this.report.put("TEMPERATURE", temperature)
        // 人数
        registrationInfo!!.WorkerCountResult = workerCount
        this.report.put("WORKER_COUNT", workerCount.toString())
        // 開始時間
        registrationInfo!!.StartTimeResult = report_start_time_select.text.toString()
        this.report.put("START_TIME", report_start_time_select.text.toString().replace(":", ""))
        // 終了時間
        registrationInfo!!.EndTimeResult = report_end_time_select.text.toString()
        this.report.put("END_TIME", report_end_time_select.text.toString().replace(":", ""))
        // 作業中止
        registrationInfo!!.IsWorkStopResult = report_work_stop_checkbox.isChecked
    }

    /* -------------------- 入力チェック -------------------- */
    private fun InputCheck(): Boolean {
        var check: Boolean = true
        when {
        // 天気
            registrationInfo!!.WeatherIDResult == 0 -> {
                ShowMessages().Show(context, context!!.getString(R.string.report_weather_not_select))
                check = false
            }
        // 人数
            registrationInfo!!.WorkerCountResult == 0 -> {
                // 作業人数0は登録不可
                ShowMessages().Show(context, context!!.getString(R.string.report_worker_count_not_select))
                check = false
            }
        // 時間
            report_start_time_select.text.toString().replace(":", "").toInt() > report_end_time_select.text.toString().replace(":", "").toInt() -> {
                // 終了時間が開始時間よりも前は登録不可
                ShowMessages().Show(context, context!!.getString(R.string.report_time_error))
                check = false
            }
        }
        return check
    }

    /* -------------------- 通信処理 -------------------- */
    private fun GetResultAsync(taskID: String, workResultID: String) {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val report_array = result.getJSONArray("WorkReports")
                        listWorkContent.clear()
                        listWorker.clear()
                        for (i in 0..report_array.length() - 1) {
                            val report_row = report_array.getJSONObject(i)
                            if (report_row.getInt("WorkResultStatus") == 2) {
                                val workResult = report_row.getJSONObject("WorkResult")
                                val workContent_array = workResult.getJSONArray("WorkContents")
                                //工程チェックリストを取得
                                if(workContent_array.length() != 0){
                                    registrationInfo!!.ListWorkContent = workContent_array.toString()
                                    registrationInfo!!.ListSelectedWorkSubContent = workContent_array.toString()
                                }
                                //作業一覧を取得
                                if(JsonHandling.toIntOrDefaultValue(workResult, "WorkerCount", 0)!! > 0){
                                    val worker_array = workResult.getJSONArray("Workers")
                                    registrationInfo!!.ListWorker = worker_array.toString()
                                }
                            }
                        }

                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.TaskID = taskID
        params.WorkID = workResultID
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResultURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    private fun GetScheduleAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val report_array = result.getJSONArray("WorkReports")
                        listWorkContent.clear()
                        listWorker.clear()
                        for (i in 0..report_array.length() - 1) {
                            val report_row = report_array.getJSONObject(i)
                            if (report_row.getInt("WorkScheduleStatus") == 2) {
                                val workSchedule = report_row.getJSONObject("WorkSchedule")
                                weatherId = workSchedule.getInt("WeatherID")
                                temperature = workSchedule.getString("Temperature")
                                workerCount =
                                    JsonHandling.toIntOrDefaultValue(workSchedule, "WorkerCount", 0)!!
                                startTime = workSchedule.getString("StartTime")
                                if (startTime.isNullOrEmpty() || startTime == "null") {
                                    startTime = "0800"
                                }
                                endTime = workSchedule.getString("EndTime")
                                if (endTime.isNullOrEmpty() || endTime == "null") {
                                    endTime = "1700"
                                }
                                val workContent_array = workSchedule.getJSONArray("WorkContents")
                                //工程チェックリストを取得
                                if(workContent_array.length() != 0){
                                    registrationInfo!!.ListWorkContent = workContent_array.toString()
                                    registrationInfo!!.ListSelectedWorkSubContent = workContent_array.toString()
                                }
                                //作業一覧を取得
                                if(workerCount > 0){
                                    val worker_array = workSchedule.getJSONArray("Workers")
                                    registrationInfo!!.ListWorker = worker_array.toString()
                                }
                                registrationInfo!!.ResultDayToEnd = workSchedule.getInt("DaysToEnd")
                                if (workSchedule.getInt("WorkStop") == 2) {
                                    workStop = true
                                    registrationInfo!!.IsWorkStopResult = true
                                }
                            }
                        }
                        SetData()
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID" )
        params.TaskID = this.settings.getString("TASK_ID" )
        params.WorkID = this.report.getString("WORK_ID" )
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ScheduleURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    private fun GetWorkStopIDAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val contents = result.getJSONArray("Contents")
                        for (i in 0..contents.length()-1) {
                            val data = contents.getJSONObject(i)
                            if (data.getString("WorkStop") == "2") {
                                val workStopContent: ArrayList<WorkContent> = ArrayList()
                                val workContent: WorkContent = WorkContent()
                                workContent.ContentID = data.getString("ContentID")
                                workContent.ContentName = data.getString("ContentName")
                                workContent.SubContentID = ""
                                workContent.SubContentName = ""
                                workContent.WorkerCompanyID = ""
                                workStopContent.add(workContent)
                                registrationInfo!!.ListWorkContent = Gson().toJson(workStopContent).toString()
                                // 作業中止の工程IDを保存
                                registrationInfo!!.ListWorkContentID = data.getString("ContentID")
                                // 作業者を空白で保存
                                listWorker.clear()
                                val worker = Worker()
                                worker.UserID = ""
                                worker.UserName = ""
                                worker.UserPhoneNumber = ""
                                worker.CompanyID = ""
                                worker.CompanyName = ""
                                worker.CompanyNumber = ""
                                listWorker.add(worker)
                                context!!.report.put("LIST_WORKER", Gson().toJson(listWorker).toString())
                                // 実績登録画面3へ遷移
                                ForwardReportResult3()
                            }
                        }
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.TaskID = registrationInfo!!.TaskID!!
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ContentsURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}