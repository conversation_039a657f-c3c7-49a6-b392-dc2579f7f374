package fois.dailyreportsystem.activity.report

import android.os.Bundle
import android.view.MotionEvent
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.AttachFilesInfo
import fois.dailyreportsystem.data.adapter.FileListAdapter
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.file_list_layout.*
import org.json.JSONObject

/**
 * ファイル一覧アクティビティ
 */
class FileListActivity : BaseActivity() {
    private var taskID: String? = null
    private var taskName: String? = null

    private var attachFiles: List<AttachFilesInfo> = ArrayList()
    private var itemAdapter: FileListAdapter? = null

    /* -------------------- ライフサイクル -------------------- */
    /**
     * 生成時アクション
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.file_list_layout)

        // クリックイベント
        // 戻るボタンタップ
        back_arrow.setOnSafeClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            backMain()
        }
    }

    /**
     * 復帰時アクション
     */
    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()) {
            moveLogin()
            return
        }

        // TaskID, TaskNameを取得
        taskID = this.settings.getString("TASK_ID")
        taskName = this.settings.getString("TASK_NAME")


        // ツールバーの邸名を設定
        toolbar_title.text = taskName

        // ファイル一覧を表示
        this.getFileList()
    }

    /* -------------------- 画面遷移 -------------------- */

    /**
     * 邸一覧画面に戻る
     */
    private fun backMain() {
        if (clickPosition == null) {
            clickPosition = true
            finish()
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
        }
    }

    /* -------------------- タップイベント -------------------- */
    /**
     * 画面タッチイベント
     */
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        try {
            if (attachFiles.isNotEmpty()) {
                // 現在のビューの位置を保存する
                this.position.put("FILE_LIST1", file_list_listView.firstVisiblePosition)
                this.position.put("FILE_LIST2", file_list_listView.getChildAt(0).top)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return super.dispatchTouchEvent(ev)
    }

    /**
     * 端末の戻るボタン
     */
    override fun onBackPressed() {
        backMain()
    }

    /* -------------------- リストビューイベント -------------------- */
    private fun listSet() {
        itemAdapter = FileListAdapter(this, taskName!!, attachFiles)
        file_list_listView.adapter = itemAdapter
        file_list_listView.setSelectionFromTop(this.position.getInt("FILE_LIST1", 0), this.position.getInt("FILE_LIST2", 0))
    }

    /* -------------------- 通信処理 -------------------- */
    // 通信処理
    private fun getFileList() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {
                showHub()
            }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") != 0) {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                        return
                    }

                    // 認証成功
                    val fileArray = result.getJSONArray("AttachFiles")
                    attachFiles = fileArray.map { AttachFilesInfo.fromJsonObject(it) }
                    listSet()
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, "$ex")
                } finally {
                    hideHub()
                    clickPosition = null
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
                hideHub()
                clickPosition = null
            }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.TaskID = taskID
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.AttachFilesURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        listSet()

        log(context?.javaClass!!.name, url + jsonParams)
    }
}