package fois.dailyreportsystem.activity.report

// 作成日：2017/08/23
// 更新日：2018/02/16

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.content.ContextCompat
import android.view.MotionEvent
import android.view.inputmethod.InputMethodManager
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.*
import kotlinx.android.synthetic.main.report_schedule2_layout.*
import org.json.JSONObject
import com.google.gson.reflect.TypeToken
import fois.dailyreportsystem.data.adapter.ReportWorkerContentAdapter
import fois.dailyreportsystem.util.*
import java.lang.reflect.Type

// 予定登録画面2のアクティビティ
class ReportSchedule2Activity : BaseActivity() {

    // 変数宣言
    var listWorker: ArrayList<Worker> = ArrayList()
    var listContent: ArrayList<Content> = ArrayList()
    var listReportSchedule2: ArrayList<ReportWorkerContent> = ArrayList()
    private var firstSet: Boolean = false

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.report_schedule2_layout)

        // クリックイベント
        // 戻るボタンタップ
        back_arrow.setOnClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            BackReportSchedule1()
        }

        // ツールバー次へボタンタップ
        next.setOnClickListener {
            next.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
            ForwardReportSchedule3()
        }

        // フッター次へボタンタップ
        report_next.setOnClickListener {
            report_next_button.setImageResource(R.drawable.com_next_on)
            ForwardReportSchedule3()
        }
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        // 邸名を表示
        toolbar_title.text = this.settings.getString("TASK_NAME")

        // データの受け取り
        registrationInfo = RegistrationInfo()
        registrationInfo = intent.getParcelableExtra<RegistrationInfo>(ConstantParameters.INTENT_KEY_TASKINFO)

        // データの読み込み
        if (!firstSet) {
            SetDataFirstTime(registrationInfo!!.WorkScheduleStatus!!)
            firstSet = true
        }
    }

    /* -------------------- 読み込み・表示 -------------------- */
    fun SetDataFirstTime(mode: Int) {
        // 作業者の取得
        GetListWorker(mode)

        // 工程の取得
        GetContentListAsync()
    }

    // 作業者の人数と名前
    fun GetListWorker(mode: Int) {
        when (mode) {
        // 新規登録
            ConstantParameters.NEW_MODE -> {
                listWorker.clear()
                // 予定登録1からの遷移か、予定登録3からの戻りか判別
                if (registrationInfo!!.ListWorker.isNullOrEmpty()) {
                    // 予定登録1からの遷移
                    val firstWorker = Worker()
                    // 作業者の一人目はログイン中のユーザ
                    firstWorker.UserID = ""
                    firstWorker.UserName = this.settings.getString("USER_NAME")
                    firstWorker.UserPhoneNumber = ""
                    firstWorker.CompanyID = this.settings.getString("COMPANY_ID")
                    firstWorker.CompanyName = ""
                    firstWorker.CompanyNumber = ""
                    // リストに追加
                    listWorker.add(firstWorker)
                    // 予定登録画面1で選択した人数分になるまで繰り返し
                    for (i in 0..registrationInfo!!.WorkerCountSchedule!!.toInt() - 2) {
                        val worker = Worker()
                        // 二人目以降は空白
                        worker.UserName = ""
                        // リストに追加
                        listWorker.add(worker)
                    }
                } else {
                    // 予定登録3からの戻り（登録3から1まで戻り、2に遷移した場合も含む）
                    // 作業者リストの作成
                    val workerList: String = registrationInfo!!.ListWorker!!
                    val type: Type = object : TypeToken<java.util.ArrayList<Worker>>() {}.type
                    val workers: ArrayList<Worker> = Gson().fromJson(workerList, type)
                    // データの存在する人数分になるまで繰り返し（予定登録1で減らした場合はその人数分）
                    for (i in 0..workers.count() - 1) {
                        var worker = Worker()
                        worker.UserID = workers[i].UserID
                        worker.UserName = workers[i].UserName
                        worker.UserPhoneNumber = workers[i].UserPhoneNumber
                        worker.CompanyID = workers[i].CompanyID
                        worker.CompanyName = workers[i].CompanyName
                        worker.CompanyNumber = workers[i].CompanyNumber
                        listWorker.add(worker)
                        if (i == registrationInfo!!.WorkerCountSchedule!!-1) {
                            // 予定登録1で選択した人数分で終了
                            break
                        }
                        // 最後までデータが取れたら予定登録1で選択した人数分になるよう作業者を空白で追加
                        if (i == workers.count()-1 && workers.count() < registrationInfo!!.WorkerCountSchedule!!) {
                            for (j in 1..registrationInfo!!.WorkerCountSchedule!! - workers.count()) {
                                worker = Worker()
                                // データ無しの作業者以降は空白
                                worker.UserName = ""
                                // リストに追加
                                listWorker.add(worker)
                            }
                        }
                    }
                }
            }
        // 変更
            ConstantParameters.EDIT_MODE -> {
                val selectWorkerCount: Int = registrationInfo!!.WorkerCountSchedule!!.toInt()
                // 作業者一覧
                if (!registrationInfo!!.ListWorker.isNullOrEmpty()) {
                    listWorker.clear()
                    listWorker = JsonHandling.toWorkerArrayList(registrationInfo!!.ListWorker.toString())!!

                    // 人数が増えた場合
                    if (selectWorkerCount > listWorker.size) {
                        val addWorkerCount: Int = selectWorkerCount - listWorker.size - 1
                        for (i in 0..addWorkerCount) {
                            val addWorker = Worker()
                            // 名前は空白
                            addWorker.UserName = ""
                            // リストに追加
                            listWorker.add(addWorker)
                        }
                    }

                    // 人数が減った場合
                    if (selectWorkerCount < listWorker.size) {
                        for (i in listWorker.size - 1 downTo selectWorkerCount) {
                            listWorker.removeAt(i)
                        }
                    }
                }
            }
        }
        // 現在の状態を一時保存ファイルに保存
        this.report.put("LIST_WORKER", Gson().toJson(listWorker).toString())
    }

    // 工程（変更時）
    fun SetCheckWorkContent() {
        if (!registrationInfo!!.ListWorkContent.isNullOrEmpty()) {
            var ListContentID: String = ""
            val workContentArray: ArrayList<WorkContent> = JsonHandling.toWorkContentArrayList(registrationInfo!!.ListWorkContent.toString())!!
            for (i in 0..workContentArray.size - 1) {
                for (j in 0..listContent.size - 1) {
                    if (workContentArray[i].ContentID == listContent[j].ContentID) {
                        listContent[j].isCheck = true
                        ListContentID += listContent[j].ContentID + ","
                    }
                }
            }
            if (ListContentID.isNotEmpty()) {
                ListContentID = ListContentID.removeRange(ListContentID.length - 1, ListContentID.length)
            }
            registrationInfo!!.ListWorkContentID = ListContentID
        }
    }

    /* -------------------- 画面遷移 -------------------- */

    // 予定登録１画面へ戻る
    fun BackReportSchedule1() {
        if(clickPosition == null) {
            clickPosition = true
            val intent = Intent(this, ReportSchedule1Activity::class.java)
            intent.putExtra(ConstantParameters.INTENT_KEY_TASKINFO, registrationInfo)
            intent.putExtra(ConstantParameters.INTENT_KEY_TASKINFO_TEMP, true)
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
        }
    }

    // 日報ー予定登録画面3へ進む
    fun ForwardReportSchedule3() {
        // データ保存
        SaveTempData()
        // 入力チェック
        if (InputCheck()) {
            if(clickPosition == null) {
                clickPosition = true
                val intent = Intent(this, ReportSchedule3Activity::class.java)
                intent.putExtra(ConstantParameters.INTENT_KEY_TASKINFO, registrationInfo)
                startActivity(intent)
                finish()
                overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
            }
        } else {
            // 入力内容にエラーがある場合、遷移せず文字・ボタン色を元に戻す
            next.setTextColor(ContextCompat.getColor(this, R.color.white))
            report_next_button.setImageResource(R.drawable.com_next)
        }
    }

    /* -------------------- リストビューイベント -------------------- */
    // リストビューにセット
    fun SetListReportSchedule2() {
        SetCheckWorkContent()
        listReportSchedule2.clear()
        // 作業者サブタイトル
        val workerSubTitle: ReportWorkerContent = ReportWorkerContent()
        workerSubTitle.SubTitle = context!!.getString(R.string.report_worker_list_title)
        workerSubTitle.type = 1
        listReportSchedule2.add(workerSubTitle)
        // 作業者リスト
        for (i in 0..listWorker.count() - 1) {
            val worker: ReportWorkerContent = ReportWorkerContent()
            worker.WorkerName = listWorker[i].UserName
            worker.type = 2
            listReportSchedule2.add(worker)
        }
        // 工程サブタイトル
        val contentSubTitle: ReportWorkerContent = ReportWorkerContent()
        contentSubTitle.SubTitle = context!!.getString(R.string.report_content_list_title)
        contentSubTitle.type = 1
        listReportSchedule2.add(contentSubTitle)
        // 工程リスト
        for (i in 0..listContent.count() - 1) {
            val content: ReportWorkerContent = ReportWorkerContent()
            content.ContentID = listContent[i].ContentID
            content.ContentName = listContent[i].ContentName
            content.contentIsCheck = listContent[i].isCheck!!
            content.type = 3
            listReportSchedule2.add(content)
        }
        val reportSchedule2Adapter: ReportWorkerContentAdapter = ReportWorkerContentAdapter(this, listReportSchedule2)
        report_schedule2_listview.adapter = reportSchedule2Adapter
    }

    /* -------------------- タップイベント -------------------- */
    //端末の戻るボタン
    override fun onBackPressed() {
        BackReportSchedule1()
    }

    //キーボードを非表示にする
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(main_layout!!.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す（EditTextからフォーカスを外す）
        main_layout!!.requestFocus()

        return super.dispatchTouchEvent(ev)
    }

    /* -------------------- データ保存 -------------------- */
    fun SaveTempData() {
        // 作業者
        registrationInfo!!.ListWorker = this.report.getString("LIST_WORKER")
        log("ListWorker", registrationInfo!!.ListWorker!!)

        // 工程
        registrationInfo!!.ListWorkContent = this.report.getString("LIST_CONTENT")
        registrationInfo!!.ListWorkContentID = this.report.getString("UNION_CONTENT")
        log("ListWorkContent", registrationInfo!!.ListWorkContent!!)
        log("ListWorkContentID", registrationInfo!!.ListWorkContentID!!)
    }

    /* -------------------- 入力チェック -------------------- */
    private fun InputCheck(): Boolean {
        var check: Boolean = true
        var errMsg: String = ""
        // 作業者
        // 作業者リストの作成
        val workerList: String = registrationInfo!!.ListWorker!!
        val type: Type = object : TypeToken<java.util.ArrayList<Worker>>() {}.type
        val workers: ArrayList<Worker> = Gson().fromJson(workerList, type)
        for (i in 0..workers.size - 1) {
            if (workers[i].UserName == "") {
                check = false
                errMsg = context!!.getString(R.string.report_worker_name_nothing)
                break
            }
        }
        // 工程
        if (errMsg == "" && registrationInfo!!.ListWorkContentID!! == "") {
            check = false
            errMsg = context!!.getString(R.string.report_content_not_select)
        }
        // メッセージがあるなら表示
        if (!check) {
            ShowMessages().Show(context, errMsg)
        }
        return check
    }

    /* -------------------- 通信処理 -------------------- */
    private fun GetContentListAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val content_array = result.getJSONArray("Contents")
                        listContent.clear()
                        for (i in 0..content_array.length() - 1) {
                            val content_row = content_array.getJSONObject(i)
                            val content = Content()
                            content.ContentID = content_row.getString("ContentID")
                            content.ContentName = content_row.getString("ContentName")
                            content.WorkStop = content_row.getInt("WorkStop")
                            listContent.add(content)
                        }
                        SetListReportSchedule2()
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.TaskID = registrationInfo!!.TaskID
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ContentsURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        log(context?.javaClass!!.name, url + jsonParams)
    }
}

