package fois.dailyreportsystem.activity.report

// 作成日：2017/08/29
// 更新日：2020/11/14 岩田

import android.Manifest
import android.app.Activity
import android.app.AlertDialog
import android.content.Intent
import android.content.pm.PackageManager
import android.database.Cursor
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.viewpager.widget.ViewPager
import com.google.gson.Gson
import fois.dailyreportsystem.BuildConfig
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.ImageAdapter
import fois.dailyreportsystem.data.PhotoThumbnailList
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.photo_detail_layout.*
import org.json.JSONObject
import java.io.File
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.util.*

// 写真詳細画面のアクティビティ
class PhotoDetailActivity : BaseActivity() {

    private val REQUEST_GALLERY = 1

    // 変数宣言
    private var imagePath: String? = null            // 撮影した写真のパス
    private var imageUpload: Boolean = false        // 写真アップロードフラグ
    private var dataGetEnd: Boolean = false         // 通信終了フラグ
    private var PhotoID: String? = null                // 写真
    private var photoTaken: Boolean = false            // 写真を撮影フラグ
    private var selectUploadStart: Boolean = true    // 選択した写真のアップロード開始
    private var uploadCount: Int = 0                    // 選択した写真のアップロードした枚数
    private var uploadErrorCount: Int = 0            // 選択した写真のアップロード失敗した枚数
    private var selectWhich: Int = 0                    // サムネイルで選択した写真の場所
    private var photoExists: Boolean = false            // 写真が存在するか

    private var Path: String? = null

    private var listPhotoDetailList: ArrayList<PhotoThumbnailList> = ArrayList()
    private var listPhotoDetailBitmap: ArrayList<Bitmap> = ArrayList()
    private var listSelectedPass: ArrayList<String> = ArrayList()        // ギャラリーから選択した写真のパス

    // キャッシュ用
    private var dir: File? = null
    private lateinit var fileList: Array<File>
    private var cacheError: Boolean = false            // キャッシュに正常に登録されていたか

    private val TEMP_FILE_NAME = "survey"

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.photo_detail_layout)

        dir = context!!.cacheDir
        fileList = dir!!.listFiles()

        if (savedInstanceState == null) {
            if (intent.extras != null) {
                if (intent.extras!!.getString(MediaStore.EXTRA_OUTPUT) != null) {
                    imagePath = intent.extras!!.getString(MediaStore.EXTRA_OUTPUT)
                } else if (intent.getStringArrayListExtra("selectPhoto") != null) {
                    listSelectedPass = (intent.getStringArrayListExtra("selectPhoto") ?: return)
                } else {
                    selectWhich = intent.getIntExtra("selectWhich", 0)
                }
            }
        } else {
            imagePath = this.settings.getString("EXTRA_OUTPUT")
            this.settings.put("EXTRA_OUTPUT", "")
        }

        if (Build.VERSION.SDK_INT > 23) {
            // Android7からフォルダ構成変更
            Path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).toString() + "/temp_image.jpg"
        } else {
            Path = Environment.getExternalStorageDirectory().toString() + "/temp_image.jpg"
        }

        // クリックイベント
        // 戻るボタンタップ
        back_arrow.setOnClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            Back()
        }
        // 削除ボタンタップ
        photo_delete.setOnClickListener {
            photo_delete.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
            DeletePhoto()
        }
        // カメラボタンタップ
        photo_detail_footer_camera.setOnClickListener {
            photo_detail_footer_camera_icon.setImageResource(R.drawable.camera_white_on)
            photo_detail_footer_camera_text.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
            TakePhoto()
        }
        // カメラロールボタンタップ
        photo_detail_footer_gallery.setOnClickListener {
            photo_detail_footer_gallery_icon.setImageResource(R.drawable.album_on)
            photo_detail_footer_gallery_text.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
            SelectPhoto()
        }
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        // 邸名表示
        toolbar_title.text =this.settings.getString("TASK_NAME")

        // タイトル表示
        photo_detail_region_content_name.text = this.settings.getString("DETAIL_CONTENT")

        if (photoTaken) {
            if(clickPosition == null) {
                clickPosition = true
                val intent: Intent = Intent(applicationContext, PhotoDetailActivity::class.java)
                intent.putExtra(MediaStore.EXTRA_OUTPUT, Path)
                startActivity(intent)
                finish()
                overridePendingTransition(0, 0)
            }
        } else if (imagePath != null && imagePath != "" && !imageUpload) {
            imageUpload = true

            // 画像ファイルを保存する
            imageSaveCheck()

            // 写真のアップロード
            ImageUploadAsync()


        } else if (listSelectedPass.size != 0 && !imageUpload) {
            imageUpload = true
            for (i in 0..listSelectedPass.size-1) {
                imagePath = listSelectedPass[i]
                // 写真のアップロード
                SelectImageUploadAsync(listSelectedPass[i])
            }
        } else {

            // 現在の時間を設定ファイルに保存
            this.settings.put("LAST_HANDLE", System.currentTimeMillis())
            GetPhotoDetailAsync()
        }

        photo_detail_footer_camera_icon.setImageResource(R.drawable.camera_white)
        photo_detail_footer_camera_text.setTextColor(ContextCompat.getColor(this, R.color.white))
        photo_detail_footer_gallery_icon.setImageResource(R.drawable.album)
        photo_detail_footer_gallery_text.setTextColor(ContextCompat.getColor(this, R.color.white))
    }

    override fun onRestart() {
        super.onRestart()
        // スリープから戻った時リストを空にする
        dataGetEnd = false
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (resultCode) {
            Activity.RESULT_CANCELED -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    var file = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).toString() + "/temp_image.jpg")
                    if (file.exists()) {
                        photoTaken = true
                    } else {
                        log("camera", "cancel")
                    }
                } else {
                    log("camera", "cancel")
                }
            }
            Activity.RESULT_OK -> {
                log("camera", "photo taken")

                if(requestCode == REQUEST_GALLERY) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                        val uriList = mutableListOf<Uri>()
                        val itemCount = data?.clipData?.itemCount ?: 0
                        if(itemCount == 0){
                            data?.data?.let { uriList.add(it) }
                        }else{
                            // 件数を絞る
                            if(itemCount > 5){
                                ShowMessages().Show(context, (context?:this).getString(R.string.photo_selected_over))
                                return
                            }
                            for (i in 0 until itemCount) {
                                data?.clipData?.getItemAt(i)?.uri?.let { uriList.add(it) }
                            }
                        }
                        listSelectedPass.clear()
                        for (i in 0 until uriList.size) {
                            uriList[i].let {
                                val cursor: Cursor? = contentResolver.query(it, null, null, null, null)
                                if (cursor != null) {
                                    if (cursor.moveToFirst()) {
                                        val pathIndex: Int = cursor.getColumnIndex(MediaStore.Images.Media.DATA)
                                        listSelectedPass.add(cursor.getString(pathIndex))
                                    }
                                    cursor.close();
                                }
                            }
                        }
                        if(listSelectedPass.size > 0) {
                            val intent: Intent = Intent(applicationContext, PhotoDetailActivity::class.java)
                            intent.putStringArrayListExtra("selectPhoto", listSelectedPass)
                            startActivity(intent)
                            finish()
                            overridePendingTransition(0, 0)
                        }
                    }
                }else{
                    photoTaken = true
                }
            }
        }
    }

    /* -------------------- 画面遷移 -------------------- */

    // 撮影項目一覧、サムネイル一覧に戻る
    private fun Back() {
        if(clickPosition == null) {
            clickPosition = true
            val intent: Intent
            if (this.settings.getString("PHOTO_MODE") == "thumbnail") {
                intent = Intent(application, PhotoThumbnailListActivity::class.java)
            } else {
                intent = Intent(application, PhotoShootListActivity::class.java)
            }
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
        }
    }

    // ギャラリー起動
    private fun OpenGallery() {
        if(clickPosition == null) {
            clickPosition = true

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
                intent.type = "image/jpeg"
                intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
                startActivityForResult(intent, REQUEST_GALLERY)
            }else{
                val intent: Intent = Intent(application, PhotoGalleryActivity::class.java)
                startActivity(intent)
                finish()
            }
        }
    }

    /* -------------------- タップイベント -------------------- */
    // 端末の戻るボタン
    override fun onBackPressed() {
        Back()
    }

    // カメラボタン
    private fun TakePhoto() {
        // データ取得が終わっていたら権限確認
        if (dataGetEnd) {
            checkPermissions()

            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED ||
                (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU &&
                        ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED)) {
                return
            }
            // 権限がある場合、カメラ起動
            StartCamera().LaunchCamera(context!!.getString(R.string.photo_detail_activity))
        }
    }

    // カメラロールボタン
    private fun SelectPhoto() {
        // データ取得が終わっていたら権限確認
        if (dataGetEnd) {
            checkPermissions()

            // ストレージの権限がある場合はギャラリー起動
            OpenGallery()
        }
    }

    // 削除ボタン
    private fun DeletePhoto() {
        // データ取得が終わっていたら写真削除
        if (dataGetEnd && photoExists) {
            val builder: AlertDialog.Builder = AlertDialog.Builder(this)
            builder.setTitle(context?.getString(R.string.photo_delete))
            builder.setMessage(context?.getString(R.string.photo_delete_text))
            builder.setPositiveButton(context?.getString(R.string.dialog_positive),
                    { dialog, which ->
                        DeletePhotoAsync()
                    })
            builder.setNegativeButton(context?.getString(R.string.dialog_negative),
                    { dialog, which ->
                        photo_delete.setTextColor(ContextCompat.getColor(this, R.color.white))
                    })
            builder.setCancelable(false)
            val alertDialog: AlertDialog = builder.create()
            alertDialog.show()
        }
    }

    /* -------------------- 画像イベント -------------------- */
    // 画像がキャッシュに存在するかチェック
    private fun ImageCheck() {
        var last: Boolean = false
        for (i in 0 until listPhotoDetailList.size) {
            if (fileList.isNotEmpty()) {
                for (j in 0..fileList.size - 1) {
                    if (fileList[j].toString().substring(fileList[j].toString().lastIndexOf("/") + 1) == listPhotoDetailList[i].CacheFileName + "Large") {
                        // キャッシュに画像が保存されている場合
                        try {
                            // キャッシュから読み込んで配列に保存
                            val fileInputStream: FileInputStream = FileInputStream(fileList[j])
                            val bitmap: Bitmap = BitmapFactory.decodeStream(fileInputStream)
                            listPhotoDetailBitmap[i] = bitmap
                            if (i == listPhotoDetailList.size - 1) {
                                // 最後までキャッシュから読み込めた場合画像をセット
                                ImageSet()
                                dataGetEnd = true
                            }
                        } catch (e: FileNotFoundException) {
                            e.printStackTrace()
                        } catch (e: Exception) {
                            // 正常にキャッシュに登録されていないとき
                            val file: File = File(context!!.cacheDir, listPhotoDetailList[i].CacheFileName + "Large")
                            if (i == listPhotoDetailList.size - 1) {
                                last = true
                            }
                            // 画像を取得
                            GetImageAsync(listPhotoDetailList[i].LargePhotoUrl!!, i, file, last)
                            // キャッシュに異常あり（破損データあり）
                            cacheError = true
                        }
                        break
                    }
                    if (j == fileList.size - 1) {
                        // キャッシュに画像が保存されていない場合、通信して取得
                        val file: File = File(context!!.cacheDir, listPhotoDetailList[i].CacheFileName + "Large")
                        if (i == listPhotoDetailList.size - 1) {
                            last = true
                        }
                        // 画像を取得
                        GetImageAsync(listPhotoDetailList[i].LargePhotoUrl!!, i, file, last)
                    }
                }
            } else {
                // キャッシュに画像が保存されていない場合、通信して取得
                val file: File = File(context!!.cacheDir, listPhotoDetailList[i].CacheFileName + "Large")
                if (i == listPhotoDetailList.size - 1) {
                    last = true
                }
                // 画像を取得
                GetImageAsync(listPhotoDetailList[i].LargePhotoUrl!!, i, file, last)
            }
        }
        dataGetEnd = true
    }

    // 画像をスライドにセット
    private fun ImageSet() {
        if (selectWhich == 0) {
            selectWhich = listPhotoDetailList.size
        }
        // 初期表示
        // 画像のセット
        val itemAdapter = ImageAdapter(this, listPhotoDetailBitmap)
        view_pager.adapter = itemAdapter
        // 写真を表示
        view_pager.setCurrentItem(selectWhich-1, false)

        // 現在の写真位置の表示
        if (listPhotoDetailList[selectWhich-1].PhotoID != "" && listPhotoDetailList[selectWhich-1].PhotoID != null) {
            photo_detail_count.text = selectWhich.toString() + "/" + listPhotoDetailList.size.toString()
            photo_delete.setTextColor(ContextCompat.getColor(this, R.color.white))
            photoExists = true
        } else {
            photo_detail_count.text = "0/0"
            // 削除ボタンを非活性化
            photo_delete.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
            photoExists = false
        }
        PhotoID = listPhotoDetailList[selectWhich-1].PhotoID
        // 写真の日付、時間表示
        photo_detail_date.text = listPhotoDetailList[selectWhich-1].PhotoDate + " " + listPhotoDetailList[selectWhich-1].PhotoTime
        // 撮影者名の表示
        photo_detail_take_user.text = listPhotoDetailList[selectWhich-1].WorkerName

        // 写真スライド時の表示
        view_pager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageSelected(position: Int) {
                // 現在の写真位置の表示
                photo_detail_count.text = (position + 1).toString() + "/" + listPhotoDetailList.size.toString()
                PhotoID = listPhotoDetailList[position].PhotoID
                // 写真の日付、時間表示
                photo_detail_date.text = listPhotoDetailList[position].PhotoDate + " " + listPhotoDetailList[position].PhotoTime
                // 撮影者名の表示
                photo_detail_take_user.text = listPhotoDetailList[position].WorkerName
                photoExists = listPhotoDetailList[selectWhich-1].PhotoID != "" && listPhotoDetailList[selectWhich-1].PhotoID != null
            }

            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {}

            override fun onPageScrollStateChanged(state: Int) {}
        })
    }


    /**
     * imageSaveCheck
     * @param imageUrl 画像パス
     */
    private fun imageSaveCheck(){
        try {
            // 設定で画像を保存するを選択している場合
            if (this.settings.getBoolean("PHOTO_SAVE")) {
                val bitmap: Bitmap = BitmapFactory.decodeFile(imagePath)

                val filepath = TEMP_FILE_NAME + "_" + System.currentTimeMillis()

                this.setGallery(bitmap, filepath) { uri ->

                    //キャッシュにファイルを作成
                    val cacheFile = this.getUriToPath(uri) ?: return@setGallery

                }
            }

        } catch (ex: Exception) {
            ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
            log((context?.javaClass ?: return).name, "$ex")
        }
    }

    // 写真をキャッシュに保存するか判別
    private fun SaveCachePhotoCheck() {
        for (i in 0..listPhotoDetailList.size-1) {
            var cacheExists: Boolean = false
            for (j in 0..fileList.size-1) {
                if (fileList[j].toString().substring(fileList[j].toString().lastIndexOf("/")+1) == listPhotoDetailList[i].CacheFileName+"Large") {
                    cacheExists = true
                }
                if (j == fileList.size-1) {
                    if (cacheError || !cacheExists) {
                        // キャッシュに異常があった場合
                        // 又は、キャッシュに画像が保存されていない場合、キャッシュに保存
                        SaveCachePhotoAsync(listPhotoDetailList[i].CacheFileName + "Large", listPhotoDetailBitmap[i])
                    }
                }
            }
        }
    }

    // 非同期でキャッシュに保存
    private fun SaveCachePhotoAsync(photoName: String, bitmap: Bitmap) {
        val async: AsyncPhotoSaver = AsyncPhotoSaver(context!!, photoName, bitmap)
        async.execute()
    }

    /* -------------------- 通信処理 -------------------- */
    // 写真詳細データ取得（画像はまだ取得しない）
    private fun GetPhotoDetailAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 成功時
                        listPhotoDetailList.clear()
                        listPhotoDetailBitmap.clear()
                        val data = result.getJSONArray("Photos")
                        for (i in 0..data.length()-1) {
                            val data_row = data.getJSONObject(i)
                            val photoDetailList = PhotoThumbnailList()
                            photoDetailList.CacheFileName = data_row.getString("CacheFileName")
                            photoDetailList.Comment = data_row.getString("Comment")
                            photoDetailList.CompanyID = data_row.getString("CompanyID")
                            photoDetailList.ContentID = data_row.getString("ContentID")
                            photoDetailList.ContentName = data_row.getString("ContentName")
                            photoDetailList.LargePhotoUrl = data_row.getString("LargePhotoUrl")
                            photoDetailList.Manager = data_row.getString("Manager")
                            photoDetailList.PhotoDate = data_row.getString("PhotoDate")
                            photoDetailList.PhotoID = data_row.getString("PhotoID")
                            photoDetailList.PhotoTime = data_row.getString("PhotoTime")
                            photoDetailList.RegionID = data_row.getString("RegionID")
                            photoDetailList.RegionName = data_row.getString("RegionName")
                            photoDetailList.SmallPhotoUrl = data_row.getString("SmallPhotoUrl")
                            photoDetailList.TaskID = data_row.getString("TaskID")
                            photoDetailList.WorkID = data_row.getString("WorkID")
                            photoDetailList.WorkerName = data_row.getString("WorkerName")
                            listPhotoDetailList.add(photoDetailList)
                            listPhotoDetailBitmap.add(BitmapFactory.decodeResource(resources, R.drawable.transparent))
                            if (data_row.getString("PhotoID") == "") {
                                photoExists = false
                            }
                        }
                    } else {
                        // 失敗時
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
                if (listPhotoDetailList.size > 0) {
                    ImageCheck()
                } else {
                    // 全ての通信終了
                    dataGetEnd = true
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.TaskID = this.settings.getString("TASK_ID")
        params.RegionID = this.settings.getString("REGION_ID")
        params.ContentID = this.settings.getString("CONTENT_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.PhotosURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    // 画像を取得
    private fun GetImageAsync(url: String, position: Int, file: File, last: Boolean) {
        val asyncBitmapLoader = AsyncBitmapLoader(object : AsyncBitmapLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {
                if (last) {
                    showHub()
                }
            }

            // 実行後
            override fun postExecute(result: Bitmap?) {
                if (result == null) {
                    hideHub()
                    //ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    ImageSet()
                    return
                }
                try {
                    // 取得した画像を配列に保存
                    listPhotoDetailBitmap[position] = result
                    // 最後の場合は画像をセット
                    if (last) {
                        ImageSet()
                        dataGetEnd = true
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                } finally {
                    if (last) {
                        hideHub()
                        // 画像をキャッシュに保存
                        SaveCachePhotoCheck()
                    }
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // 非同期通信開始
        asyncBitmapLoader.execute(url)

        log(context?.javaClass!!.name, url + "{" + file.toString() + "}")
    }

    // 写真をアップロード（カメラから）
    private fun ImageUploadAsync() {

        val rotation: String = this.getImageRotation(imagePath!!)
        val params: Dictionary<String, String> = Hashtable<String, String>()
        params.put("UserID", this.settings.getString("USER_ID"))
        params.put("TaskID", this.settings.getString("TASK_ID"))
        params.put("RegionID", this.settings.getString("REGION_ID"))
        params.put("ContentID", this.settings.getString("CONTENT_ID"))
        params.put("Comment", "コメント")
        params.put("Rotation", rotation)


        val asyncImageUploader = AsyncImageUploader(this, object : AsyncImageUploader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 成功時
                    } else {
                        // 失敗時
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                    // アップロードが終わったら写真詳細を取得
                    GetPhotoDetailAsync()
                    var file: File?
                    if (Build.VERSION.SDK_INT > 23) {
                        // Android7以降は読み込み先が異なる
                        file = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).toString() + "/temp_image.jpg")
                    } else {
                        // Android6以前
                        file = File(Environment.getExternalStorageDirectory().toString() + "/temp_image.jpg")
                    }
                    if(file.exists()){
                        file.delete()
                    }

                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        }, params)

        // URL作成
        val url: String = Constant.URL + Constant.AddPhotoURL

        // 非同期通信開始
        // 第一引数：URL、第二～七引数：パラメータ、第八引数：画像URL
        asyncImageUploader.execute(url, imagePath, rotation)

        log("AddPhoto", url)
    }

    // 写真をアップロード（ギャラリーから）
    private fun SelectImageUploadAsync(imagePath: String, uri: Uri? = null) {
        val rotation = if(uri == null) {
            this.getImageRotation(imagePath)
        }else{
            this.getImageRotation(uri, imagePath)
        }
        val params: Dictionary<String, String> = Hashtable<String, String>()
        params.put("UserID", this.settings.getString("USER_ID"))
        params.put("TaskID", this.settings.getString("TASK_ID"))
        params.put("RegionID", this.settings.getString("REGION_ID"))
        params.put("ContentID", this.settings.getString("CONTENT_ID"))
        params.put("Comment", "コメント")
        params.put("Rotation", rotation)

        val asyncImageUploader = AsyncImageUploader(this, object : AsyncImageUploader.AsyncCallback {
            // 実行前
            override fun preExecute() {
                if (selectUploadStart) {
                    showHub()
                    selectUploadStart = false
                }
            }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    uploadErrorCount++
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 成功時
                        uploadCount++
                    }
                } catch (ex: Exception) {
                    uploadErrorCount++
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    if (uploadCount + uploadErrorCount == listSelectedPass.size) {
                        if (uploadCount == listSelectedPass.size) {
                            //ShowMessages().Show(context, context!!.getString(R.string.photo_registered))
                        } else {
                            ShowMessages().Show(context, context!!.getString(R.string.photo_registered_error))
                        }

                        hideHub()
                        // アップロードが終わったら写真詳細を取得
                        GetPhotoDetailAsync()
                    }
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        }, params)

        // URL作成
        val url: String = Constant.URL + Constant.AddPhotoURL

        // 非同期通信開始
        // 第一引数：URL、第2引数：画像URL、第3引数：画像向き
        asyncImageUploader.execute(url, imagePath, rotation)
        log("AddPhoto", url)
    }

    // 写真の削除
    private fun DeletePhotoAsync() {

        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                var success: Boolean = false
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 成功時
                        success = true
                    } else {
                        // 失敗時
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
                if (success) {
                    selectWhich = 0
                    GetPhotoDetailAsync()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.TaskID = this.settings.getString("TASK_ID")
        params.PhotoID = PhotoID

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.DeletePhotoURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}
