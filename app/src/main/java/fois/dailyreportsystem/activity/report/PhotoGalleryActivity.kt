package fois.dailyreportsystem.activity.report

// 作成日：2017/11/06
// 更新日：2018/02/13

import android.content.ContentResolver
import android.content.ContentUris
import android.content.Intent
import android.database.Cursor
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.BitmapFactory.*
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.util.Size
import androidx.core.content.ContextCompat
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.PhotoGallery
import fois.dailyreportsystem.data.PhotoGalleryAdapter
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.photo_gallery_layout.*
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList
import kotlin.concurrent.thread

// 写真選択（カメラロール）画面のアクティビティ
class PhotoGalleryActivity : BaseActivity() {

    // 変数宣言
    private var listPhotoGallery: ArrayList<PhotoGallery> = ArrayList()
    private var listPhotoPass: ArrayList<String> = ArrayList()
    private var selectPhotoPass: ArrayList<String> = ArrayList()

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.photo_gallery_layout)

        // クリックイベント
        // 戻るボタンタップ
        back_arrow.setOnClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            Back()
        }

        // 完了ボタンタップ
        complete.setOnClickListener {
            complete.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
            SelectComplete()
        }
    }

    override fun onResume() {
        super.onResume()

        // 選択状態の番号をリセット
        this.settings.put("SELECT_INDEX", "")

        // 端末の画像を取得
        GetPhoto()
    }

    override fun onRestart() {
        super.onRestart()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }
    }

    /* -------------------- 画面遷移 -------------------- */
    // 写真詳細に戻る
    private fun Back() {
        if(clickPosition == null) {
            clickPosition = true
            val intent: Intent = Intent(application, PhotoDetailActivity::class.java)
            if (selectPhotoPass.size != 0) {
                intent.putStringArrayListExtra("selectPhoto", selectPhotoPass)
            }
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
        }
    }

    /* -------------------- タップイベント -------------------- */
    // 端末の戻るボタン
    override fun onBackPressed() {
        Back()
    }

    // 完了ボタン
    private fun SelectComplete() {
        val selectIndex: String = this.settings.getString("SELECT_INDEX")
        if (selectIndex == "") {
            // 未選択
            ShowMessages().Show(context, context!!.getString(R.string.photo_unselected))
            complete.setTextColor(ContextCompat.getColor(this, R.color.white))
        } else {
            if (selectIndex.indexOf(",") == -1) {
                // 選択1件のみ
                selectPhotoPass.add(listPhotoPass[selectIndex.toInt()])
            } else {
                // 複数選択
                val str: List<String> = selectIndex.split(",")
                for (i in 0..str.size-1) {
                    selectPhotoPass.add(listPhotoPass[str[i].toInt()])
                }
            }
            // 写真詳細へ戻る
            Back()
        }
    }

    /* -------------------- 画像イベント -------------------- */
    // 端末に保存されている画像を取得する
    private fun GetPhoto() {

        listPhotoPass.clear()
        listPhotoGallery.clear()

        val query = contentResolver.query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                arrayOf(MediaStore.Images.Media._ID, MediaStore.Images.Media.DATE_ADDED, MediaStore.Images.Media.DATA),
                null,
                null,
                "${MediaStore.Images.Media.DATE_ADDED} DESC LIMIT 500"
        )

            query?.use { cursor ->

                val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID)

                while (cursor.moveToNext()) {
                    val id = cursor.getLong(idColumn)
                    val contentUri: Uri = ContentUris.withAppendedId( MediaStore.Images.Media.EXTERNAL_CONTENT_URI, id )

                    val thumbnail: Bitmap = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        contentResolver.loadThumbnail(contentUri, Size(270,270), null)
                    } else {
                        MediaStore.Images.Thumbnails.getThumbnail(contentResolver, id, MediaStore.Images.Thumbnails.MICRO_KIND, null)
                    }
                    PhotoGallery().let{
                        it.bitmap = thumbnail
                        listPhotoGallery.add(it)
                    }

                    val pathIndex: Int = cursor.getColumnIndex(MediaStore.Images.Media.DATA)
                    listPhotoPass.add(cursor.getString(pathIndex))
                }
            }
            // 画像をセット
            ImageSet()

        /*

        val uri: Uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
        val cursor: Cursor? = contentResolver.query(uri, null, null, null, null)
        if (cursor != null ) {
            cursor.moveToFirst()
            listPhotoPass.clear()
            listPhotoGallery.clear()
            try {
                if (!cursor.isAfterLast) {
                        do {
                            // 画像のパスを取得
                            val pathIndex: Int = cursor.getColumnIndex(MediaStore.Images.Media.DATA)

                            val id: Long = cursor.getLong(cursor.getColumnIndexOrThrow("_id"))
                            // サムネイルを作成
                            val bitmap: Bitmap = MediaStore.Images.Thumbnails.getThumbnail(contentResolver, id, MediaStore.Images.Thumbnails.MICRO_KIND, null)

                            val photoGallery: PhotoGallery = PhotoGallery()
                            photoGallery.bitmap = bitmap

                            listPhotoPass.add(cursor.getString(pathIndex))
                            listPhotoGallery.add(photoGallery)
                        } while (cursor.moveToNext())
                }
            } catch (e: Exception) {
                e.stackTrace
            }

            listPhotoPass.reverse()
            listPhotoGallery.reverse()

            cursor.close()

            // 画像をセット
            ImageSet()
        }*/

    }

    private fun ImageSet() {
        val photoGalleryAdapter: PhotoGalleryAdapter = PhotoGalleryAdapter(this, listPhotoGallery)
        grid_gallery.adapter = photoGalleryAdapter
    }
}