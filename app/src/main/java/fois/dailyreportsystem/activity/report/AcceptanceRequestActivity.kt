package fois.dailyreportsystem.activity.report

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.CompleteWorkProcessInfo
import fois.dailyreportsystem.data.RegistrationInfo
import fois.dailyreportsystem.data.WorkProcess
import fois.dailyreportsystem.data.adapter.WorkProcessAdapter
import fois.dailyreportsystem.util.AsyncJsonLoader
import fois.dailyreportsystem.util.Constant
import fois.dailyreportsystem.util.ConstantParameters
import fois.dailyreportsystem.util.DateUtil
import fois.dailyreportsystem.util.SendParameter
import fois.dailyreportsystem.util.ShowMessages
import fois.dailyreportsystem.util.getString
import fois.dailyreportsystem.util.loginCheck
import fois.dailyreportsystem.util.setOnSafeClickListener
import fois.dailyreportsystem.util.settings
import kotlinx.android.synthetic.main.acceptance_request_layout.register_button
import kotlinx.android.synthetic.main.acceptance_request_layout.reportDate
import kotlinx.android.synthetic.main.acceptance_request_layout.work_process_list_listview
import kotlinx.android.synthetic.main.report_list_layout.back_arrow
import kotlinx.android.synthetic.main.report_list_layout.toolbar_title
import org.json.JSONObject

class AcceptanceRequestActivity : BaseActivity() {

    var listWorkProcess: ArrayList<WorkProcess> = ArrayList()
    var itemAdapter: WorkProcessAdapter? = null
    private var reportInfo: RegistrationInfo? = null

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.acceptance_request_layout)

        // バックボタンタップ
        back_arrow.setOnSafeClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            backReportList()
        }

        // 登録ボタンタップ
        register_button.setOnSafeClickListener {
            register_button.setImageResource(R.drawable.gray_next_on)
            addWorkProcessAsync()
        }
    }

    //端末の戻るボタン
    override fun onBackPressed() {
        backReportList()
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        // 邸名の表示
        toolbar_title.text = this.settings.getString("TASK_NAME")

        // Intentパラメータ取得
        reportInfo = intent.getParcelableExtra<RegistrationInfo>(ConstantParameters.INTENT_KEY_TASKINFO)
        registrationInfo = reportInfo

        // 日報日付
        val strReportDate: String? = registrationInfo?.ReportDateDefault?.trim()
        if (!strReportDate.isNullOrEmpty() && strReportDate.length == 8) {
            reportDate.text = DateUtil.convertDateToDisplay(strReportDate)
        }

        // 日報一覧を表示
        this.getWorkProcessList()
    }

    /* -------------------- 画面遷移 -------------------- */

    // 日報一覧画面へ戻る
    private fun backReportList() {
        if(clickPosition == null) {
            clickPosition = true
            val intent = Intent(this, ReportListActivity::class.java)
            intent.putExtra(ConstantParameters.INTENT_KEY_TASKID, registrationInfo?.TaskID)
            intent.putExtra(ConstantParameters.INTENT_KEY_TASKNAME, registrationInfo?.TaskName)
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
        }
    }

    private fun addWorkProcessAsync() {
        val className = context?.javaClass?.name
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    Toast.makeText(context, context!!.getString(R.string.connect_error), Toast.LENGTH_LONG).show()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        backReportList()
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    Toast.makeText(context, context!!.getString(R.string.unexpected_error), Toast.LENGTH_LONG).show()
                    if (className != null) {
                        log(className, "$ex")
                    }
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val listWorkProcessFilter = listWorkProcess.filter { it.Selected == true }
        val jsonData: ArrayList<CompleteWorkProcessInfo> = ArrayList()
        for (i in 0 until listWorkProcessFilter.count()) {
            val dataWorkProcess: CompleteWorkProcessInfo = CompleteWorkProcessInfo()
            dataWorkProcess.ProcessID = listWorkProcessFilter[i].ProcessID.toString()
            dataWorkProcess.ProcessSubID = listWorkProcessFilter[i].ProcessSubID.toString()
            jsonData.add(dataWorkProcess)
        }

        val data: String = Gson().toJson(jsonData)

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.TaskID = this.settings.getString("TASK_ID")
        params.Date = registrationInfo?.ReportDateDefault?.trim()
        params.JsonData = data

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.CompleteWorkProcess

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        if (className != null) {
            log(className, url + jsonParams)
        }
    }

    /* -------------------- リストビューイベント -------------------- */
    private fun listSet() {
        itemAdapter = WorkProcessAdapter(this, listWorkProcess)
        work_process_list_listview.adapter = itemAdapter
        work_process_list_listview.setOnItemClickListener { _, _, position, _ ->
            val workProcess: WorkProcess = listWorkProcess[position]
            if (workProcess.ReadOnly != true) {
                workProcess.Selected = workProcess.Selected != true
                itemAdapter?.notifyDataSetChanged()
            }
        }
    }

    /* -------------------- 通信処理 -------------------- */
    private fun getWorkProcessList() {
        val className = context?.javaClass?.name
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val workProcessArray = result.getJSONArray("Process")
                        listWorkProcess.clear()
                        for (i in 0 until workProcessArray.length()) {
                            val workProcessRow = workProcessArray.getJSONObject(i)
                            val workProcess = WorkProcess()
                            workProcess.DisplayOrder = workProcessRow.getInt("DisplayOrder")
                            workProcess.ProcessID = workProcessRow.getInt("ProcessID")
                            workProcess.ProcessName = workProcessRow.getString("ProcessName")
                            workProcess.ProcessSubID = workProcessRow.getString("ProcessSubID")
                            workProcess.ProcessSubName = workProcessRow.getString("ProcessSubName")
                            workProcess.ReadOnly = workProcessRow.getBoolean("ReadOnly")
                            workProcess.Selected = workProcessRow.getBoolean("Selected")

                            listWorkProcess.add(workProcess)
                        }
                        listSet()
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    if (className != null) {
                        log(className, "$ex")
                    }
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.TaskID = registrationInfo?.TaskID
        params.Date = registrationInfo?.ReportDateDefault

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.WorkProcess

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        if (className != null) {
            log(className, url + jsonParams)
        }
    }
}