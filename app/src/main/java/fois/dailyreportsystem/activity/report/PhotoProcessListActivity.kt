package fois.dailyreportsystem.activity.report

// 作成日：2017/10/10
// 更新日：2018/02/13

import android.content.Intent
import android.os.Bundle
import android.view.MotionEvent
import android.widget.AdapterView
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.PhotoProcessList
import fois.dailyreportsystem.data.adapter.PhotoProcessListAdapter
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.photo_process_list_layout.*
import org.json.JSONObject

// 工程一覧画面のアクティビティ
class PhotoProcessListActivity : BaseActivity() {

    // 変数宣言
    val listPhotoProcessList: ArrayList<PhotoProcessList> = ArrayList()

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.photo_process_list_layout)

        // クリックイベント
        // 戻るボタンタップ
        back_arrow.setOnClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            BackMain()
        }
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            // 初期表示位置のリセット
            this.position.put("PHOTO_PROCESS_LIST1", 0)
            this.position.put("PHOTO_PROCESS_LIST2", 0)
            moveLogin()
            return
        }

        // 邸名の表示
        toolbar_title.text = this.settings.getString("TASK_NAME")

        // 工程一覧の取得
        getProcessListAsync()
    }

    override fun onRestart() {
        super.onRestart()
        // スリープから戻った時リストを空にする
        listPhotoProcessList.clear()
    }
    /* -------------------- 画面遷移 -------------------- */

    // 邸一覧画面へ戻る
    private fun BackMain() {
        if(clickPosition == null) {
            clickPosition = true
            // 初期表示位置のリセット
            this.position.put("PHOTO_PROCESS_LIST1", 0)
            this.position.put("PHOTO_PROCESS_LIST2", 0)
            finish()
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
        }
    }

    /* -------------------- リストビューイベント -------------------- */
    // リストセット
    private fun ListSet() {
        val photoProcessListAdapter: PhotoProcessListAdapter = PhotoProcessListAdapter(this, listPhotoProcessList)
        process_list_listview.adapter = photoProcessListAdapter
        // 保存した位置でリストビューを表示
        process_list_listview.setSelectionFromTop(this.position.getInt("PHOTO_PROCESS_LIST1", 0), this.position.getInt("PHOTO_PROCESS_LIST2", 0))
        process_list_listview.onItemClickListener = ClickItem
    }

    // リスト選択
    private val ClickItem = AdapterView.OnItemClickListener { parent, view, position, id ->
        if(clickPosition == null) {
            clickPosition = true
            val processList = this@PhotoProcessListActivity.process_list_listview.getItemAtPosition(position) as PhotoProcessList
            this.settings.put("REGION_ID", processList.RegionID)
            this.settings.put("REGION_NAME", processList.RegionName)
            val intent: Intent
            if (this.settings.getString("PHOTO_MODE") == "thumbnail") {
                intent = Intent(application, PhotoThumbnailListActivity::class.java)
            } else {
                intent = Intent(application, PhotoShootListActivity::class.java)
            }
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
        }
    }

    /* -------------------- タップイベント -------------------- */
    //端末の戻るボタン
    override fun onBackPressed() {
        BackMain()
    }

    // 画面タッチイベント
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        try {
            if (listPhotoProcessList.size > 0) {
                // 現在の位置を保存する
                this.position.put("PHOTO_PROCESS_LIST1", process_list_listview.firstVisiblePosition)
                this.position.put("PHOTO_PROCESS_LIST2", process_list_listview.getChildAt(0).top)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return super.dispatchTouchEvent(ev)
    }

    /* -------------------- 通信処理 -------------------- */
    private fun getProcessListAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 成功時
                        listPhotoProcessList.clear()
                        val data = result.getJSONArray("ShootingRegions")
                        for (i in 0..data.length()-1) {
                            val data_row = data.getJSONObject(i)
                            val processList = PhotoProcessList()
                            processList.ContentVersionID = data_row.getString("ContentVersionID")
                            processList.photoCount = data_row.getInt("PhotoCount")
                            processList.RegionID = data_row.getString("RegionID")
                            processList.RegionName = data_row.getString("RegionName")
                            processList.reportFlg = data_row.getInt("ReportFlg")
                            listPhotoProcessList.add(processList)
                        }
                        if (data.length() > 0) {
                            ListSet()
                        }
                    } else {
                        // 失敗時
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.TaskID = this.settings.getString("TASK_ID")
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.ProcessURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}
