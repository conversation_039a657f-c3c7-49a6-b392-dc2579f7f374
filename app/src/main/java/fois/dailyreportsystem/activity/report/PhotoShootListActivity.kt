package fois.dailyreportsystem.activity.report

// 作成日：
// 更新日：2018/02/13

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import android.view.MotionEvent
import android.view.View
import android.widget.AdapterView
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.PhotoShootList
import fois.dailyreportsystem.data.adapter.PhotoShootListAdapter
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.photo_shoot_list_layout.*
import org.json.JSONObject
import java.io.File

// 撮影項目一覧画面のアクティビティ
class PhotoShootListActivity : BaseActivity() {

    // 変数宣言
    val listPhotoShootList: ArrayList<PhotoShootList> = ArrayList()

    private var Path: String? = null
    private var photoTaken: Boolean = false     // 写真を撮影したか

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.photo_shoot_list_layout)

        // 現在のモードを登録
        this.settings.put("PHOTO_MODE", "shoot")

        // 写真の取得先
        if (Build.VERSION.SDK_INT >23) {
            Path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).toString() + "/temp_image.jpg"
        } else {
            Path = Environment.getExternalStorageDirectory().toString() + "/temp_image.jpg"
        }

        // クリックイベント
        // バックボタンタップ
        back_arrow.setOnClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            Back()
        }

        // サムネイルボタンタップ
        thumbnail_button.setOnClickListener {
            thumbnail.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
            thumbnail_image.setImageResource(R.drawable.thumbnail_on)
            ChangeThumbnailList()
        }
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        // 邸名の表示
        toolbar_title.text = this.settings.getString("TASK_NAME" )

        // 邸名の長さを判別
        val chars: CharArray = this.settings.getString("TASK_NAME" ).toCharArray()
        var taskNameWidth: Int = 0
        for (i in chars.indices) {
            if((chars[i] <= '\u007e') || (chars[i] in '\uff61'..'\uff9f')) {
                taskNameWidth += 1
            } else {
                taskNameWidth += 2
            }
        }
        if (taskNameWidth > 12) {
            // 邸名の長さが全角6文字を超えた場合、ツールバーのサムネイル文字を非表示にする
            thumbnail.visibility = View.GONE
        }

        // 工程名の表示
        photo_shoot_region.text = this.settings.getString("REGION_NAME")

        if (photoTaken) {
            val intent: Intent = Intent(applicationContext, PhotoDetailActivity::class.java)
            intent.putExtra(MediaStore.EXTRA_OUTPUT, Path)
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
        } else {
            // 撮影項目一覧の取得
            getPhotographyListAsync()
        }
    }

    override fun onRestart() {
        super.onRestart()
        // スリープから戻った時リストを空にする
        listPhotoShootList.clear()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (resultCode) {
            Activity.RESULT_CANCELED -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    val directory = File(
                        Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),
                        ""
                    )
                    val file = File(directory, "temp_image${Constant.IMAGE_FILE_EXTENSION}")
                    if (file.exists()) {
                        photoTaken = true
                    } else {
                        log("camera", "cancel")
                    }
                } else {
                    log("camera", "cancel")
                }
            }
            Activity.RESULT_OK -> {
                log("camera", "photo taken")
                photoTaken = true
            }
        }
    }

    /* -------------------- 画面遷移 -------------------- */
    // サムネールに切替
    private fun ChangeThumbnailList() {
        if(clickPosition == null) {
            clickPosition = true
            // 初期表示位置のリセット
            ListPositionReset()
            val intent: Intent = Intent(this, PhotoThumbnailListActivity::class.java)
            startActivity(intent)
            finish()
            overridePendingTransition(0, 0)
        }
    }

    // 工程一覧へ戻る
    private fun Back() {
        if(clickPosition == null) {
            clickPosition = true
            // 初期表示位置のリセット
            ListPositionReset()
            val intent: Intent = Intent(this, PhotoProcessListActivity::class.java)
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
        }
    }

    /* -------------------- リストビューイベント -------------------- */
    // リストセット
    private fun ListSet() {
        val photoShootListAdapter: PhotoShootListAdapter = PhotoShootListAdapter(this, listPhotoShootList)
        photo_shoot_list_listview.adapter = photoShootListAdapter
        // 保存した位置でリストビューを表示
        photo_shoot_list_listview.setSelectionFromTop(this.position.getInt("PHOTO_SHOOT_LIST1", 0), this.position.getInt("PHOTO_SHOOT_LIST2", 0))
        photo_shoot_list_listview.onItemClickListener = ClickItem
    }

    // リスト選択
    private val ClickItem = AdapterView.OnItemClickListener { parent, view, position, id ->
        val photographyList = this@PhotoShootListActivity.photo_shoot_list_listview.getItemAtPosition(position) as PhotoShootList
        this.settings.put("CONTENT_ID", photographyList.ContentID)
        this.settings.put("DETAIL_CONTENT", photographyList.RegionName + "-" + photographyList.ContentName)

        when (id) {
            R.id.photo_shoot_camera.toLong() -> {
                PermissionCheckCamera()
            }
            else -> {
                val intent: Intent = Intent(application, PhotoDetailActivity::class.java)
                startActivity(intent)
                finish()
                overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
            }
        }
    }

    // リスト位置のリセット
    private fun ListPositionReset() {
        // 初期表示位置のリセット
        this.position.put("PHOTO_SHOOT_LIST1", 0)
        this.position.put("PHOTO_SHOOT_LIST2", 0)
    }

    // リストのカメラタップ、権限確認
    private fun PermissionCheckCamera() {
        checkPermissions()

        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED ||
            (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU &&
                    ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED)) {
            return
        }

        // 権限がある場合、カメラ起動
        StartCamera().LaunchCamera(context!!.getString(R.string.photo_shoot_list_activity))
    }

    /* -------------------- タップイベント -------------------- */
    //端末の戻るボタン
    override fun onBackPressed() {
        Back()
    }

    // 画面タッチイベント
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        try {
            if (listPhotoShootList.size > 0) {
                // 現在の位置を保存する
                this.settings.put("PHOTO_SHOOT_LIST1", photo_shoot_list_listview!!.firstVisiblePosition)
                this.settings.put("PHOTO_SHOOT_LIST2", photo_shoot_list_listview!!.getChildAt(0).top)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return super.dispatchTouchEvent(ev)
    }

    /* -------------------- 通信処理 -------------------- */
    // 撮影項目一覧の取得
    private fun getPhotographyListAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 成功時
                        listPhotoShootList.clear()
                        val data = result.getJSONArray("ShootingRegions")
                        val regions = data.getJSONObject(0)
                        val data_content = regions.getJSONArray("ShootingContent")
                        for (i in 0..data_content.length()-1) {
                            val data_row = data_content.getJSONObject(i)
                            val photographyList = PhotoShootList()
                            photographyList.RegionName = regions.getString("RegionName")
                            photographyList.ContentID = data_row.getString("ContentID")
                            photographyList.ContentName = data_row.getString("ContentName")
                            photographyList.ContentVersionID = data_row.getString("ContentVersionID")
                            photographyList.photoCount = data_row.getInt("PhotoCount")
                            photographyList.reportFlg = data_row.getInt("ReportFlg")
                            listPhotoShootList.add(photographyList)
                        }
                        if (data_content.length() > 0) {
                            ListSet()
                        }
                    } else {
                        // 失敗時
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.TaskID = this.settings.getString("TASK_ID")
        params.RegionID =this.settings.getString("REGION_ID")
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.PhotographyURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}