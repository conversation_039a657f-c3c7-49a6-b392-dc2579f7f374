package fois.dailyreportsystem.activity.report

// 作成日：2017/08/18
// 更新日：2018/04/06

import android.os.Bundle
import android.view.MotionEvent
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.Report
import fois.dailyreportsystem.data.ReportListAdapter
import fois.dailyreportsystem.data.WorkSchedule
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.report_list_layout.*
import org.json.JSONObject

// 日報一覧画面のアクティビティ
class ReportListActivity : BaseActivity() {

    var listReport: ArrayList<Report> = ArrayList()
    var itemAdapter: ReportListAdapter? = null
    private var taskID: String? = null
    private var taskName: String? = null

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.report_list_layout)

        // クリックイベント
        // 戻るボタンタップ
        back_arrow.setOnSafeClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            BackMain()
        }
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        // TaskID, TaskNameを取得
        taskID = this.settings.getString("TASK_ID")
        taskName = this.settings.getString("TASK_NAME")


        // ツールバーの邸名を設定
        toolbar_title.text = taskName

        // 日報一覧を表示
        this.GetReportList()

        // 日報一時保存ファイルのクリア
        context!!.report.clear()
    }

    /* -------------------- 画面遷移 -------------------- */

    // 邸一覧画面へ戻る
    private fun BackMain() {
        if(clickPosition == null) {
            clickPosition = true
            finish()
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
        }
    }

    /* -------------------- タップイベント -------------------- */
    // 画面タッチイベント
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        try {
            if (listReport.size > 0) {
                // 現在のビューの位置を保存する
                this.position.put("REPORT_LIST1", report_list_listview.firstVisiblePosition)
                this.position.put("REPORT_LIST2", report_list_listview.getChildAt(0).top)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return super.dispatchTouchEvent(ev)
    }

    //端末の戻るボタン
    override fun onBackPressed() {
        BackMain()
    }

    /* -------------------- リストビューイベント -------------------- */
    private fun ListSet() {
        itemAdapter = ReportListAdapter(this, listReport, taskID, taskName)
        report_list_listview.adapter = itemAdapter
        report_list_listview.setSelectionFromTop(this.position.getInt("REPORT_LIST1", 0),this.position.getInt("REPORT_LIST2", 0))
    }

    /* -------------------- 通信処理 -------------------- */
    // 通信処理
    private fun GetReportList() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val report_array = result.getJSONArray("WorkReports")
                        listReport.clear()
                        for (i in 0..report_array.length() - 1) {
                            val report_row = report_array.getJSONObject(i)
                            val report = Report()
                            report.ReportDate = report_row.getString("ReportDate")
                            report.WorkComplete = report_row.getString("WorkComplete")
                            report.ShowCompleteButton = report_row.getString("ShowCompleteButton")
                            report.WorkResultID = report_row.getString("WorkResultID")
                            report.WorkResultStatus = report_row.getInt("WorkResultStatus")
                            report.WorkScheduleID = report_row.getString("WorkScheduleID")
                            report.WorkScheduleStatus = report_row.getInt("WorkScheduleStatus")
                            report.ContentNames = report_row.getString("ContentNames")

                            if (report.WorkScheduleStatus == 2) {
                                val schedule = report_row.getJSONObject("WorkSchedule")
                                val workScheduleDetail: WorkSchedule = WorkSchedule()
                                workScheduleDetail.Comment = schedule.getString("Comment")
                                workScheduleDetail.Date = schedule.getString("Date")
                                workScheduleDetail.DaysToEnd = schedule.getInt("DaysToEnd")
                                var endTime = schedule.getString("EndTime")
                                if (endTime.isNullOrEmpty() || endTime == "null") {
                                    endTime = "1700"
                                }
                                workScheduleDetail.EndTime = endTime
                                var startTime = schedule.getString("StartTime")
                                if (startTime.isNullOrEmpty() || startTime == "null") {
                                    startTime = "0800"
                                }
                                workScheduleDetail.StartTime = startTime
                                workScheduleDetail.Temperature = schedule.getString("Temperature")
                                workScheduleDetail.WeatherID = schedule.getInt("WeatherID")
                                workScheduleDetail.WorkID = schedule.getString("WorkID")
                                workScheduleDetail.WorkStop = schedule.getInt("WorkStop")
                                workScheduleDetail.WorkerCount = JsonHandling.toIntOrDefaultValue(schedule, "WorkerCount", null)
                                report.WorkScheduleDetail = workScheduleDetail
                            }
                            if (report.WorkResultStatus == 2) {
                                val workResult = report_row.getJSONObject("WorkResult")
                                val workResultDetail: WorkSchedule = WorkSchedule()
                                workResultDetail.Comment = workResult.getString("Comment")
                                workResultDetail.Date = workResult.getString("Date")
                                workResultDetail.DaysToEnd = workResult.getInt("DaysToEnd")
                                var endTime = workResult.getString("EndTime")
                                if (endTime.isNullOrEmpty() || endTime == "null") {
                                    endTime = "1700"
                                }
                                workResultDetail.EndTime = endTime
                                var startTime = workResult.getString("StartTime")
                                if (startTime.isNullOrEmpty() || startTime == "null") {
                                    startTime = "0800"
                                }
                                workResultDetail.StartTime = startTime
                                workResultDetail.Temperature = workResult.getString("Temperature")
                                workResultDetail.WeatherID = workResult.getInt("WeatherID")
                                workResultDetail.WorkID = workResult.getString("WorkID")
                                workResultDetail.WorkStop = workResult.getInt("WorkStop")
                                workResultDetail.WorkerCount = JsonHandling.toIntOrDefaultValue(workResult, "WorkerCount", null)
                                report.WorkResultDetail = workResultDetail
                            }
                            listReport.add(report)
                        }
                        ListSet()
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.TaskID = taskID
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ReportsURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}
