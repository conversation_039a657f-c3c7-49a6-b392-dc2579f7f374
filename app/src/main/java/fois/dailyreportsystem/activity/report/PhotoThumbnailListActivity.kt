package fois.dailyreportsystem.activity.report

// 作成日：2017/08/24
// 更新日：2018/02/13

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import android.view.MotionEvent
import android.view.View
import android.widget.AdapterView
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.PhotoThumbnailList
import fois.dailyreportsystem.data.adapter.PhotoThumbnailListAdapter
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.photo_thumbnail_list_layout.*
import org.json.JSONObject

// サムネイル一覧画面のアクティビティ
class PhotoThumbnailListActivity : BaseActivity() {

    // 変数宣言
    val listPhotoThumbnailList: ArrayList<PhotoThumbnailList> = ArrayList()

    private var Path: String? = null
    private var photoTaken: Boolean = false     // 写真を撮影したか

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.photo_thumbnail_list_layout)

        // 現在のモードを登録
        this.settings.put("PHOTO_MODE", "thumbnail")

        // 写真の取得先
        if (Build.VERSION.SDK_INT >23) {
            Path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).toString() + "/temp_image.jpg"
        } else {
            Path = Environment.getExternalStorageDirectory().toString() + "/temp_image.jpg"
        }

        // クリックイベント
        // バックボタンタップ
        back_arrow.setOnSafeClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            BackProcessList()
        }

        // サムネイルボタンタップ
        list_button.setOnSafeClickListener {
            list.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
            list_image.setImageResource(R.drawable.list_on)
            ChangePhotographyList()
        }
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        // 邸名の表示
        toolbar_title.text = this.settings.getString("TASK_NAME" )

        // 邸名の長さを判別
        val chars: CharArray = this.settings.getString("TASK_NAME" ).toCharArray()
        var taskNameWidth: Int = 0
        for (i in chars.indices) {
            if((chars[i] <= '\u007e') || (chars[i] in '\uff61'..'\uff9f')) {
                taskNameWidth += 1
            } else {
                taskNameWidth += 2
            }
        }
        if (taskNameWidth > 16) {
            // 邸名の長さが全角8文字を超えた場合、ツールバーのサムネイル文字を非表示にする
            list.visibility = View.GONE
        }

        // 工程名の表示
        photo_thumbnail_region.text = this.settings.getString("REGION_NAME")

        if (photoTaken) {
            val intent: Intent = Intent(applicationContext, PhotoDetailActivity::class.java)
            intent.putExtra(MediaStore.EXTRA_OUTPUT, Path)
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
        } else {
            // 撮影項目一覧（サムネール）の取得
            getPhotoThumbnailListAsync()
        }
    }

    override fun onRestart() {
        super.onRestart()
        // スリープから戻った時リストを空にする
        listPhotoThumbnailList.clear()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (resultCode) {
            Activity.RESULT_CANCELED -> {
                log("camera", "cancel")
            }
            Activity.RESULT_OK -> {
                log("camera", "photo taken")
                photoTaken = true
            }
        }
    }

    /* -------------------- 画面遷移 -------------------- */
    // 撮影項目一覧画面へ切替
    private fun ChangePhotographyList() {
        if(clickPosition == null) {
            clickPosition = true
            // 初期表示位置のリセット
            ListPositionReset()
            val intent = Intent(this, PhotoShootListActivity::class.java)
            startActivity(intent)
            finish()
            overridePendingTransition(0, 0)
        }
    }

    // 工程一覧画面へ戻る
    private fun BackProcessList() {
        if(clickPosition == null) {
            clickPosition = true
            // 初期表示位置のリセット
            ListPositionReset()
            val intent = Intent(this, PhotoProcessListActivity::class.java)
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
        }
    }

    /* -------------------- リストビューイベント -------------------- */
    // リストビューにセット
    private fun ListSet() {
        val photoThumbnailListAdapter: PhotoThumbnailListAdapter = PhotoThumbnailListAdapter(this, listPhotoThumbnailList)
        photo_thumbnail_list_listview.adapter = photoThumbnailListAdapter
        // 保存した位置でリストビューを表示
        photo_thumbnail_list_listview.setSelectionFromTop(this.position.getInt("PHOTO_THUMBNAIL_LIST1", 0), this.position.getInt("PHOTO_THUMBNAIL_LIST2", 0))

        photo_thumbnail_list_listview.onItemClickListener = ClickItem
    }

    // リスト選択
    private val ClickItem = AdapterView.OnItemClickListener { parent, view, position, id ->
        val photoThumbnailList = this@PhotoThumbnailListActivity.photo_thumbnail_list_listview.getItemAtPosition(position) as PhotoThumbnailList
        this.settings.put("CONTENT_ID", photoThumbnailList.ContentID)
        this.settings.put("DETAIL_CONTENT", photoThumbnailList.RegionName + "-" + photoThumbnailList.ContentName)
        when (photoThumbnailList.type) {
            1 -> {
                // サムネイル（リスト）タップ
                if(clickPosition == null) {
                    clickPosition = true
                    val intent: Intent = Intent(application, PhotoDetailActivity::class.java)
                    intent.putExtra("selectWhich", photoThumbnailList.which)
                    startActivity(intent)
                    finish()
                    overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
                }
            }
            2 -> {
                checkPermissions()
                if (ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED &&
                    ((Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU &&
                            ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) ||
                            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU)) {
                    // 権限がある場合、カメラ起動
                    StartCamera().LaunchCamera(context!!.getString(R.string.photo_thumbnail_list_activity))
                }
            }
        }
    }

    // リスト位置のリセット
    private fun ListPositionReset() {
        this.position.put("PHOTO_THUMBNAIL_LIST1", 0)
        this.position.put("PHOTO_THUMBNAIL_LIST2", 0)
    }

    /* -------------------- タップイベント -------------------- */
    //端末の戻るボタン
    override fun onBackPressed() {
        BackProcessList()
    }

    // 画面タッチイベント
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        try {
            if (listPhotoThumbnailList.size > 0) {
                // 現在の位置を保存する
                this.position.put("PHOTO_THUMBNAIL_LIST1", photo_thumbnail_list_listview.firstVisiblePosition)
                this.position.put("PHOTO_THUMBNAIL_LIST2", photo_thumbnail_list_listview.getChildAt(0).top)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return super.dispatchTouchEvent(ev)
    }

    /* -------------------- 通信処理 -------------------- */
    private fun getPhotoThumbnailListAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 成功時
                        listPhotoThumbnailList.clear()
                        val data = result.getJSONArray("Photos")
                        // タイトル判別用
                        var beforeContentID: String = "beforeContentID"
                        // 写真追加用
                        var beforeContentName: String = ""
                        var beforeRegionName: String = ""
                        // 写真が何枚目か判別用
                        var which = 0
                        for (i in 0..data.length()-1) {
                            val data_row = data.getJSONObject(i)
                            if (beforeContentID != data_row.getString("ContentID")) {
                                if (beforeContentID != "beforeContentID") {
                                    // リスト：写真を追加する
                                    val photoThumbnailList = PhotoThumbnailList()
                                    photoThumbnailList.ContentID = beforeContentID
                                    photoThumbnailList.ContentName = beforeContentName
                                    photoThumbnailList.RegionName = beforeRegionName
                                    photoThumbnailList.type = 2
                                    listPhotoThumbnailList.add(photoThumbnailList)
                                }
                                // リスト：作業場所タイトル
                                val photoThumbnailList = PhotoThumbnailList()
                                photoThumbnailList.ContentName = data_row.getString("ContentName")
                                photoThumbnailList.type = 0
                                listPhotoThumbnailList.add(photoThumbnailList)
                                which = 1
                            }

                            // リスト：サムネイル
                            var photoThumbnailList = PhotoThumbnailList()
                            photoThumbnailList.CacheFileName = data_row.getString("CacheFileName")
                            photoThumbnailList.Comment = data_row.getString("Comment")
                            photoThumbnailList.CompanyID = data_row.getString("CompanyID")
                            photoThumbnailList.ContentID = data_row.getString("ContentID")
                            photoThumbnailList.ContentName = data_row.getString("ContentName")
                            photoThumbnailList.LargePhotoUrl = data_row.getString("LargePhotoUrl")
                            photoThumbnailList.Manager = data_row.getString("Manager")
                            photoThumbnailList.PhotoDate = data_row.getString("PhotoDate")
                            photoThumbnailList.PhotoID = data_row.getString("PhotoID")
                            photoThumbnailList.PhotoTime = data_row.getString("PhotoTime")
                            photoThumbnailList.RegionID = data_row.getString("RegionID")
                            photoThumbnailList.RegionName = data_row.getString("RegionName")
                            photoThumbnailList.SmallPhotoUrl = data_row.getString("SmallPhotoUrl")
                            photoThumbnailList.TaskID = data_row.getString("TaskID")
                            photoThumbnailList.WorkID = data_row.getString("WorkID")
                            photoThumbnailList.WorkerName = data_row.getString("WorkerName")
                            photoThumbnailList.type = 1
                            photoThumbnailList.which = which
                            listPhotoThumbnailList.add(photoThumbnailList)
                            which++

                            beforeContentID = data_row.getString("ContentID")
                            beforeContentName = data_row.getString("ContentName")
                            beforeRegionName = data_row.getString("RegionName")
                            if (i == data.length()-1) {
                                // リスト：写真を追加する（リスト最後）
                                photoThumbnailList = PhotoThumbnailList()
                                photoThumbnailList.ContentID = data_row.getString("ContentID")
                                photoThumbnailList.ContentName = data_row.getString("ContentName")
                                photoThumbnailList.RegionName = data_row.getString("RegionName")
                                photoThumbnailList.type = 2
                                listPhotoThumbnailList.add(photoThumbnailList)
                            }
                        }
                        if (data.length() > 0) {
                            ListSet()
                        }
                    } else {
                        // 失敗時
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.TaskID = this.settings.getString("TASK_ID")
        params.RegionID = this.settings.getString("REGION_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.PhotosURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}