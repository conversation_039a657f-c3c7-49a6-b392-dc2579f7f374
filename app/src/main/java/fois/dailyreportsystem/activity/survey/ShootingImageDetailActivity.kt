package fois.dailyreportsystem.activity.survey

import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.graphics.Bitmap
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.*
import android.view.KeyEvent.KEYCODE_ENTER
import android.view.inputmethod.InputMethodManager
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.ResearchPhotoAttribute
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.image_detail_layout.*
import kotlinx.android.synthetic.main.image_detail_layout.back_arrow
import kotlinx.android.synthetic.main.image_detail_layout.diagnosis_comment
import org.json.JSONObject
import java.io.File
import java.io.FileOutputStream


class ShootingImageDetailActivity : BaseActivity() {

    private var mImageUsage: String = ""
    private var forwardOriginal = ""
    private var researchPhotoAttribute = ResearchPhotoAttribute.init()

    /* -------------------- ライフサイクル -------------------- */
    /*
    起動：onCreate→onResume
    スリープから復帰、他画面からの復帰：onRestart→onResume
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        log(context?.javaClass!!.name, "onCreate")
        super.onCreate(savedInstanceState)
        setContentView(R.layout.image_detail_layout)

        forwardOriginal = this.settings.getString("FORWARD_IMAGE_DETAIL")

        when (forwardOriginal) {
            "GALLERY" -> {
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                setDisplayContentsByOrientation(Configuration.ORIENTATION_PORTRAIT)
            }
            "SHOOTING" -> {
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
                setDisplayContentsByOrientation(Configuration.ORIENTATION_LANDSCAPE)
            }
        }

        back_arrow.setOnSafeClickListener { onBackPressed() }
        head_complete.setOnSafeClickListener {
            runOnUiThread {
                run {
                    updateImageInfo()
                }
            }
        }
        complete_text.setOnSafeClickListener {
            runOnUiThread {
                run {
                    updateImageInfo()
                }
            }
        }
        delete_area.setOnSafeClickListener { openDialog() }

        for_spare_area.setOnClickListener { view -> usageSwitchChange(view) }
        for_report_area.setOnClickListener { view -> usageSwitchChange(view) }

        diagnosis_comment.setOnKeyListener(object : View.OnKeyListener {
            override fun onKey(v: View?, keyCode: Int, event: KeyEvent?): Boolean {
                if (event!!.action == KeyEvent.ACTION_DOWN && keyCode == KEYCODE_ENTER) {
                    return true
                }
                return false
            }
        })

        diagnosis_comment.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                validation()
            }
        })

        this.settings.remove("SHOOTING_ACTION_DELETE")
        this.settings.remove("SHOOTING_ACTION_SELECTED")

    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        runOnUiThread {
            run {
                val photoUri = this.settings.getString("PHOTO_URI", null)
                this.settings.remove("PHOTO_URI")
                if(photoUri != null){
                    val bitmap = BitmapUtil.decodeSampledBitmapFromResource(photoUri,1280,960)
                    shooting_image.setImageBitmap(bitmap)

                }
                getPhotoData()

            }
        }

        log(context?.javaClass!!.name, "onResume")
    }

    /** 画面の向きによって設定を変更するメソッド */
    private fun setDisplayContentsByOrientation(orientation: Int) {

        val headerParams = LayoutHeader.layoutParams
        val mainContentsParam = image_detail_main_contents.layoutParams as LinearLayout.LayoutParams
        val imageAreaParams = image_area.layoutParams as LinearLayout.LayoutParams
        val diagnosisArea = diagnosis_area.layoutParams as LinearLayout.LayoutParams
        val commentArea = comment_area.layoutParams as LinearLayout.LayoutParams


        // 画面の向きにより、画面の構成を変更する。
        // 横
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
            headerParams.height = convertDp2Px(30f, this)
            back_arrow.setPadding(0,convertDp2Px(7F,this),0,convertDp2Px(7F,this))

            image_detail_main_contents.orientation = LinearLayout.HORIZONTAL

            imageAreaParams.height = LinearLayout.LayoutParams.MATCH_PARENT
            imageAreaParams.width = 0
            imageAreaParams.weight = 0.5f

            diagnosisArea.height = LinearLayout.LayoutParams.WRAP_CONTENT
            diagnosisArea.width = 0
            diagnosisArea.weight = 0.5f

            mainContentsParam.height = 0
            mainContentsParam.width = LinearLayout.LayoutParams.MATCH_PARENT
            mainContentsParam.weight = 0.8f

            commentArea.height = 0
            commentArea.width = LinearLayout.LayoutParams.MATCH_PARENT
            commentArea.weight = 0.2f

        }

        LayoutHeader.layoutParams = headerParams
        image_area.layoutParams = imageAreaParams
        diagnosis_area.layoutParams = diagnosisArea
        image_detail_main_contents.layoutParams = mainContentsParam
        comment_area.layoutParams = commentArea

    }

    // pxをdpに変換する処理
    private fun convertDp2Px(dp: Float, context: Context): Int {
        val metrics = context.resources.displayMetrics
        return (dp * metrics.density).toInt()
    }

    // スリープからの復帰、他画面からの復帰
    override fun onRestart() {
        super.onRestart()
        // リストビューの位置を取得する
        log(context?.javaClass!!.name, "onRestart")
    }

    override fun onPause() {
        super.onPause()

        log(context?.javaClass!!.name, "onPause")
    }

    // 画面タッチイベント
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(image_detail_root.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す（EditTextからフォーカスを外す）
        image_detail_root.requestFocus()

        return super.dispatchTouchEvent(ev)
    }

    private fun usageSwitchChange(view: View) {
        when (view.id) {
            for_spare_area.id -> {
                mImageUsage = Constant.ResearchTaskPhotoType.SPARE.SELECTED
                for_spare_check.setPadding(0,convertDp2Px(10F,this),0,convertDp2Px(10F,this))
                for_spare_check.setImageResource(R.drawable.circle_blue_check)
                for_spare.setTextColor(ContextCompat.getColor(this,R.color.light_blue))
                for_report_check.setPadding(0,convertDp2Px(10F,this),0,convertDp2Px(10F,this))
                for_report_check.setImageResource(R.drawable.circle_non_check)
                for_report.setTextColor(ContextCompat.getColor(this,R.color.button_gray))
            }
            for_report_area.id -> {
                mImageUsage = Constant.ResearchTaskPhotoType.REPORT.SELECTED
                for_spare_check.setPadding(0,convertDp2Px(10F,this),0,convertDp2Px(10F,this))
                for_spare_check.setImageResource(R.drawable.circle_non_check)
                for_spare.setTextColor(ContextCompat.getColor(this,R.color.button_gray))
                for_report_check.setPadding(0,convertDp2Px(10F,this),0,convertDp2Px(10F,this))
                for_report_check.setImageResource(R.drawable.circle_blue_check)
                for_report.setTextColor(ContextCompat.getColor(this,R.color.light_blue))
            }
        }
        validation()
    }

    private fun setupItem() {

        category_name.text = researchPhotoAttribute.PointOutCategoryName
        diagnosis_text.text = researchPhotoAttribute.PointOutItemName

        diagnosis_comment.setText(researchPhotoAttribute!!.Remarks)

        mImageUsage = researchPhotoAttribute!!.Selected

        if (mImageUsage == Constant.ResearchTaskPhotoType.SPARE.SELECTED) {
            usageSwitchChange(for_spare_area)
        } else {
            usageSwitchChange(for_report_area)
        }


    }

    /**
     *  openDialog ダイアログ表示処理
     * */
    private fun openDialog() {
        var alertDialog: AlertDialog? = null
        val alertBuilder = AlertDialog.Builder(this, R.style.MyDialog)
        val mAlertLayout = this.layoutInflater.inflate(R.layout.guide_popup_window, null)

        val actionTextView = mAlertLayout.findViewById<TextView>(R.id.action_text)
        val cancelTextView = mAlertLayout.findViewById<TextView>(R.id.popup_cancel)
        cancelTextView.setOnSafeClickListener {
            if (alertDialog!!.isShowing) alertDialog!!.dismiss()
        }

        actionTextView.text = this.resources.getString(R.string.do_delete)
        actionTextView.setTextColor(ContextCompat.getColor(this, R.color.red))
        actionTextView.setOnSafeClickListener {
            deleteImage()
            if (alertDialog!!.isShowing) alertDialog!!.dismiss()
        }

        alertBuilder.setView(mAlertLayout)
        alertDialog = alertBuilder.create()

        alertDialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        val dialogLayoutParam = alertDialog.window!!.attributes

        dialogLayoutParam.gravity = Gravity.BOTTOM
        alertDialog.window!!.attributes = dialogLayoutParam
        alertDialog.window!!.setLayout(resources.displayMetrics.widthPixels, LinearLayout.LayoutParams.WRAP_CONTENT)

        alertDialog.show()
    }

    private fun validation(): Boolean {
        var check: Boolean = true

        if(researchPhotoAttribute.Remarks == diagnosis_comment.text.toString()
                && researchPhotoAttribute.Selected == mImageUsage){
            check = false
        }

        // デザイン変更
        if (check) {
            complete_text.isEnabled = true
            complete_text.setBackgroundResource(R.color.light_blue)
            head_complete.isEnabled = true
            head_complete.setTextColor(ContextCompat.getColor(this, R.color.white))
        } else {
            head_complete.isEnabled = false
            head_complete.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
            complete_text.isEnabled = false
            complete_text.setBackgroundResource(R.color.half_transparent)
        }
        return check
    }

    /* -------------------- 画面遷移 -------------------- */

        // 端末の戻るボタン
        override fun onBackPressed() {
            when (forwardOriginal) {
                "GALLERY" -> {
                    val intent = Intent(this, ShootingGalleryActivity::class.java)
                    startActivity(intent)
                    finish()
                    overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
                }
                "SHOOTING" -> {
                    this.settings.put("SHOOTING_ACTION_SELECTED", mImageUsage)
                    finish()
                    overridePendingTransition(0, 0)
                }
            }

        }


    /* -------------------- リストビューイベント -------------------- */

    /* -------------------- 通信処理 -------------------- */

    /**
     *  getPhotoData 調査データを取得する。
     *
     * */
    private fun getPhotoData() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val taskArray = result.getJSONArray("ResearchPhotoAttributes")

                        for (i in 0 until taskArray.length()) {
                            val row = taskArray.getJSONObject(i)

                            researchPhotoAttribute = ResearchPhotoAttribute(
                                    DisplayOrder = row.getString("DisplayOrder"),
                                    PhotoDate = row.getString("PhotoDate"),
                                    PhotoID = row.getString("PhotoID"),
                                    PointOutCategoryID = row.getString("PointOutCategoryID"),
                                    PointOutCategoryName = row.getString("PointOutCategoryName"),
                                    PointOutID = row.getString("PointOutID"),
                                    PointOutItemID = row.getString("PointOutItemID"),
                                    PointOutItemName = row.getString("PointOutItemName"),
                                    PointOutName = row.getString("PointOutName"),
                                    Remarks = row.getString("Remarks"),
                                    ResearchTaskID = row.getString("ResearchTaskID"),
                                    Rotation = row.getString("Rotation"),
                                    Selected = row.getString("Selected"),
                                    PointOutSelectedPhotoCount = row.getString("PointOutSelectedPhotoCount"),
                                    PointOutUnselectedPhotoCount = row.getString("PointOutUnselectedPhotoCount"),
                                    PointOutCategorySelectedPhotoCount = row.getString("PointOutCategorySelectedPhotoCount"),
                                    PointOutCategoryUnselectedPhotoCount = row.getString("PointOutCategoryUnselectedPhotoCount"),
                                    PointOutItemSelectedPhotoCount = row.getString("PointOutItemSelectedPhotoCount"),
                                    PointOutItemUnselectedPhotoCount = row.getString("PointOutItemUnselectedPhotoCount")
                            )


                        }

                        log("result", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    setupItem()
                    getImageAsync()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.PhotoID = this.settings.getString("PHOTO_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.ResearchPhoto

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * updateImageInfo 写真情報更新処理
     *  報告用、予備用の更新
     *  備考欄の更新
     * */
    private fun updateImageInfo() {
        if(!validation()) { return }
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    if (progressDialog!!.isShowing) {
                        try {
                            progressDialog!!.dismiss()
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        log("result", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()

                    onBackPressed()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.PhotoID = this.settings.getString("PHOTO_ID")
        params.Selected = mImageUsage
        params.Remarks = diagnosis_comment.text.toString()

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.ResearchPhotoModify

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    // 写真取得
    private fun getImageAsync() {
        val asyncBitmapLoader = AsyncBitmapLoader(object : AsyncBitmapLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: Bitmap?) {
                if (result == null) { return }
                try {
                    val file = File(context?.cacheDir, researchPhotoAttribute.PhotoID)
                    FileOutputStream(file).use { outputStream ->
                        result.compress(Bitmap.CompressFormat.JPEG, 100, outputStream)
                        shooting_image.setImageBitmap(result)
                        outputStream.flush()
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        val userId = this.settings.getString("USER_ID")

        val url = Constant.URL + Constant.GetResearchPhoto + "?UserID=$userId&PhotoID=${researchPhotoAttribute!!.PhotoID}&Size=1"

        // 非同期通信開始
        asyncBitmapLoader.execute(url)
    }

    /**
     * deleteImage イメージ削除処理
     *
     *
     * */
    private fun deleteImage() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        settings.put("SHOOTING_ACTION_DELETE",true)
                        onBackPressed()
                        log("result", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.PhotoID = this.settings.getString("PHOTO_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.ResearchPhotoDelete

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}
