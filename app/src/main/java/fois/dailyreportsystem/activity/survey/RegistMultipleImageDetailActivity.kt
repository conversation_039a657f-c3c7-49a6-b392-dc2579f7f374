package fois.dailyreportsystem.activity.survey

import android.annotation.TargetApi
import android.app.AlertDialog
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.Image
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.HandlerThread
import android.provider.MediaStore
import android.util.Log
import android.view.View
import android.view.Window
import android.view.WindowManager
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.PointOut
import fois.dailyreportsystem.data.PointOutItem
import fois.dailyreportsystem.data.Schedule
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.regist_multiple_image_detail_layout.*
import android.view.*
import android.widget.AdapterView
import android.widget.ListView
import com.google.gson.Gson
import fois.dailyreportsystem.data.ResearchPhotoAttribute
import fois.dailyreportsystem.data.adapter.CategoryPartAdapter
import fois.dailyreportsystem.data.adapter.DiagnosisPartCommentAdapter
import kotlinx.android.synthetic.main.regist_multiple_image_detail_layout.back_arrow
import org.json.JSONObject
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList

@TargetApi(Build.VERSION_CODES.LOLLIPOP)
class RegistMultipleImageDetailActivity: BaseActivity() {
    //region プライベートメンバ
    private var photoType: String = ""
    private var researchTaskId = ""
    private var listSelectedPass: ArrayList<String> = ArrayList()
    private var imagePath: String? = null
    private var imageUpload: Boolean = false

    private var backgroundThread: HandlerThread? = null
    private var backgroundHandler: Handler? = null

    lateinit var researchTaskData: Schedule
    lateinit var pointOutList: ArrayList<PointOut>
    lateinit var outItemList: List<PointOutItem>
    var diagnosisPartListView: ListView? = null
    var diagnosisPartCommentListView: ListView? = null
    var countCurrentImage = 1
    var photoId: String? = ""
    var takePhotoData: PointOutItem? = null
    var mImageUsage: String = ""
    var updateComplete: Boolean = false
    var isRegistering: Boolean = false

    private val TEMP_FILE_NAME = "survey"
    //endregion


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        log(this.javaClass.name, "Start")
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        window.setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN)
        // レイアウト適用
        setContentView(R.layout.regist_multiple_image_detail_layout)

        // 戻るボタンのタッチリスナーを登録
        back_arrow.setOnSafeClickListener { backButton() }

        val intent = this.intent
        // 現地調査情報を登録
        researchTaskData = intent.getParcelableExtra<Schedule>("SURVEY_DATA") as Schedule
        researchTaskId = intent.getStringExtra(ConstantParameters.INTENT_KEY_RESEARCHTASKID)!!

        if (savedInstanceState == null) {
            if (intent.extras != null) {
                if (intent.extras!!.getString(MediaStore.EXTRA_OUTPUT) != null) {
                    imagePath = intent.extras!!.getString(MediaStore.EXTRA_OUTPUT)
                } else if (intent.getStringArrayListExtra("selectPhotoList") != null) {
                    listSelectedPass = (intent.getStringArrayListExtra("selectPhotoList") ?: return)
                }
            }
        } else {
            imagePath = this.settings.getString("EXTRA_OUTPUT")
            this.settings.put("EXTRA_OUTPUT", "")
        }

        if (imagePath != null && imagePath != "" && !imageUpload) {
            imageUpload = true
        } else if (listSelectedPass.size != 0 && !imageUpload) {
            imageUpload = true
            imagePath = listSelectedPass[0]
        }
        setupItem()
    }

    private fun setupItem() {
        val bitmap: Bitmap = BitmapFactory.decodeFile(imagePath)
        list_image_dipslay.setImageBitmap(bitmap)
        photoType = Constant.ResearchTaskPhotoType.REPORT.SELECTED

        val file = File(imagePath)
        val lastModDate = Date(file.lastModified())
        val fileDate = SimpleDateFormat("yyyy/MM/dd HH:MM").format(lastModDate)
        image_datetime.text = fileDate
        count_image.text = "$countCurrentImage" + "/" + listSelectedPass.size.toString()
    }

    override fun onStart() {
        super.onStart()

        // 撮影部位情報を通信で取得
        runOnUiThread {
            run {
                getResearchPointOut()
            }
        }

        // カメラをバックグランドで実行
        startBackgroundThread()
    }

    override fun onRestart() {
        super.onRestart()
    }

    override fun onResume() {
        super.onResume()
        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }
        startBackgroundThread()
    }

    override fun onPause() {
        log(this.javaClass.name, "onPause")
        stopBackgroundThread()

        super.onPause()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        log(this.javaClass.name, "onKeyDown")

        if (keyCode != KeyEvent.KEYCODE_BACK) {
            return super.onKeyDown(keyCode, event)
        }

        backButton()
        return super.onKeyDown(keyCode, event)
    }

    public override fun onDestroy() {
        super.onDestroy()

        log(this.javaClass.name, "onDestroy End")
    }

    /**
     * backButton 前画面へ戻る
     */
    private fun backButton() {
        log(this.javaClass.name, "backButton")
        showDialog()
    }

    private fun showDialog() {
        val builder: AlertDialog.Builder = AlertDialog.Builder(this)
        builder.setTitle("確認")
        builder.setMessage("戻ってよろしいですか？\n残り画像は保存されなくなります。")
        builder.setPositiveButton(context?.getString(R.string.dialog_positive)
        ) { dialog, which ->
            returnSurveyPropertyActivity()
        }
        builder.setNegativeButton(context?.getString(R.string.dialog_negative)
        ) { dialog, which ->
            dialog.dismiss()
        }
        builder.setCancelable(false)
        val alertDialog: AlertDialog = builder.create()
        alertDialog.show()
    }

    private fun returnSurveyPropertyActivity() {
        val intent = Intent(application, SurveyPropertyActivity::class.java)
        intent.putExtra("RESEARCH_ID", researchTaskId)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_out_down, R.anim.slide_in_up)
    }

    /**
     * カメラをバックグランドで実行
     */
    private fun startBackgroundThread() {
        if (backgroundThread == null) {
            backgroundThread = HandlerThread("Camera Background").also { it.start() }
        }
        if (backgroundHandler == null) {
            backgroundHandler = backgroundThread?.looper?.let { Handler(it) }
        }
    }

    private fun stopBackgroundThread() {
        log(this.javaClass.name, "stopBackgroundThread")
        backgroundThread?.quitSafely()
        try {
            backgroundThread?.join()
            backgroundThread = null
            backgroundHandler = null
        } catch (e: InterruptedException) {
            log(this.javaClass.name, e.printStackTrace().toString())
        }
    }

    //----------------------------------- 以下 通信系 ------------------------------------------

    // 撮影項目と診断を確認
    private fun getResearchPointOut() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {
                showHub()
            }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")


                    if (status.getInt("StatusCode") == 0) {
                        val pointOuts = result.getJSONArray("PointOuts")
                        val poointOutObject = pointOuts.getJSONObject(0)
                        val categorys = poointOutObject.getJSONArray("PointOutCategorys")

                        pointOutList = arrayListOf()

                        // PointOutの取得
                        for (i in 0 until categorys.length()) {
                            val jsonObject = categorys.getJSONObject(i)
                            val pointOut = PointOut(CategoryName = jsonObject.getString("CategoryName"),
                                DisplayOrder = jsonObject.getString("DisplayOrder"),
                                PointOutCategoryID = jsonObject.getString("PointOutCategoryID"),
                                PointOutID = jsonObject.getString("PointOutID"),
                                PointOutItems = emptyList(),
                                SelectedPhotoCount = jsonObject.getString("SelectedPhotoCount"),
                                UnselectedPhotoCount = jsonObject.getString("UnselectedPhotoCount"))

                            val categoryItems = jsonObject.getJSONArray("PointOutItems")

                            val pointOutItemList = arrayListOf<PointOutItem>()

                            // categoryの取得
                            for (j in 0 until categoryItems.length()) {
                                val itemsJson = categoryItems.getJSONObject(j)
                                val pointOutItem = PointOutItem(DisplayOrder = itemsJson.getString("DisplayOrder"),
                                    ItemName = itemsJson.getString("ItemName"),
                                    ItemShortName = itemsJson.getString("ItemShortName"),
                                    PointOutCategoryID = itemsJson.getString("PointOutCategoryID"),
                                    PointOutID = itemsJson.getString("PointOutID"),
                                    PointOutItemID = itemsJson.getString("PointOutItemID"),
                                    Remarks = itemsJson.getString("Remarks"),
                                    SelectedPhotoCount = itemsJson.getString("SelectedPhotoCount"),
                                    UnselectedPhotoCount = itemsJson.getString("UnselectedPhotoCount"))

                                pointOutItemList.add(pointOutItem)

                            }

                            pointOut.PointOutItems = pointOutItemList

                            pointOutList.add(pointOut)
                        }

                        setCategoryListView()

                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
                hideHub()
            }
        })

        val researchTask = researchTaskData!!.researchTask

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ResearchTaskID = researchTask!!.ResearchTaskID

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskPointOutItems

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     *  setCategoryListView 撮影部位のビューを追加
     * */
    private fun setCategoryListView() {

        val arrayAdapter = CategoryPartAdapter(this, pointOutList)
        diagnosisPartListView = findViewById<ListView>(R.id.diagnosis_part)

        diagnosisPartListView!!.onItemClickListener = categoryPartListOnclick
        diagnosisPartListView!!.adapter = arrayAdapter

        // 画面表示時に撮影コメントを先頭の撮影部位で初期化して表示
        outItemList = pointOutList[0].PointOutItems

        // コメントリストを初期設定
        val commentAdapter = DiagnosisPartCommentAdapter(this, outItemList, shutterMap)

        diagnosisPartCommentListView = findViewById<ListView>(R.id.diagnosis_comment)

        diagnosisPartCommentListView!!.adapter = commentAdapter
    }

    /**
     *  categoryPartListOnclick 撮影部位タップ処理
     * */
    private val categoryPartListOnclick = AdapterView.OnItemClickListener { _, _, position, _ ->
        //setCategoryItem(position)
        outItemList = pointOutList[position].PointOutItems
        (diagnosisPartCommentListView!!.adapter as DiagnosisPartCommentAdapter).reflesh(outItemList)
    }

    /**
     * 撮影タップ時の処理
     * 撮影データにコメントを付加し、サーバー送信する。
     *
     * */
    private var shutterMap: MutableMap<String, (PointOutItem, String) -> Unit> = mutableMapOf("shutterAction" to fun(data: PointOutItem, select: String) {
        log(this.javaClass.name, "shutter_action ")
        if (!isRegistering) {
            isRegistering = true;
            // 撮影した写真情報の取得
            takePhotoData = data.copy()
            // 報告用、予備用の設定
            mImageUsage = select

            val bitmap: Bitmap = BitmapFactory.decodeFile(imagePath)

            val filepath = TEMP_FILE_NAME + "_" + System.currentTimeMillis()

            this.setFileImage(bitmap, filepath) { uri ->

                //キャッシュにファイルを作成
                val cacheFile = this.getUriToPath(uri) ?: return@setFileImage

                runOnUiThread {
                    run {
                        // 画像ファイルを登録する.
                        imageUploadAsync(cacheFile)
                    }
                }
            }
        }


    })

    // 写真をアップロード（カメラから）
    private fun imageUploadAsync(file: File) {

        //通信前に撮影枚数を増やす
        addPhotoCount()

        photoId = null

        log(context?.javaClass!!.name, Uri.fromFile(file).toString())
        val rotation: String = this.getImageRotation(Uri.fromFile(file), file.absolutePath)
        val params: Dictionary<String, String> = Hashtable<String, String>()
        params.put("UserID", this.settings.getString("USER_ID"))
        params.put("ResearchTaskID", researchTaskData.researchTaskID)
        params.put("PhotoDate", DateUtil.getDateFormat("yyyy-MM-dd HH:mm"))
        params.put("PointOutID", takePhotoData!!.PointOutID)
        params.put("PointOutItemID", takePhotoData!!.PointOutItemID)
        params.put("PointOutCateogryID", takePhotoData!!.PointOutCategoryID)
        params.put("Rotation", rotation)
        params.put("Selected", mImageUsage)

        // URL作成
        val url: String = Constant.URL + Constant.ResearchPhotoAdd
        val startTime = System.nanoTime()
        val asyncMultipleImageUploader = AsyncMultipleImageUploader(this, object : AsyncMultipleImageUploader.AsyncCallback {
            // 実行前
            override fun preExecute() {
                Log.i("TIME preExecute", ((System.nanoTime()-startTime)/1000000).toString() + "ms")
                updateComplete = false
                // ビットマップを表示
                runOnUiThread {
                    run {
                        image_progressBar.visibility = View.VISIBLE
                    }
                }
            }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                Log.i("TIME postExecute", ((System.nanoTime()-startTime)/1000000).toString() + "ms")
                if (result == null) {
                    isRegistering = false
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // ローディングを開始

                        // サーバー上のデータとテキストで表示されているデータがあっているか比較
                        val photoData = result.getJSONArray("ResearchPhotoAttributes")
                        val photoJsonData = photoData.getJSONObject(0)

                        val researchPhotoAttribute = ResearchPhotoAttribute.init(photoJsonData)
                        photoId = researchPhotoAttribute.PhotoID

                        // リストの更新
                        setPointOutList(researchPhotoAttribute)
                        setPointOutItemList(outItemList, toPointOutItem(researchPhotoAttribute))

                        // CategoryAdapter(左)の更新
                        (diagnosisPartListView!!.adapter as CategoryPartAdapter).reflesh(pointOutList)

                        // CommentAdapter(右)の更新
                        (diagnosisPartCommentListView!!.adapter as DiagnosisPartCommentAdapter).reflesh(outItemList)
                    } else {
                        // 失敗時
                        log(context?.javaClass!!.name, status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    isRegistering = false
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    updateComplete = true
                    // ローディングを終了する、サムネイルのセットアップをする
                    image_progressBar.visibility = View.GONE
                    if(countCurrentImage < listSelectedPass.size) {
                        countCurrentImage += 1
                        imagePath = listSelectedPass[countCurrentImage - 1]
                        setupItem()
                    } else {
                        returnSurveyPropertyActivity()
                    }
                    isRegistering = false
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
                isRegistering = false
            }
        }, params)

        // 非同期通信開始
        // 第1引数：URL
        // 第2引数：imageFilePath
        // 第3引数：rotation
        asyncMultipleImageUploader.execute(url, file.absolutePath, rotation)
    }


    /**
     * 撮影部位と項目に＋１カウント
     */
    fun addPhotoCount(){

        // カテゴリー(撮影部位)の撮影枚数カウントアップ
        pointOutList.forEach{
            // 撮影した写真と同じデータである
            if (it.PointOutCategoryID == takePhotoData!!.PointOutCategoryID){
                if (mImageUsage == "0"){
                    it.UnselectedPhotoCount = (it.UnselectedPhotoCount.toInt() + 1).toString()
                }else{
                    it.SelectedPhotoCount = (it.SelectedPhotoCount.toInt() + 1).toString()
                }
            }
        }
        // テキストビュー更新
        (diagnosisPartListView!!.adapter as CategoryPartAdapter).reflesh(pointOutList)

        takePhotoData!!.let{
            // 報告用写真判定
            if (mImageUsage == "0"){
                it.UnselectedPhotoCount = (it.UnselectedPhotoCount.toInt() + 1).toString()
            }else{
                it.SelectedPhotoCount = (it.SelectedPhotoCount.toInt() + 1).toString()
            }
        }
        setPointOutItemList(outItemList, takePhotoData!!)
        // テキストビュー更新
        (diagnosisPartCommentListView!!.adapter as DiagnosisPartCommentAdapter).reflesh(outItemList)

    }

    /**
     *  ResearchPhotoAttributeをpointOutListと入れ替える
     */
    fun setPointOutList(rpa: ResearchPhotoAttribute) {
        pointOutList.forEach { po ->
            // 撮影した写真と同じデータである
            if (po.PointOutItems.any {
                    it.PointOutCategoryID == rpa.PointOutCategoryID &&
                            it.PointOutID == rpa.PointOutID &&
                            it.PointOutItemID == rpa.PointOutItemID
                }) {
                setPointOutItemList(po.PointOutItems, toPointOutItem(rpa))
                // 撮影枚数の更新
                po.SelectedPhotoCount = rpa.PointOutCategorySelectedPhotoCount
                po.UnselectedPhotoCount = rpa.PointOutCategoryUnselectedPhotoCount
            }
        }
    }

    /**
     *  List<PointOutItem>にPointOutItemと入れる
     */
    fun setPointOutItemList(pointOutItems: List<PointOutItem>, poi: PointOutItem) {
        pointOutItems.forEach {
            // 撮影した写真と同じデータである
            if (it.PointOutCategoryID == poi.PointOutCategoryID &&
                it.PointOutID == poi.PointOutID &&
                it.PointOutItemID == poi.PointOutItemID) {
                // 撮影枚数の更新
                it.SelectedPhotoCount = poi.SelectedPhotoCount
                it.UnselectedPhotoCount = poi.UnselectedPhotoCount
            }
        }
    }

    /**
     *　ResearchPhotoAttributeをPointOutItemに変換
     */
    fun toPointOutItem(rpa: ResearchPhotoAttribute): PointOutItem {
        return PointOutItem(DisplayOrder = rpa.DisplayOrder,
            ItemName = rpa.PointOutItemName,
            ItemShortName = rpa.PointOutItemName,
            PointOutCategoryID = rpa.PointOutCategoryID,
            PointOutID = rpa.PointOutID,
            PointOutItemID = rpa.PointOutItemID,
            Remarks = rpa.Remarks,
            SelectedPhotoCount = rpa.PointOutItemSelectedPhotoCount,
            UnselectedPhotoCount = rpa.PointOutItemUnselectedPhotoCount
        )
    }
}











