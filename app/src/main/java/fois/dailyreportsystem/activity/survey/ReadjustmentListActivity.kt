package fois.dailyreportsystem.activity.survey

import android.content.Intent
import android.os.Bundle
import android.widget.AdapterView
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.*
import fois.dailyreportsystem.data.adapter.ReadjustmentListAdapter
import fois.dailyreportsystem.util.*
import fois.dailyreportsystem.util.JsonHandling.toScheduleArrayList
import kotlinx.android.synthetic.main.readjustment_list_layout.*
import org.json.JSONObject

class ReadjustmentListActivity : BaseActivity() {

    private var orderStatusList: ArrayList<Schedule>? = null

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.readjustment_list_layout)

        back_arrow.setOnClickListener { onBackPressed() }

    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }
        getSchedulesList()
        this.settings.put("RESEARCH_TASK_ID", "")
    }

    /* -------------------- 画面遷移 -------------------- */

    /* -------------------- タップイベント -------------------- */
    // 端末の戻るボタン
    override fun onBackPressed() { finish()
    }

    /**
     * setListView List生成処理.
     *
     */
    private fun setListView() {
        if (orderStatusList != null) {
            readjustment_count.text = this.resources.getString(R.string.count, orderStatusList!!.size)

            var listAdapter = ReadjustmentListAdapter(this, orderStatusList!!)

            order_list.adapter = listAdapter
            order_list.onItemClickListener = itemClick
        }
    }

    private val itemClick = AdapterView.OnItemClickListener { parent, view, position, id ->
        val schedule = order_list.getItemAtPosition(position) as Schedule

        val intent = Intent(this, ReadjustmentRegistrationActivity::class.java)
        this.settings.put("RESEARCH_TASK_ID", schedule.researchTaskID)
        this.settings.put("RESEARCH_TASK_CITY_CODE", schedule.researchTask.CityCode)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_up, R.anim.fade_out)

    }

    /* -------------------- 通信処理 -------------------- */

    /**
     * getSchedulesList 調査.
     *
     * */
    private fun getSchedulesList() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")

                    // 認証成功
                    if (status.getInt("StatusCode") == 0) {
                        val scheduleArrayList = result.getJSONArray("Schedules")

                        orderStatusList = toScheduleArrayList(scheduleArrayList)

                        setListView()
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.OfficeID = this.settings.getString("OFFICE_ID")
        params.SurveyStatusID = Constant.OrderStatusCode.READJUSTMENT.status

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTasks

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}