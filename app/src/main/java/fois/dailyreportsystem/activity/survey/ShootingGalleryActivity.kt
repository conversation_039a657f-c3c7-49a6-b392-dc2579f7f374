package fois.dailyreportsystem.activity.survey

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.AdapterView
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.ResearchPhotoAttribute
import fois.dailyreportsystem.data.adapter.ShootingGalleyAdapter
import kotlinx.android.synthetic.main.shooting_grallery.*

import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.row_diagnosis_part.*
import org.json.JSONObject


class ShootingGalleryActivity : BaseActivity() {

    var shootingGalleryList: ArrayList<ResearchPhotoAttribute> = ArrayList()
    private lateinit var swipeRefresh: SwipeRefreshLayout
    private var viewType = Constant.ResearchTaskPhotoType.ALL.SELECTED
    private var itemAdapter : ShootingGalleyAdapter? = null

    /* -------------------- ライフサイクル -------------------- */
    /*
    起動：onCreate→onResume
    スリープから復帰、他画面からの復帰：onRestart→onResume
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        log(context?.javaClass!!.name, "onCreate")
        super.onCreate(savedInstanceState)
        setContentView(R.layout.shooting_grallery)

        this.settings.put("FORWARD_IMAGE_DETAIL", "")
        swipeRefresh = findViewById(R.id.gallery_swipe_refreshLayout)
        swipeRefresh.setOnRefreshListener { getImageList() }

        back_arrow.setOnSafeClickListener { onBackPressed() }
        filter_select.setOnClickListener { switchListView() }

        // 表示初期値設定
        filter_select.text = if(this.position.getString("SHOOTING_GALLERY_SELECTED", viewType) == Constant.ResearchTaskPhotoType.ALL.SELECTED) {
            this.resources.getString(R.string.shooting_all)
        } else {
            this.resources.getString(R.string.shooting_report_only)
        }
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        getImageList()
    }

    /**
     * switchListView 表示切替処理
     *
     * */
    private fun switchListView() {
        if(viewType == Constant.ResearchTaskPhotoType.ALL.SELECTED) {
            viewType = Constant.ResearchTaskPhotoType.REPORT.SELECTED
            filter_select.text = this.resources.getString(R.string.shooting_report_only)
        } else {
            viewType = Constant.ResearchTaskPhotoType.ALL.SELECTED
            filter_select.text = this.resources.getString(R.string.shooting_all)
        }

        this.position.put("SHOOTING_GALLERY_SELECTED", viewType)
        itemAdapter!!.filter.filter(viewType)
    }

    /* -------------------- 画面遷移 -------------------- */

    // 端末の戻るボタン
    override fun onBackPressed() {
        val intent = Intent(this, SurveyPropertyActivity::class.java)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
    }

    /* -------------------- リストビューイベント -------------------- */

    fun setUpListView() {
        itemAdapter = ShootingGalleyAdapter(this, shootingGalleryList)
        shooting_list.adapter = itemAdapter
        shooting_list.onItemClickListener = clickItem

        // 更新をかけたときにもフィルターをかける
        itemAdapter!!.filter.filter(this.position.getString("SHOOTING_GALLERY_SELECTED", viewType))

        shooting_list.setSelectionFromTop(this.position.getInt("SHOOTING_GALLERY_LIST1"), this.position.getInt("SHOOTING_GALLERY_LIST2"))
        this.position.put("SHOOTING_GALLERY_LIST1", 0)
        this.position.put("SHOOTING_GALLERY_LIST2", 0)
    }

    // リスト選択
    private val clickItem = AdapterView.OnItemClickListener { adapter, _, position, _ ->
        shooting_list.safeClick()

        val surveyData = shooting_list.adapter.getItem(position) as ResearchPhotoAttribute

        this.position.put("SHOOTING_GALLERY_LIST1", shooting_list.firstVisiblePosition)
        this.position.put("SHOOTING_GALLERY_LIST2", shooting_list.getChildAt(0).top)

        this.settings.put("PHOTO_ID", surveyData.PhotoID)
        this.settings.put("FORWARD_IMAGE_DETAIL", "GALLERY")
        val intent = Intent(this, ShootingImageDetailActivity::class.java)
        startActivity(intent)
        finish()
    }


    /* -------------------- 通信処理 -------------------- */

    /**
     * getImageList 撮影写真一覧取得
     *
     * */
    private fun getImageList() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        shootingGalleryList = ArrayList()
                        // 認証成功
                        val taskArray = result.getJSONArray("ResearchPhotoAttributes")

                        // 予備・報告用写真枚数
                        //var reserve:Int = 0
                        //var report:Int = 0

                        for (i in 0 until taskArray.length()) {
                            val row = taskArray.getJSONObject(i)

                            var researchPhotoAttribute = ResearchPhotoAttribute(
                                    DisplayOrder = row.getString("DisplayOrder"),
                                    PhotoDate = row.getString("PhotoDate"),
                                    PhotoID = row.getString("PhotoID"),
                                    PointOutCategoryID = row.getString("PointOutCategoryID"),
                                    PointOutCategoryName = row.getString("PointOutCategoryName"),
                                    PointOutID = row.getString("PointOutID"),
                                    PointOutItemID = row.getString("PointOutItemID"),
                                    PointOutItemName = row.getString("PointOutItemName"),
                                    PointOutName = row.getString("PointOutName"),
                                    Remarks = row.getString("Remarks"),
                                    ResearchTaskID = row.getString("ResearchTaskID"),
                                    Rotation = row.getString("Rotation"),
                                    Selected = row.getString("Selected"),
                                    PointOutSelectedPhotoCount = row.getString("PointOutSelectedPhotoCount"),
                                    PointOutUnselectedPhotoCount = row.getString("PointOutUnselectedPhotoCount"),
                                    PointOutCategorySelectedPhotoCount = row.getString("PointOutCategorySelectedPhotoCount"),
                                    PointOutCategoryUnselectedPhotoCount = row.getString("PointOutCategoryUnselectedPhotoCount"),
                                    PointOutItemSelectedPhotoCount = row.getString("PointOutItemSelectedPhotoCount"),
                                    PointOutItemUnselectedPhotoCount = row.getString("PointOutItemUnselectedPhotoCount")
                            )

                            // 予備用の場合
                            /*if(row.getString("Selected") == "0"){
                                reserve += 1
                            }
                            else{
                                report += 1
                            }*/

                            shootingGalleryList.add(researchPhotoAttribute)

                        }

                        setUpListView()

                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                    swipeRefresh.isRefreshing = false
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ResearchTaskID = this.settings.getString("RESEARCH_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchPhotos

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

}
