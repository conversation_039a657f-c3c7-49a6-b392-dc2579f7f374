package fois.dailyreportsystem.activity.survey

import android.Manifest
import android.annotation.TargetApi
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.graphics.*
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.hardware.camera2.*
import android.media.*
import android.net.Uri
import android.os.*
import androidx.core.app.ActivityCompat
import android.util.Size
import android.util.SparseIntArray
import android.view.*
import android.view.animation.Animation
import android.view.animation.Animation.AnimationListener
import android.view.animation.AnimationUtils
import android.widget.*
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.*
import fois.dailyreportsystem.data.adapter.CategoryPartAdapter
import fois.dailyreportsystem.data.adapter.DiagnosisPartCommentAdapter
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.shooting_layout.*
import org.json.JSONObject
import java.io.*
import java.lang.Long.signum
import java.util.*
import java.util.concurrent.Semaphore
import java.util.concurrent.TimeUnit
import kotlin.collections.ArrayList

@TargetApi(Build.VERSION_CODES.LOLLIPOP)
class ShootingActivity : BaseActivity(), SensorEventListener, ActivityCompat.OnRequestPermissionsResultCallback {

    private val MAX_ZOON = 40
    private val MAX_IMAGES = 4

    /** 回転したと判定する際の閾値角度 */
    private val THRESHOLD_DEGREE = 60

    /** 縦から横に変化したと判定する際の角度 */
    private val VERTICAL_TO_HORIZONTAL_DEGREE = THRESHOLD_DEGREE

    /** 横から縦にに変化したと判定する際の角度 */
    private val HORIZONTAL_TO_VERTICAL_DEGREE = 90 - THRESHOLD_DEGREE

    /** 縦向きを表す定数 */
    private val ORIENTATION_VERTICAL = 0

    /** 横向きを表す定数 */
    private val ORIENTATION_HORIZONTAL = 1

    /** ラジアンを度に変換する際の定数 */
    private val RAD2DEG = 180 / Math.PI

    /** Camera camera_state: Showing camera preview. */
    private val STATE_PREVIEW = 0

    /** Camera camera_state: Waiting for the focus to be locked.*/
    private val STATE_WAITING_LOCK = 1

    /** Camera camera_state: Waiting for the exposure to be precapture camera_state. */
    private val STATE_WAITING_PRECAPTURE = 2

    /** Camera camera_state: Waiting for the exposure camera_state to be something other than precapture.*/
    private val STATE_WAITING_NON_PRECAPTURE = 3

    /** Camera camera_state: Picture was taken. */
    private val STATE_PICTURE_TAKEN = 4

    private var preAFState: Int? = -1

    private var AF_SAME_STATE_REPEAT_MAX = 5

    private val MATRIX_SIZE = 16

    var inR = FloatArray(MATRIX_SIZE)
    var outR = FloatArray(MATRIX_SIZE)
    var I = FloatArray(MATRIX_SIZE)

    var orientationValues = FloatArray(3)
    var magneticValues = FloatArray(3)
    var accelerometerValues = FloatArray(3)

    var mPreOrientation = -1

    var sensorManager: SensorManager? = null

    // フォーカスが終わらない場合
    var sameAFStateCount: Int = 0

    var photoId: String? = ""
    var photoUri: String? = null

    val REQUEST_CAMERA_PERMISSION = 1

    var handle = Handler()

    // 消すアニメーション
    var runnable = Runnable {
        try {
            val animation = AnimationUtils.loadAnimation(this, R.anim.fadeout_thumnail)
            animation.fillAfter = false
            // アニメーション終了時にサムネイルのパーツを初期化する。
            animation.setAnimationListener(object : AnimationListener {
                override fun onAnimationStart(var1: Animation) {}
                override fun onAnimationRepeat(var1: Animation) {}
                override fun onAnimationEnd(var1: Animation) {
                    thumbnail.visibility = View.GONE
                    thumbnail_background.setBackgroundResource(0)
                    latest_photo_thumbnail.setImageBitmap(null)
                }
            })

            thumbnail.startAnimation(animation)
        } catch (e: Exception) {
        }
    }

    //check camera_state orientation of output image
    private val ORIENTATIONS = SparseIntArray()

    init {
        ORIENTATIONS.append(Surface.ROTATION_0, 90)
        ORIENTATIONS.append(Surface.ROTATION_90, 0)
        ORIENTATIONS.append(Surface.ROTATION_180, 270)
        ORIENTATIONS.append(Surface.ROTATION_270, 180)
    }

    private val TEMP_FILE_NAME = "survey"

    // プレビューのサイズ・画像サイズ
    private var takePhotoWidth = 1280
    private var takePhotoHeight = 960

    private var aeMode: Int = 0

    /** カメラマネージャーを生成 */
    private var cameraManager: CameraManager? = null

    // ズーム処理
    private var zoomLevel: Float = 1f
    var zoom: Rect? = null
    var finger_spacing = 0f

    var takephotoRotation = 0
    var roll: Int = 0

    var mediaActionSound: MediaActionSound? = null

    var takePhotoData: PointOutItem? = null
    var mImageUsage: String = ""
    var updateComplete: Boolean = false

    lateinit var researchTaskData: Schedule

    lateinit var pointOutList: ArrayList<PointOut>

    lateinit var outItemList: List<PointOutItem>

    var diagnosisPartListView: ListView? = null

    var diagnosisPartCommentListView: ListView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        log(this.javaClass.name, "Start")
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        window.setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN)
        // レイアウト適用
        setContentView(R.layout.shooting_layout)

        // サムネイルのタッチリスナーを登録
        latest_photo_thumbnail.setOnSafeClickListener { forwardImageDetail() }

        // 戻るボタンのタッチリスナーを登録
        back_arrow.setOnSafeClickListener { backButton() }

        // 現地調査情報を登録
        researchTaskData = intent.getParcelableExtra<Schedule>("SURVEY_DATA") as Schedule

        // カメラの設定
        // 初期値　オート
        aeMode = CaptureRequest.CONTROL_AE_MODE_ON_AUTO_FLASH
    }

    override fun onStart() {
        super.onStart()

        // 撮影部位情報を通信で取得
        runOnUiThread {
            run {
                getResearchPointOut()
            }
        }

        if (this.settings.getBoolean("SHOOTING_ACTION_DELETE")) {
            thumbnail.visibility = View.GONE
        } else {
            val selected = this.settings.getString("SHOOTING_ACTION_SELECTED")
            if (selected != "") {
                val drawableId = if (selected == Constant.ResearchTaskPhotoType.SPARE.SELECTED) {
                    R.drawable.radius_frame_gray
                } else {
                    R.drawable.radius_frame_blue
                }
                thumbnail_background.setBackgroundResource(drawableId)
            }
        }

        // カメラをバックグランドで実行
        startBackgroundThread()
    }

    override fun onRestart() {
        super.onRestart()

        // カメラを終了
        closeCamera()

    }

    override fun onResume() {
        super.onResume()
        startBackgroundThread()

        initSensor()
        if (textureView.isAvailable) {
            openCamera(textureView.width, textureView.height)
        } else {
            textureView.surfaceTextureListener = surfaceTextureListener
        }
    }

    override fun onPause() {
        log(this.javaClass.name, "onPause")
        // カメラを終了
        closeCamera()
        stopBackgroundThread()

        super.onPause()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        log(this.javaClass.name, "onKeyDown")

        if (keyCode != KeyEvent.KEYCODE_BACK) {
            return super.onKeyDown(keyCode, event)
        }

        backButton()
        return super.onKeyDown(keyCode, event)
    }

    public override fun onDestroy() {
        super.onDestroy()

        sensorManager!!.unregisterListener(this)

        log(this.javaClass.name, "onDestroy End")
    }


    //region カメラ
    //https://github.com/googlearchive/android-Camera2Basic/blob/master/kotlinApp/Application/src/main/java/com/example/android/camera2basic/Camera2BasicFragment.kt

    /** カメラプレビューテクスチャー設定 */
    private val surfaceTextureListener = object : TextureView.SurfaceTextureListener {

        override fun onSurfaceTextureAvailable(texture: SurfaceTexture, width: Int, height: Int) {
            openCamera(width, height)
        }

        override fun onSurfaceTextureSizeChanged(texture: SurfaceTexture, width: Int, height: Int) {
            configureTransform(width, height)
        }

        override fun onSurfaceTextureDestroyed(texture: SurfaceTexture) = true

        override fun onSurfaceTextureUpdated(texture: SurfaceTexture) = Unit
    }


    private lateinit var cameraId: String

    private var captureSession: CameraCaptureSession? = null

    private var cameraDevice: CameraDevice? = null

    private lateinit var previewSize: Size

    private val stateCallback = object : CameraDevice.StateCallback() {

        // カメラ接続完了
        override fun onOpened(cameraDevice: CameraDevice) {
            log(this.javaClass.name, "CameraDevice.StateCallback() onOpened")

            cameraOpenCloseLock.release()
            <EMAIL> = cameraDevice

            // シャッター音をロードしておく
            mediaActionSound = MediaActionSound()
            mediaActionSound?.load(MediaActionSound.SHUTTER_CLICK)

            createCameraPreviewSession()
        }

        // カメラ切断
        override fun onDisconnected(cameraDevice: CameraDevice) {
            log(this.javaClass.name, "CameraDevice.StateCallback() onDisconnected")

            cameraOpenCloseLock.release()
            cameraDevice.close()
            mediaActionSound?.release()
            <EMAIL> = null
        }

        // カメラエラー
        override fun onError(cameraDevice: CameraDevice, error: Int) {
            log(this.javaClass.name, "CameraDevice.StateCallback() onError")

            onDisconnected(cameraDevice)
            <EMAIL>()
        }
    }

    private var backgroundThread: HandlerThread? = null

    private var backgroundHandler: Handler? = null

    private var imageReader: ImageReader? = null


    /**
     * カメラの画像が生成されるタイミングで呼び出される処理.
     *
     * */
    private val onImageAvailableListener = ImageReader.OnImageAvailableListener {

        val image: Image = it.acquireLatestImage() ?: return@OnImageAvailableListener

        // Imageをファイルに保存する
        val buffer = image.planes[0].buffer
        val bytes = ByteArray(buffer.remaining())
        buffer.get(bytes)

        val bitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.size)

        val filepath = TEMP_FILE_NAME + "_" + System.currentTimeMillis()

        this.setGallery(bitmap, filepath) { uri ->

            //キャッシュにファイルを作成
            val cacheFile = this.getUriToPath(uri) ?: return@setGallery

            // 設定で画像を保存するを選択していない場合
            if (!this.settings.getBoolean("PHOTO_SAVE")) {
                // ファイルを消す
                this.removeFile(filepath)
            }

            runOnUiThread {
                run {
                    // 画像ファイルを登録する.
                    imageUploadAsync(cacheFile)

                    // サムネイルを表示する
                    setThumbnail(cacheFile)

                    //
                    photoUri = cacheFile.absolutePath

                    if (!this.settings.getBoolean("PHOTO_SAVE")) {
                        var file = File("$cacheFile")
                        if(file.exists()) {
                            file.delete()
                        }
                    }
                }
            }
        }
        image.close()

        unlockFocus()
    }

    private lateinit var previewRequestBuilder: CaptureRequest.Builder

    private lateinit var previewRequest: CaptureRequest

    // 写真を撮るためのカメラの現在の状態
    private var state = STATE_PREVIEW

    // カメラを閉じる前にアプリが終了しないようにする
    private val cameraOpenCloseLock = Semaphore(1)

    // フラッシュサポート判定
    private var flashSupported = false

    // カメラセンサーの向き
    private var sensorOrientation = 0

    /**
     * キャプチャーしている間バックグランドで呼ばれる
     */
    private val captureCallback = object : CameraCaptureSession.CaptureCallback() {

        private fun process(result: CaptureResult) {
            when (state) {
                STATE_PREVIEW -> Unit //カメラのプレビューが正常に機能しているときは何もしない
                STATE_WAITING_LOCK -> capturePicture(result)
                STATE_WAITING_PRECAPTURE -> {
                    // 一部のデバイスではCONTROL_AE_STATEをnullとなる
                    val aeState = result.get(CaptureResult.CONTROL_AE_STATE)
                    log(this.javaClass.name, "STATE_WAITING_PRECAPTURE aeState = " + aeState?.toString())
                    if (aeState == null || aeState == CaptureResult.CONTROL_AE_STATE_PRECAPTURE || aeState == CaptureRequest.CONTROL_AE_STATE_FLASH_REQUIRED) {
                        state = STATE_WAITING_NON_PRECAPTURE
                    }
                }
                STATE_WAITING_NON_PRECAPTURE -> {
                    // 一部のデバイスではCONTROL_AE_STATEをnullとなる
                    val aeState = result.get(CaptureResult.CONTROL_AE_STATE)
                    log(this.javaClass.name, "STATE_WAITING_NON_PRECAPTURE aeState = " + aeState?.toString())
                    if (aeState == null || aeState != CaptureResult.CONTROL_AE_STATE_PRECAPTURE) {
                        state = STATE_PICTURE_TAKEN
                        captureStillPicture()
                    } else if (aeState != CaptureResult.CONTROL_AF_STATE_PASSIVE_SCAN && aeState == preAFState) {
                        sameAFStateCount++
                        // 同一状態上限
                        if (sameAFStateCount >= AF_SAME_STATE_REPEAT_MAX) {
                            state = STATE_PREVIEW
                            captureStillPicture()
                            return
                        }
                    } else {
                        sameAFStateCount = 0
                    }
                    preAFState = aeState
                }
            }
        }

        private fun capturePicture(result: CaptureResult) {
            val afState = result.get(CaptureResult.CONTROL_AF_STATE)
            log(this.javaClass.name, "STATE_WAITING_LOCK afState = " + afState?.toString())
            if (afState == null) {
                state = STATE_PREVIEW
                captureStillPicture()
            } else if (afState == CaptureResult.CONTROL_AF_STATE_FOCUSED_LOCKED || afState == CaptureResult.CONTROL_AF_STATE_NOT_FOCUSED_LOCKED) {
                // 一部のデバイスではCONTROL_AE_STATEをnullとなる
                val aeState = result.get(CaptureResult.CONTROL_AE_STATE)
                log(this.javaClass.name, "STATE_WAITING_LOCK aeState = " + aeState?.toString())
                if (aeState == null || aeState == CaptureResult.CONTROL_AE_STATE_CONVERGED) {
                    state = STATE_PICTURE_TAKEN
                    captureStillPicture()
                } else {
                    runPrecaptureSequence()
                }
            } else if (afState != CaptureResult.CONTROL_AF_STATE_PASSIVE_SCAN && afState == preAFState) {
                sameAFStateCount++
                // 同一状態上限
                if (sameAFStateCount >= AF_SAME_STATE_REPEAT_MAX) {
                    state = STATE_PREVIEW
                    sameAFStateCount = 0
                    captureStillPicture()
                    return
                }
            } else {
                sameAFStateCount = 0
            }
            preAFState = afState
        }

        override fun onCaptureProgressed(session: CameraCaptureSession, request: CaptureRequest, partialResult: CaptureResult) {
            process(partialResult)
        }

        override fun onCaptureCompleted(session: CameraCaptureSession, request: CaptureRequest, result: TotalCaptureResult) {
            process(result)
        }
    }

    /**
     * TextureViewが有効化されたらカメラを準備する
     * 画像生成の処理を設定
     * プレビュー画面のサイズを設定（画像サイズも）
     */
    private fun setUpCameraOutputs(width: Int, height: Int) {
        log(this.javaClass.name, "openCamera")

        cameraManager = getSystemService(Context.CAMERA_SERVICE) as CameraManager

        try {
            var characteristics: CameraCharacteristics?

            // 背面カメラを対象にカメラIDを検索
            for (cameraId in cameraManager!!.cameraIdList) {
                characteristics = cameraManager!!.getCameraCharacteristics(cameraId)

                // 全面カメラを使用しない
                val cameraDirection = characteristics.get(CameraCharacteristics.LENS_FACING)
                if (cameraDirection != null && cameraDirection == CameraCharacteristics.LENS_FACING_FRONT) {
                    continue
                }

                val map = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
                        ?: continue


                val largest = Collections.max(
                        Arrays.asList(*map.getOutputSizes(ImageFormat.JPEG)),
                        CompareSizesByArea())
                imageReader = ImageReader.newInstance(largest.width, largest.height,
                        ImageFormat.JPEG, /*maxImages*/ 2).apply {
                    setOnImageAvailableListener(onImageAvailableListener, backgroundHandler)
                }

                // センサーに相対的なプレビューサイズを取得するためにディメンションを交換する必要があるかどうかを確認
                // 座標をセット
                val displayRotation = windowManager.defaultDisplay.rotation

                sensorOrientation = characteristics.get(CameraCharacteristics.SENSOR_ORIENTATION)!!

                val swappedDimensions = areDimensionsSwapped(displayRotation)

                val displaySize = Point()
                windowManager.defaultDisplay.getSize(displaySize)
                val rotatedPreviewWidth = if (swappedDimensions) height else width
                val rotatedPreviewHeight = if (swappedDimensions) width else height
                /*
                var maxPreviewWidth = if (swappedDimensions) displaySize.y else displaySize.x
                var maxPreviewHeight = if (swappedDimensions) displaySize.x else displaySize.y

                if (maxPreviewWidth > MAX_PREVIEW_WIDTH) maxPreviewWidth = MAX_PREVIEW_WIDTH
                if (maxPreviewHeight > MAX_PREVIEW_HEIGHT) maxPreviewHeight = MAX_PREVIEW_HEIGHT
                 */

                // 大きすぎるプレビューサイズを使用しようとすると、カメラを超える可能性があります
                // バスの帯域幅制限。プレビューが表示されますが、
                // ガベージキャプチャデータ
                previewSize = chooseOptimalSize(map.getOutputSizes(SurfaceTexture::class.java),
                        rotatedPreviewWidth, rotatedPreviewHeight,
                        takePhotoWidth, takePhotoHeight)

                // TextureViewのアスペクト比を、選択したプレビューのサイズに合わせます。
                if (resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    textureView.setAspectRatio(previewSize.width, previewSize.height)
                } else {
                    textureView.setAspectRatio(previewSize.height, previewSize.width)
                }

                //フラッシュがサポートされているかどうかを確認
                flashSupported = characteristics.get(CameraCharacteristics.FLASH_INFO_AVAILABLE) == true


                this.cameraId = cameraId

                //実行可能なカメラが見つかり、メンバー変数の設定が完了
                //したがって、他の利用可能なカメラを反復処理する必要はありません。
                return
            }


        } catch (e: Exception) {
            log(this.javaClass.name, e.printStackTrace().toString())
            showAlertDialog("バッテリー残量または、カメラデバイスにアクセスできないため、カメラの起動ができません。",
                    this.baseContext!!.getString(R.string.company_info_title),
                    "OK",
                    DialogInterface.OnClickListener { _, _ ->
                        finish()
                    })
        }
    }

    /**
     * areDimensionsSwapped 画面角度変換
     *
     * @param displayRotation
     * @return
     */
    private fun areDimensionsSwapped(displayRotation: Int): Boolean {
        var swappedDimensions = false

        log(this.javaClass.name, "areDimensionsSwapped")

        when (displayRotation) {
            Surface.ROTATION_0, Surface.ROTATION_180 -> {
                if (sensorOrientation == 90 || sensorOrientation == 270) {
                    swappedDimensions = true
                }
            }
            Surface.ROTATION_90, Surface.ROTATION_270 -> {
                if (sensorOrientation == 0 || sensorOrientation == 180) {
                    swappedDimensions = true
                }
            }
            else -> {

            }
        }
        return swappedDimensions
    }

    /**
     * Opens the camera specified by [Camera2BasicFragment.cameraId].
     */
    private fun openCamera(width: Int, height: Int) {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED ||
            (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU &&
                    ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED)) {
            checkPermissions()
            return
        }

        setUpCameraOutputs(width, height)
        configureTransform(width, height)
        val manager = this.getSystemService(Context.CAMERA_SERVICE) as CameraManager
        try {
            // カメラが開くのを待ちます-2.5秒で十分
            if (!cameraOpenCloseLock.tryAcquire(2500, TimeUnit.MILLISECONDS)) {
                throw RuntimeException("Time out waiting to lock camera opening.")
            }
            manager.openCamera(cameraId, stateCallback, backgroundHandler)
        } catch (e: Exception) {

        } catch (e: InterruptedException) {
            throw RuntimeException("Interrupted while trying to lock camera opening.", e)
        }

    }

    private fun closeCamera() {
        log(this.javaClass.name, "closeCamera")

        try {
            cameraOpenCloseLock.acquire()
            if (captureSession != null) {
                captureSession?.close()
                captureSession = null
            }
            if (cameraDevice != null) {
                cameraDevice?.close()
                cameraDevice = null
            }
            if (imageReader != null) {
                imageReader?.close()
                imageReader = null
            }
        } catch (e: InterruptedException) {
            throw RuntimeException("Interrupted while trying to lock camera closing.", e)
        } finally {
            cameraOpenCloseLock.release()
        }
    }

    /**
     * カメラをバックグランドで実行
     */
    private fun startBackgroundThread() {
        if (backgroundThread == null) {
            backgroundThread = HandlerThread("Camera Background").also { it.start() }
        }
        if (backgroundHandler == null) {
            backgroundHandler = backgroundThread?.looper?.let { Handler(it) }
        }
    }

    private fun stopBackgroundThread() {
        log(this.javaClass.name, "stopBackgroundThread")
        backgroundThread?.quitSafely()
        try {
            backgroundThread?.join()
            backgroundThread = null
            backgroundHandler = null
        } catch (e: InterruptedException) {
            log(this.javaClass.name, e.printStackTrace().toString())
        }
    }

    /**
     *  createCameraPreviewSession カメラセッションを作成
     * */
    fun createCameraPreviewSession() {
        log(this.javaClass.name, "createCameraPreviewSession")

        try {

            val texture = textureView.surfaceTexture ?: return

            // デフォルトバッファのサイズを、必要なカメラプレビューのサイズに設定
            texture.setDefaultBufferSize(previewSize!!.width, previewSize!!.height)

            //プ レビューを開始するために必要な出力サーフェス
            val surface = Surface(texture)

            // 出力サーフェスを使用してCaptureRequest.Builderを設定
            previewRequestBuilder = cameraDevice!!.createCaptureRequest(
                    CameraDevice.TEMPLATE_PREVIEW
            )
            previewRequestBuilder.addTarget(surface)

            //カメラプレビュー用のCameraCaptureSessionを作成
            cameraDevice?.createCaptureSession(listOf(surface, imageReader?.surface),

                    object : CameraCaptureSession.StateCallback() {
                        override fun onConfigured(cameraCaptureSession: CameraCaptureSession) {
                            //カメラが既に閉じている場合
                            if (cameraDevice == null)
                                return

                            log(this.javaClass.name, "cameraDevice!!.createCaptureSession onConfigured")
                            //セッションの準備ができたら、プレビューの表示を開始
                            captureSession = cameraCaptureSession

                            try {

                                //カメラのプレビューでは、オートフォーカスを継続する必要がある
                                previewRequestBuilder.set(CaptureRequest.CONTROL_AF_MODE, CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE)

                                // Flashは必要に応じて自動的に有効
                                setAutoFlash(previewRequestBuilder)

                                // カメラのプレビューの表示を開始
                                previewRequest = previewRequestBuilder.build()
                                captureSession?.setRepeatingRequest(previewRequest, captureCallback, backgroundHandler)

                            } catch (e: Exception) {
                                log(this.javaClass.name, e.printStackTrace().toString())
                            }
                        }

                        override fun onConfigureFailed(cameraCaptureSession: CameraCaptureSession) {
                            log(this.javaClass.name, "onConfigureFailed")
                        }
                    }, null)
        } catch (e: Exception) {
            log(this.javaClass.name, e.printStackTrace().toString())
        }
    }

    /**
     * textureViewに表示するプレビューを操作する
     *
     * */
    private fun configureTransform(viewWidth: Int, viewHeight: Int) {
        log(this.javaClass.name, "configureTransform")

        val rotation = this.windowManager.defaultDisplay.rotation
        val matrix = Matrix()
        val viewRect = RectF(0f, 0f, viewWidth.toFloat(), viewHeight.toFloat())
        val bufferRect = RectF(0f, 0f, previewSize.height.toFloat(), previewSize.width.toFloat())
        val centerX = viewRect.centerX()
        val centerY = viewRect.centerY()

        if (Surface.ROTATION_90 == rotation || Surface.ROTATION_270 == rotation) {
            bufferRect.offset(centerX - bufferRect.centerX(), centerY - bufferRect.centerY())
            val scale = Math.max(
                    viewHeight.toFloat() / previewSize.height,
                    viewWidth.toFloat() / previewSize.width)
            with(matrix) {
                setRectToRect(viewRect, bufferRect, Matrix.ScaleToFit.FILL)
                postScale(scale, scale, centerX, centerY)
                postRotate((90 * (rotation - 2)).toFloat(), centerX, centerY)
            }
        } else if (Surface.ROTATION_180 == rotation) {
            matrix.postRotate(180f, centerX, centerY)
        }

        // 画像のキャプチャーの画質
        textureView.setTransform(matrix)
    }

    /**
     * lockFocus フォーカスロック
     */
    private fun lockFocus() {
        try {
            // This is how to tell the camera to lock focus.
            previewRequestBuilder.set(CaptureRequest.CONTROL_AF_TRIGGER, CameraMetadata.CONTROL_AF_TRIGGER_START)
            // Tell #captureCallback to wait for the lock.
            state = STATE_WAITING_LOCK
            captureSession?.capture(previewRequestBuilder.build(), captureCallback, backgroundHandler)
        } catch (e: Exception) {
            log(this.javaClass.name, e.printStackTrace().toString())
        }
    }

    /**
     * 再プレビュー開始
     */
    private fun runPrecaptureSequence() {
        try {
            // This is how to tell the camera to trigger.
            previewRequestBuilder.set(CaptureRequest.CONTROL_AE_PRECAPTURE_TRIGGER,
                    CaptureRequest.CONTROL_AE_PRECAPTURE_TRIGGER_START)
            // Tell #captureCallback to wait for the precapture sequence to be set.
            state = STATE_WAITING_PRECAPTURE
            captureSession?.capture(previewRequestBuilder.build(), captureCallback,
                    backgroundHandler)
        } catch (e: Exception) {
            log(this.javaClass.name, e.printStackTrace().toString())
        }

    }

    /**
     *  プレビューから画像を取得する処理(TakePhoto)
     */
    private fun captureStillPicture() {
        log(this.javaClass.name, "captureStillPicture")

        try {
            if (cameraDevice == null) return

            val rotation = this.windowManager.defaultDisplay.rotation

            // 固定で角度の範囲によって写真の位置を決める
            if (-40 <= roll && roll <= 40) { // 縦
                takephotoRotation = Surface.ROTATION_0
            } else if (-135 <= roll && roll <= -41) {
                takephotoRotation = Surface.ROTATION_90
            } else if ((-180 <= roll && roll <= -136) || (roll in 160..180)) {
                takephotoRotation = Surface.ROTATION_180
            } else if (roll in 39..159) {
                takephotoRotation = Surface.ROTATION_270
            }

            log(this.javaClass.name, "確定角度 = $takephotoRotation")
            log(this.javaClass.name, "roll = $roll")
            log(this.javaClass.name, "端末角度 = $rotation")
            log(this.javaClass.name, "センサー角度 = $sensorOrientation")

            // 写真用のbuilderを生成する
            val captureBuilder = cameraDevice?.createCaptureRequest(
                    CameraDevice.TEMPLATE_STILL_CAPTURE)?.apply {

                log(this.javaClass.name, "cameraDevice?.createCaptureRequest( CameraDevice.TEMPLATE_STILL_CAPTURE)?.apply")

                //撮影用のSurfaceを設定。
                addTarget(imageReader?.surface!!)

                // 画像の向きを調整 以下の説明文はサンプルより
                //センサーの向きはほとんどのデバイスで90、一部のデバイスでは270です（例：Nexus 5X）
                //これを考慮してJPEGを適切に回転させる必要があります。
                //方向が90のデバイスの場合は、ORIENTATIONSからマッピングを返します。
                //方向が270のデバイスの場合、JPEGを180度回転する必要があります.
                set(CaptureRequest.JPEG_ORIENTATION, (ORIENTATIONS.get(rotation) + sensorOrientation + 270) % 360)

                // Use the same AE and AF modes as the preview.
                set(CaptureRequest.CONTROL_AF_MODE, CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE)

                // ズーム設定
                set(CaptureRequest.SCALER_CROP_REGION, zoom)

            }?.also { setAutoFlash(it) }

            val captureCallback = object : CameraCaptureSession.CaptureCallback() {

                override fun onCaptureCompleted(session: CameraCaptureSession, request: CaptureRequest, result: TotalCaptureResult) {
                    unlockFocus()
                }

                override fun onCaptureFailed(session: CameraCaptureSession, request: CaptureRequest, failure: CaptureFailure) {
                    super.onCaptureFailed(session, request, failure)
                    unlockFocus()
                }
            }

            // シャッター音
            //mediaActionSound!!.play(MediaActionSound.SHUTTER_CLICK)

            captureSession?.apply {
                log(this.javaClass.name, "captureSession?.apply")

                stopRepeating()
                //abortCaptures()
                // 撮影の開始
                capture(captureBuilder!!.build(), captureCallback, null)
            }

        } catch (e: Exception) {
            log(this.javaClass.name, e.printStackTrace().toString())
        }
    }

    /**
     * unlockFocus 撮影ロック解除
     */
    private fun unlockFocus() {

        try {
            // オートフォーカストリガーをリセットする
            previewRequestBuilder.set(CaptureRequest.CONTROL_AF_TRIGGER, CameraMetadata.CONTROL_AF_TRIGGER_CANCEL)
            setAutoFlash(previewRequestBuilder)
            captureSession?.capture(previewRequestBuilder.build(), captureCallback, backgroundHandler)
            // この後、カメラは通常のプレビュー状態に戻ります。
            state = STATE_PREVIEW
            //captureSession?.setRepeatingRequest(previewRequest, captureCallback, backgroundHandler)

            captureSession?.setRepeatingRequest(previewRequestBuilder.build(), captureCallback, backgroundHandler)
        } catch (e: Exception) {
            log(this.javaClass.name, e.printStackTrace().toString())
        }
    }

    private fun setAutoFlash(requestBuilder: CaptureRequest.Builder) {
        if (flashSupported) {
            //requestBuilder.set(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON_AUTO_FLASH)
        }
    }

    // Utirity系
    companion object {

        private val MAX_PREVIEW_WIDTH = 1920

        private val MAX_PREVIEW_HEIGHT = 1080

        @JvmStatic
        private fun chooseOptimalSize(
                choices: Array<Size>,
                textureViewWidth: Int,
                textureViewHeight: Int,
                maxWidth: Int,
                maxHeight: Int
        ): Size {

            val pictureAspectSize = ArrayList<Size>()

            for (option in choices) {
                if (option.height == option.width * 3 / 4) {
                    if (option.width > maxWidth && option.height > maxHeight) {
                        pictureAspectSize.add(option)
                    }
                }
            }

            if (pictureAspectSize.size > 0) {
                return Collections.min(pictureAspectSize) { lhs, rhs -> lhs.width * lhs.height - rhs.width * rhs.height }
            } else {
                return choices[0]
            }
        }
    }

    //endregion

    //region センサー
    protected fun initSensor() {
        sensorManager = getSystemService(Context.SENSOR_SERVICE) as SensorManager
        sensorManager!!.registerListener(this, sensorManager!!.getDefaultSensor(Sensor.TYPE_ACCELEROMETER), SensorManager.SENSOR_DELAY_UI)
        sensorManager!!.registerListener(this, sensorManager!!.getDefaultSensor(Sensor.TYPE_MAGNETIC_FIELD), SensorManager.SENSOR_DELAY_UI)
    }

    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // センサーに利用
    }

    override fun onSensorChanged(event: SensorEvent?) {
        // log(this.javaClass.name, "onSensorChanged")

        when (event!!.sensor.type) {
            Sensor.TYPE_MAGNETIC_FIELD -> {
                magneticValues = event.values.clone()
            }
            Sensor.TYPE_ACCELEROMETER -> {
                accelerometerValues = event.values.clone()
            }
        }

        SensorManager.getRotationMatrix(inR, I, accelerometerValues, magneticValues)
        SensorManager.remapCoordinateSystem(inR, SensorManager.AXIS_X, SensorManager.AXIS_Z, outR)
        SensorManager.getOrientation(outR, orientationValues)

        roll = (orientationValues[2] * RAD2DEG).toInt()

        // 以下、縦横判定処理
        val absRoll = Math.abs(roll)
        if (this.mPreOrientation == -1) {
            this.mPreOrientation = if (absRoll < VERTICAL_TO_HORIZONTAL_DEGREE) ORIENTATION_VERTICAL else ORIENTATION_HORIZONTAL
        } else if (absRoll < 90) {
            this.mPreOrientation = getOrientation(this.mPreOrientation, roll)
        } else {
            // プラマイ90度を超える場合は90度未満に置き換えて向きを反転させる。
            val plusMinus = if (roll >= 0) 1 else -1
            val localRoll = (absRoll - 90) * plusMinus
            val preOrientation = invertOrientation(mPreOrientation)
            mPreOrientation = invertOrientation(getOrientation(preOrientation, localRoll))
        }

        // 縦向き範囲の場合はメッセージを表示
        if (mPreOrientation == ORIENTATION_VERTICAL) {
            warning_message_frame!!.visibility = View.VISIBLE
        } else {
            warning_message_frame!!.visibility = View.GONE
        }
    }

    /**
     * 与えられた端末の向きを反転させます。
     *
     * @param orientation 端末の向き([.ORIENTATION_HORIZONTAL] or [.ORIENTATION_VERTICAL] or -1)
     * @return 反転した端末の向き（[.ORIENTATION_HORIZONTAL] or [.ORIENTATION_VERTICAL] or -1）
     */
    private fun invertOrientation(orientation: Int): Int {
        return if (orientation == ORIENTATION_HORIZONTAL) {
            ORIENTATION_VERTICAL
        } else if (orientation == ORIENTATION_VERTICAL) {
            ORIENTATION_HORIZONTAL
        } else {
            -1
        }
    }

    /**
     * 一つ前の端末の向きと現在の傾きを元に、現在の端末の向きを求めます
     *
     * @param preOrientation 一つ前の端末の向き([.ORIENTATION_HORIZONTAL] or [.ORIENTATION_VERTICAL] or -1)
     * @param roll           傾き(90度未満)
     * @return 現在の端末の向き（[.ORIENTATION_HORIZONTAL] or [.ORIENTATION_VERTICAL] or -1）
     */
    private fun getOrientation(preOrientation: Int, roll: Int): Int {
        // log(this.javaClass.name, "getOrientation")

        val absRoll = Math.abs(roll)
        return if (preOrientation == ORIENTATION_VERTICAL) {
            if (absRoll < VERTICAL_TO_HORIZONTAL_DEGREE)
                ORIENTATION_VERTICAL
            else
                ORIENTATION_HORIZONTAL
        } else if (preOrientation == ORIENTATION_HORIZONTAL) {
            if (absRoll < HORIZONTAL_TO_VERTICAL_DEGREE)
                ORIENTATION_VERTICAL
            else
                ORIENTATION_HORIZONTAL
        } else {
            -1
        }
    }

    // endregion

    /**
     * backButton 前画面へ戻る
     */
    private fun backButton() {
        log(this.javaClass.name, "backButton")

        val intent = Intent(application, SurveyPropertyActivity::class.java)
        intent.putExtra("RESEARCH_ID", researchTaskData.researchTaskID)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_out_down, R.anim.slide_in_up)
    }

    /**
     * forwardImageDetail 画像詳細へ遷移
     */
    private fun forwardImageDetail() {
        log(this.javaClass.name, "forwardImageDetail")

        // 写真が登録エラーの場合、登録完了していない場合は遷移しない
        if (photoId == null) {
            return
        }
        if (photoUri == null) {
            return
        }

        val intent = Intent(application, ShootingImageDetailActivity::class.java)

        this.settings.put("PHOTO_ID", photoId)
        this.settings.put("PHOTO_URI", photoUri)
        this.settings.put("FORWARD_IMAGE_DETAIL", "SHOOTING")

        startActivity(intent)
        overridePendingTransition(0, 0)
    }

    /**
     *  setCategoryListView 撮影部位のビューを追加
     * */
    private fun setCategoryListView() {

        val arrayAdapter = CategoryPartAdapter(this, pointOutList)
        diagnosisPartListView = findViewById<ListView>(R.id.diagnosis_part)

        diagnosisPartListView!!.onItemClickListener = categoryPartListOnclick
        diagnosisPartListView!!.adapter = arrayAdapter

        // 画面表示時に撮影コメントを先頭の撮影部位で初期化して表示
        outItemList = pointOutList[0].PointOutItems

        // コメントリストを初期設定
        val commentAdapter = DiagnosisPartCommentAdapter(this, outItemList, shutterMap)

        diagnosisPartCommentListView = findViewById<ListView>(R.id.diagnosis_comment)

        diagnosisPartCommentListView!!.adapter = commentAdapter
    }

    /**
     *  categoryPartListOnclick 撮影部位タップ処理
     * */
    private val categoryPartListOnclick = AdapterView.OnItemClickListener { _, _, position, _ ->
        //setCategoryItem(position)
        outItemList = pointOutList[position].PointOutItems
        (diagnosisPartCommentListView!!.adapter as DiagnosisPartCommentAdapter).reflesh(outItemList)
    }

    /**
     * 撮影タップ時の処理
     * 撮影データにコメントを付加し、サーバー送信する。
     *
     * */
    private var shutterMap: MutableMap<String, (PointOutItem, String) -> Unit> = mutableMapOf("shutterAction" to fun(data: PointOutItem, select: String) {
        log(this.javaClass.name, "shutter_action ")

        // 撮影した写真情報の取得
        takePhotoData = data.copy()
        // 報告用、予備用の設定
        mImageUsage = select

        //addPhotoCount()

        lockFocus()

    })


    override fun onTouchEvent(event: MotionEvent): Boolean {

        try {
            if (cameraManager == null) return false
            val characteristics = cameraManager!!.getCameraCharacteristics(cameraId!!)
            val zoomRect = characteristics.get(CameraCharacteristics.SENSOR_INFO_ACTIVE_ARRAY_SIZE)
            val action = event.action
            val currentFingerSpacing: Float
            if (event.pointerCount > 1) {
                //マルチタッチロジック
                currentFingerSpacing = getFingerSpacing(event)

                if (finger_spacing.toInt() != 0) {
                    if (currentFingerSpacing > finger_spacing && MAX_ZOON > zoomLevel.toInt()) {
                        zoomLevel += 1f

                    } else if (currentFingerSpacing < finger_spacing && zoomLevel.toInt() > 1) {
                        zoomLevel -= 1f
                    }

                    if (zoomLevel >= MAX_ZOON) {
                        zoomLevel = MAX_ZOON.toFloat()
                    }

                    val minW = zoomRect!!.width() / MAX_ZOON
                    val minH = zoomRect.height() / MAX_ZOON
                    val difW = zoomRect.width() - minW
                    val difH = zoomRect.height() - minH
                    var cropW = difW / 100 * zoomLevel
                    var cropH = difH / 100 * zoomLevel
                    cropW -= (cropW.toBits() and 0b0011)
                    cropH -= (cropH.toBits() and 0b0011)
                    zoom = Rect(cropW.toInt(), cropH.toInt(), zoomRect.width() - cropW.toInt(), zoomRect.height() - cropH.toInt())
                    previewRequestBuilder.set(CaptureRequest.SCALER_CROP_REGION, zoom)
                }
                finger_spacing = currentFingerSpacing
            } else {
                if (action == MotionEvent.ACTION_UP) {
                    //single touch logic
                }
            }

            captureSession!!.setRepeatingRequest(previewRequestBuilder.build(), captureCallback, backgroundHandler)

        } catch (e: Exception) {
            throw RuntimeException("can not access camera.", e)
        }

        return true
    }

    private fun getFingerSpacing(event: MotionEvent): Float {
        val x = event.getX(0) - event.getX(1)
        val y = event.getY(0) - event.getY(1)
        return Math.sqrt((x * x + y * y).toDouble()).toFloat()
    }

    private fun setThumbnail(file: File) {

        // ビットマップを表示
        val bitmap = BitmapUtil.decodeSampledBitmapFromResource(file.path, takePhotoWidth / 4, takePhotoHeight / 4)
                ?: return
        val drawableId = if (mImageUsage == Constant.ResearchTaskPhotoType.SPARE.SELECTED) {
            R.drawable.radius_frame_gray
        } else {
            R.drawable.radius_frame_blue
        }
        thumbnail_background.setBackgroundResource(drawableId)
        latest_photo_thumbnail.setImageBitmap(bitmap)
    }

    //----------------------------------- 以下 通信系 ------------------------------------------

    // 撮影項目と診断を確認
    private fun getResearchPointOut() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {
                showHub()
            }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")


                    if (status.getInt("StatusCode") == 0) {
                        val pointOuts = result.getJSONArray("PointOuts")
                        val poointOutObject = pointOuts.getJSONObject(0)
                        val categorys = poointOutObject.getJSONArray("PointOutCategorys")

                        pointOutList = arrayListOf()

                        // PointOutの取得
                        for (i in 0 until categorys.length()) {
                            val jsonObject = categorys.getJSONObject(i)
                            val pointOut = PointOut(CategoryName = jsonObject.getString("CategoryName"),
                                    DisplayOrder = jsonObject.getString("DisplayOrder"),
                                    PointOutCategoryID = jsonObject.getString("PointOutCategoryID"),
                                    PointOutID = jsonObject.getString("PointOutID"),
                                    PointOutItems = emptyList(),
                                    SelectedPhotoCount = jsonObject.getString("SelectedPhotoCount"),
                                    UnselectedPhotoCount = jsonObject.getString("UnselectedPhotoCount"))

                            val categoryItems = jsonObject.getJSONArray("PointOutItems")

                            val pointOutItemList = arrayListOf<PointOutItem>()

                            // categoryの取得
                            for (j in 0 until categoryItems.length()) {
                                val itemsJson = categoryItems.getJSONObject(j)
                                val pointOutItem = PointOutItem(DisplayOrder = itemsJson.getString("DisplayOrder"),
                                        ItemName = itemsJson.getString("ItemName"),
                                        ItemShortName = itemsJson.getString("ItemShortName"),
                                        PointOutCategoryID = itemsJson.getString("PointOutCategoryID"),
                                        PointOutID = itemsJson.getString("PointOutID"),
                                        PointOutItemID = itemsJson.getString("PointOutItemID"),
                                        Remarks = itemsJson.getString("Remarks"),
                                        SelectedPhotoCount = itemsJson.getString("SelectedPhotoCount"),
                                        UnselectedPhotoCount = itemsJson.getString("UnselectedPhotoCount"))

                                pointOutItemList.add(pointOutItem)

                            }

                            pointOut.PointOutItems = pointOutItemList

                            pointOutList.add(pointOut)
                        }

                        setCategoryListView()

                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
                hideHub()
            }
        })

        val researchTask = researchTaskData!!.researchTask

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ResearchTaskID = researchTask!!.ResearchTaskID

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskPointOutItems

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    // 写真をアップロード（カメラから）
    private fun imageUploadAsync(file: File) {

        //通信前に撮影枚数を増やす
        addPhotoCount()

        photoId = null

        log(context?.javaClass!!.name, Uri.fromFile(file).toString())
        val rotation: String = this.getImageRotation(Uri.fromFile(file), file.absolutePath)
        val params: Dictionary<String, String> = Hashtable<String, String>()
        params.put("UserID", this.settings.getString("USER_ID"))
        params.put("ResearchTaskID", researchTaskData.researchTaskID)
        params.put("PhotoDate", DateUtil.getDateFormat("yyyy-MM-dd HH:mm"))
        params.put("PointOutID", takePhotoData!!.PointOutID)
        params.put("PointOutItemID", takePhotoData!!.PointOutItemID)
        params.put("PointOutCateogryID", takePhotoData!!.PointOutCategoryID)
        params.put("Rotation", rotation)
        params.put("Selected", mImageUsage)

        // URL作成
        val url: String = Constant.URL + Constant.ResearchPhotoAdd
        val asyncImageUploader = AsyncImageUploader(this, object : AsyncImageUploader.AsyncCallback {
            // 実行前
            override fun preExecute() {
                updateComplete = false
                // ビットマップを表示
                runOnUiThread {
                    run {
                        thumbnail.visibility = View.VISIBLE
                        image_progressBar.visibility = View.VISIBLE
                    }
                }
            }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // ローディングを開始

                        // サーバー上のデータとテキストで表示されているデータがあっているか比較
                        val photoData = result.getJSONArray("ResearchPhotoAttributes")
                        val photoJsonData = photoData.getJSONObject(0)

                        val researchPhotoAttribute = ResearchPhotoAttribute.init(photoJsonData)
                        photoId = researchPhotoAttribute.PhotoID

                        // リストの更新
                        setPointOutList(researchPhotoAttribute)
                        setPointOutItemList(outItemList, toPointOutItem(researchPhotoAttribute))

                        // CategoryAdapter(左)の更新
                        (diagnosisPartListView!!.adapter as CategoryPartAdapter).reflesh(pointOutList)

                        // CommentAdapter(右)の更新
                        (diagnosisPartCommentListView!!.adapter as DiagnosisPartCommentAdapter).reflesh(outItemList)
                    } else {
                        // 失敗時
                        log(context?.javaClass!!.name, status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    updateComplete = true

                    // ローディングを終了する、サムネイルのセットアップをする
                    image_progressBar.visibility = View.GONE
                    // サムネイルフェードアウト
                    //handle.removeCallbacks(null);
                    //handle.postDelayed(runnable, 5000)

                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
            }
        }, params)

        // 非同期通信開始
        // 第1引数：URL
        // 第2引数：imageFilePath
        // 第3引数：rotation
        asyncImageUploader.execute(url, file.absolutePath, rotation)
    }


    /**
     * 撮影部位と項目に＋１カウント
     */
    fun addPhotoCount(){

        // カテゴリー(撮影部位)の撮影枚数カウントアップ
        pointOutList.forEach{
            // 撮影した写真と同じデータである
            if (it.PointOutCategoryID == takePhotoData!!.PointOutCategoryID){
                if (mImageUsage == "0"){
                    it.UnselectedPhotoCount = (it.UnselectedPhotoCount.toInt() + 1).toString()
                }else{
                    it.SelectedPhotoCount = (it.SelectedPhotoCount.toInt() + 1).toString()
                }
            }
        }
        // テキストビュー更新
        (diagnosisPartListView!!.adapter as CategoryPartAdapter).reflesh(pointOutList)

        takePhotoData!!.let{
            // 報告用写真判定
            if (mImageUsage == "0"){
                it.UnselectedPhotoCount = (it.UnselectedPhotoCount.toInt() + 1).toString()
            }else{
                it.SelectedPhotoCount = (it.SelectedPhotoCount.toInt() + 1).toString()
            }
        }
        setPointOutItemList(outItemList, takePhotoData!!)
        // テキストビュー更新
        (diagnosisPartCommentListView!!.adapter as DiagnosisPartCommentAdapter).reflesh(outItemList)

    }

    /**
     *  ResearchPhotoAttributeをpointOutListと入れ替える
     */
    fun setPointOutList(rpa: ResearchPhotoAttribute) {
        pointOutList.forEach { po ->
            // 撮影した写真と同じデータである
            if (po.PointOutItems.any {
                        it.PointOutCategoryID == rpa.PointOutCategoryID &&
                                it.PointOutID == rpa.PointOutID &&
                                it.PointOutItemID == rpa.PointOutItemID
                    }) {
                setPointOutItemList(po.PointOutItems, toPointOutItem(rpa))
                // 撮影枚数の更新
                po.SelectedPhotoCount = rpa.PointOutCategorySelectedPhotoCount
                po.UnselectedPhotoCount = rpa.PointOutCategoryUnselectedPhotoCount
            }
        }
    }

    /**
     *  List<PointOutItem>にPointOutItemと入れる
     */
    fun setPointOutItemList(pointOutItems: List<PointOutItem>, poi: PointOutItem) {
        pointOutItems.forEach {
            // 撮影した写真と同じデータである
            if (it.PointOutCategoryID == poi.PointOutCategoryID &&
                    it.PointOutID == poi.PointOutID &&
                    it.PointOutItemID == poi.PointOutItemID) {
                // 撮影枚数の更新
                it.SelectedPhotoCount = poi.SelectedPhotoCount
                it.UnselectedPhotoCount = poi.UnselectedPhotoCount
            }
        }
    }

    /**
     *　ResearchPhotoAttributeをPointOutItemに変換
     */
    fun toPointOutItem(rpa: ResearchPhotoAttribute): PointOutItem {
        return PointOutItem(DisplayOrder = rpa.DisplayOrder,
                ItemName = rpa.PointOutItemName,
                ItemShortName = rpa.PointOutItemName,
                PointOutCategoryID = rpa.PointOutCategoryID,
                PointOutID = rpa.PointOutID,
                PointOutItemID = rpa.PointOutItemID,
                Remarks = rpa.Remarks,
                SelectedPhotoCount = rpa.PointOutItemSelectedPhotoCount,
                UnselectedPhotoCount = rpa.PointOutItemUnselectedPhotoCount
        )
    }
}

@RequiresApi(Build.VERSION_CODES.LOLLIPOP)
internal class CompareSizesByArea : Comparator<Size> {

    // ここでキャストして、乗算がオーバーフローしないようにします
    override fun compare(lhs: Size, rhs: Size) =
            signum(lhs.width.toLong() * lhs.height - rhs.width.toLong() * rhs.height)

}