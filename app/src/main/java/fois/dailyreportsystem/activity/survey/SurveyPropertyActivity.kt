package fois.dailyreportsystem.activity.survey

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.view.MotionEvent
import android.view.View
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.Schedule
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.survey_property_layout.*
import org.json.JSONObject
import java.io.File
import java.util.*
import kotlin.collections.ArrayList
import android.os.Bundle as Bundle1


class SurveyPropertyActivity : BaseActivity() {
    private val REQUEST_GALLERY = 1

    private var shootingOrientationSelect = ""

    private var schedule: Schedule? = null

    private var listSelectedPhoto: ArrayList<String> = ArrayList()
    private var listSelectedPhotoSorted: ArrayList<String> = ArrayList()
    private var mapListPhoto: MutableMap<Date, String> = mutableMapOf()


    /* -------------------- ライフサイクル -------------------- */
    /*
    起動：onCreate→onResume
    スリープから復帰、他画面からの復帰：onRestart→onResume
     */
    override fun onCreate(savedInstanceState: Bundle1?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.survey_property_layout)

        back_arrow.setOnSafeClickListener { backSurveyList() }

        // 撮影方向初期設定 // 連打有効
        check_left.setOnClickListener { view -> shootingOrientationOnClick(view) }
        check_right.setOnClickListener { view -> shootingOrientationOnClick(view) }

        // カメラロールボタンタップ
        photo_detail_footer_gallery.setOnTouchListener { view, motionEvent ->
            when (motionEvent.action) {
                MotionEvent.ACTION_DOWN -> {
                    photo_detail_footer_gallery_icon.setImageResource(R.drawable.album_on)
                    photo_detail_footer_gallery_text.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
                }
                MotionEvent.ACTION_UP -> {
                    photo_detail_footer_gallery_icon.setImageResource(R.drawable.album)
                    photo_detail_footer_gallery_text.setTextColor(ContextCompat.getColor(this, R.color.white))
                    view.performClick()
                }
            }

            return@setOnTouchListener true
        }

        photo_detail_footer_gallery.setOnClickListener {
            selectPhoto()
        }

        photo_detail_footer_camera.setOnTouchListener { view, motionEvent ->
            when (motionEvent.actionMasked) {
                MotionEvent.ACTION_DOWN -> {
                    photo_detail_footer_camera_icon.setImageResource(R.drawable.camera_white_on)
                    photo_detail_footer_camera_text.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
                }
                MotionEvent.ACTION_UP -> {
                    photo_detail_footer_camera_icon.setImageResource(R.drawable.camera_white)
                    photo_detail_footer_camera_text.setTextColor(ContextCompat.getColor(this, R.color.white))
                    view.performClick()
                }
            }

            return@setOnTouchListener true
        }

        photo_detail_footer_camera.setOnClickListener{
            forwardShooting()
        }

        offRight()
        offLeft()

        // 撮影写真一覧へ初期は非活性
        image_list_icon.setImageResource(R.drawable.photo_list_hover)
        image_list_link.setTextColor(ContextCompat.getColor(this, R.color.gray))

        log(context?.javaClass!!.name, "onCreate")
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        this.settings.put("TASK_NAME", "")
        getPropertyData()

        log(context?.javaClass!!.name, "onResume")
    }

    // スリープからの復帰、他画面からの復帰
    override fun onRestart() {
        super.onRestart()

        log(context?.javaClass!!.name, "onRestart")
    }

    override fun onPause() {
        super.onPause()

        log(context?.javaClass!!.name, "onPause")
    }

    /* -------------------- 画面遷移 -------------------- */

    /**
     * forwardShooting 撮影画面遷移
     *
     * */
    private fun forwardShooting() {

        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED ||
            (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU &&
                    ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED)) {
            return
        }

        val intent = Intent(applicationContext, ShootingActivity::class.java)
        intent.putExtra("SURVEY_DATA", schedule)
        startActivity(intent)
        finish()
    }

    private fun forwardShootingGallery() {

        val intent = Intent(applicationContext, ShootingGalleryActivity::class.java)

        this.settings.put("TASK_NAME", schedule!!.researchTask.TaskName)

        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
    }

    // 調査物件一覧画面へ戻る
    private fun backSurveyList() {
        back_arrow.setImageResource(R.drawable.white_arrow_l_hover)
        finish()
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
    }

    // カメラロールボタン
    private fun selectPhoto() {
        checkPermissions()
        
        // ストレージの権限がある場合はギャラリー起動
        openGallery()
    }

    // ギャラリー起動
    private fun openGallery() {
        if(clickPosition == null) {
            clickPosition = true
            val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
            intent.type = "image/jpeg"
            intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
            startActivityForResult(intent, REQUEST_GALLERY)
         }
    }

    /**
     * ギャラリーの選択結果
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            REQUEST_GALLERY -> {
                if (resultCode != Activity.RESULT_OK) {
                    // キャンセルなどで選択しなかった場合
                    return
                }
                // if multiple images are selected
                val uriList = mutableListOf<Uri>()
                val itemCount = data?.clipData?.itemCount ?: 0
                if(itemCount == 0){
                    data?.data?.let { uriList.add(it) }
                }else {
                    for (i in 0 until itemCount) {
                        data?.clipData?.getItemAt(i)?.uri?.let { uriList.add(it) }
                    }
                }
                listSelectedPhoto.clear()
                for (i in 0 until uriList.size) {
                    uriList[i].let {
                        val cursor: Cursor? = contentResolver.query(it, null, null, null, null)
                        if (cursor != null) {
                            if (cursor.moveToFirst()) {
                                val pathIndex: Int = cursor.getColumnIndex(MediaStore.Images.Media.DATA)
                                listSelectedPhoto.add(cursor.getString(pathIndex))
                            }
                            cursor.close();
                        }
                    }
                }
                if(listSelectedPhoto.size > 0) {
                    for (i in 0 until listSelectedPhoto.size) {
                        val file = File(listSelectedPhoto[i])
                        if (file.exists()) {
                            val lastModDate = Date(file.lastModified())
                            mapListPhoto.put(lastModDate, listSelectedPhoto[i])
                        }
                    }
                    val sortedMapPhotoByDate = mapListPhoto.toSortedMap(compareBy  { it })
                    if(sortedMapPhotoByDate.size > 0) {
                        for (i in 0 until sortedMapPhotoByDate.size) {
                            val keyByIndex = sortedMapPhotoByDate.keys.elementAt(i)
                            listSelectedPhotoSorted.add(sortedMapPhotoByDate.getValue(keyByIndex))
                        }
                    }

                    val intent: Intent = Intent(applicationContext, RegistMultipleImageDetailActivity::class.java)
                    intent.putStringArrayListExtra("selectPhotoList", listSelectedPhotoSorted)
                    intent.putExtra(ConstantParameters.INTENT_KEY_RESEARCHTASKID, schedule!!.researchTaskID)
                    intent.putExtra("SURVEY_DATA", schedule)
                    startActivity(intent)
                    finish()
                    overridePendingTransition(0, 0)
                } else {
                    ShowMessages().Show(context, context?.getString(R.string.photo_select_error)!!)
                    return
                }

//                val uri = data?.data!!
//                val path = getUriToPath(uri)
//                if (path == "") {
//                    ShowMessages().Show(context, context?.getString(R.string.photo_select_error)!!)
//                    return
//                }
//                val intent = Intent(application, RegistImageDetailActivity::class.java)
//                intent.putExtra(ConstantParameters.INTENT_KEY_IMAGEPASS, path)
//                intent.putExtra(ConstantParameters.INTENT_KEY_RESEARCHTASKID, schedule!!.researchTaskID)
//                startActivity(intent)
//                finish()
            }
            else -> {
            }
        }
    }

    /**
     * URIからファイルパスを取得する(ローカル・SDストレージからのみ)
     */
    private fun getUriToPath(uri: Uri): String {
        val projection = arrayOf(MediaStore.MediaColumns.DATA)
        val cursor = context!!.contentResolver.query(uri, projection, null, null, null) ?: return ""
        var path: String? = null
        if (cursor.moveToFirst()) {
            path = cursor.getString(0)
        }

        cursor.close()
        if (path == null) {
            return ""
        }

        val file = File(path)
        return file.absolutePath
    }

    /* -------------------- 画面データ設定 -------------------- */

    /**
     * setupData データ設定.
     *
     * */
    @SuppressLint("SetTextI18n")
    private fun setupData() {

        val researchTask = schedule!!.researchTask

        val direction = researchTask.Direction

        // 左回り、右回り初期選択
        if (Constant.ResearchTaskDirection.LEFT.DIRECTION == direction) {
            onLeft()
            offRight()
        } else if (Constant.ResearchTaskDirection.RIGHT.DIRECTION == direction) {
            offLeft()
            onRight()
        }

        // 写真登録状況により遷移可能にする。
        if (researchTask.PhotoCount != "0") {
            image_list_icon.setImageResource(R.drawable.photo_list)
            image_list_link.setTextColor(ContextCompat.getColor(this, R.color.light_blue))
            image_list.setDrawable(ContextCompat.getDrawable(context!!, R.drawable.ripple_white))
            image_list.setOnSafeClickListener { forwardShootingGallery() }
        } else {
            image_list_icon.setImageResource(R.drawable.photo_list_hover)
            image_list_link.setTextColor(ContextCompat.getColor(this, R.color.light_gray))
        }

        survey_property_toolbar_title.text = researchTask.TaskName + " 様邸"

        survey_staff.text = schedule!!.userName

        request_company.text = researchTask.MakerName
        request_staff.text = researchTask.MakerUserName

        survey_area.text = researchTask.AreaName
        survey_address.text = researchTask.PrefName + researchTask.CityName + researchTask.TaskAddress

        survey_date.text =  researchTask.PreferredDate1.toDate()!!.toString("yyyy年MM月dd日 (E) HH:mm") +  "～"

    }

    /**
     * 端末の戻るボタン
     */
    override fun onBackPressed() {
        backSurveyList()
    }

    private fun shootingOrientationOnClick(view: View) {
        when (view.id) {
            check_left.id -> {
                shootingOrientationSelect = Constant.ResearchTaskDirection.LEFT.DIRECTION

                onLeft()
                offRight()
                researchTaskModify()
            }
            check_right.id -> {
                shootingOrientationSelect = Constant.ResearchTaskDirection.RIGHT.DIRECTION

                offLeft()
                onRight()
                researchTaskModify()
            }
        }
    }

    private fun onLeft() {
        left_check_image.setImageResource(R.drawable.circle_blue_check)
        left_text.setTextColor(ContextCompat.getColor(this, R.color.light_blue))
        left_icon.setImageResource(R.drawable.turn_left)
    }

    private fun offLeft() {
        left_check_image.setImageResource(R.drawable.circle_non_check)
        left_text.setTextColor(ContextCompat.getColor(this, R.color.gray))
        left_icon.setImageResource(R.drawable.turn_left_off)
    }

    private fun onRight() {
        right_check_image.setImageResource(R.drawable.circle_blue_check)
        right_text.setTextColor(ContextCompat.getColor(this, R.color.light_blue))
        right_icon.setImageResource(R.drawable.turn_right)
    }

    private fun offRight() {
        right_check_image.setImageResource(R.drawable.circle_non_check)
        right_text.setTextColor(ContextCompat.getColor(this, R.color.gray))
        right_icon.setImageResource(R.drawable.turn_right_off)
    }

    /* -------------------- 通信処理 -------------------- */

    /**
     * getPropertyData 調査物件情報取得.
     *
     * */
    private fun getPropertyData() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val taskArray = result.getJSONArray("Schedules")
                        val array = JsonHandling.toScheduleArrayList(taskArray)
                        if (array != null && array.count() > 0){
                            schedule = array[0]
                        }
                        setupData()

                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ResearchTaskID = this.settings.getString("RESEARCH_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTask

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * researchTaskModify 写真の右回り、左回りを変更する.
     *
     * */
    private fun researchTaskModify() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") != 0) {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        schedule!!.researchTask.Direction = shootingOrientationSelect

        // Schedule変更データの作成
        val scheduleModifyData = schedule!!.getBaseModifyData()
        val researchTask = schedule!!.researchTask!!.getBaseModifyData()

        researchTask["Direction"] = schedule!!.researchTask!!.Direction

        scheduleModifyData["ResearchTask"] = researchTask

        val list = ArrayList<MutableMap<String, Any?>>()
        list.add(scheduleModifyData)

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.JsonData = Gson().toJson(list)

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskModify

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

}
