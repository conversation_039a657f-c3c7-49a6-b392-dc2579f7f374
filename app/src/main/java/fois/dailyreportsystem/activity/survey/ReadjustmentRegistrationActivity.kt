package fois.dailyreportsystem.activity.survey

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Paint
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.MotionEvent
import android.view.Window
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.ReceptionTime
import fois.dailyreportsystem.data.Schedule
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.readjustment_regstration_layout.*
import org.json.JSONObject
import java.util.*
import fois.dailyreportsystem.data.DateDialogUtil


class ReadjustmentRegistrationActivity : BaseActivity() {

    var schedule: Schedule? = null

    private var freeScheduleList: ArrayList<Schedule>? = ArrayList()

    private var receptionTimes: ArrayList<ReceptionTime>? = ArrayList()

    private var countHub = 0

    /* -------------------- ライフサイクル -------------------- */
    /*
    起動：onCreate→onResume
    スリープから復帰、他画面からの復帰：onRestart→onResume
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(fois.dailyreportsystem.R.layout.readjustment_regstration_layout)

        task_name.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) { }
            override fun afterTextChanged(s: Editable) { validation() }
        })

        research_date1.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) { validation() }
        })

        back_button.setOnSafeClickListener { onBackPressed() }
        readjustment.setOnSafeClickListener { researchTaskModify() }
        readjustment_registration.setOnSafeClickListener { researchTaskModify() }
        cancel_text.setOnSafeClickListener { openDialog() }

        validation()

        log(context?.javaClass!!.name, "onCreate")
    }

    override fun onResume() {
        super.onResume()

        getResearch()
        getAreaAvailability()
        getReceptionTime()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }


        log(context?.javaClass!!.name, "onResume")
    }

    // スリープからの復帰、他画面からの復帰
    override fun onRestart() {
        super.onRestart()
        log(context?.javaClass!!.name, "onRestart")
    }

    override fun onPause() {
        super.onPause()
        log(context?.javaClass!!.name, "onPause")
    }

    // 画面タッチでキーボードを閉じる
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        hideKeyboard(readjustment_registration_layout)
        return super.dispatchTouchEvent(ev)
    }

    /**
     * validation() 更新対象チェック .
     *
     */
    private fun validation(): Boolean {

        // 邸名 必須チェック
        if (task_name.text.toString().isBlank()) {
            updatable(false)
            return false
        }

        // 調査日 必須チェック
        if (research_date1.text.toString().isBlank()) {
            updatable(false)
            return false
        }

        val startDate = research_date1.text.toString().toDate(Constant.FORMAT_DATETIME_WEEK_SLASH)
        if(startDate == null){
            updatable(false)
            return false
        }

        // 昨日日付チェック
        if (startDate < Date().startTime()) {
            updatable(false)
            cancelLine(false)
            return false
        }

        // 3ヵ月以内チェック
        // 3ヵ月後を超えてればエラー
        val endDate = startDate.addMinutes(120)
        if (endDate > endDate.addMonth(3).endTime()) {
            updatable(false)
            cancelLine(false)
            return false
        }

        // 空き状況チェック
        if (freeScheduleList!!.find { schedule ->
                    val startDateTime = schedule.startDateTime!!.toDate() ?: return false
                    val endDatetime = schedule.endDateTime!!.toDate() ?: return false
                    (startDate >= startDateTime && startDate < endDatetime) || (endDate > startDateTime && endDate <= endDatetime)
                } != null) {
            updatable(false)
            cancelLine(false)
            return false
        }

        // 稼働時間が範囲外の場合
        if (receptionTimes!!.find { receptionTime ->
                    val sDate = receptionTime.StartDate.toDate(Constant.FORMAT_DATE_SLASH) ?: return false
                    val eDate = receptionTime.EndDate.toDate(Constant.FORMAT_DATE_SLASH) ?: return false
                    // 開始日 <= [選択] <= 終了日
                    if (sDate.startTime() <= startDate && endDate <= eDate.endTime()) {
                        // 設定値
                        val sTime  = receptionTime.StartTime.toDate("HH:mm") ?: return false
                        val eTime = receptionTime.EndTime.toDate("HH:mm") ?: return false
                        val sMinute = sTime.hour() * 60 + sTime.minute()
                        val eMinute = eTime.hour() * 60 + eTime.minute()
                        // 選択値
                        val startMinute = startDate.hour() * 60 + startDate.minute()
                        val endMinute = endDate.hour() * 60 + endDate.minute()
                        // 8:00 <= [選択] <= 17:00
                        ( sMinute <= startMinute && endMinute <= eMinute)
                    } else {
                        false
                    }
                } == null) {
            updatable(false)
            cancelLine(false)
            return false
        }

        updatable(true)
        cancelLine(true)

        return true
    }

    /**
     * notUpdatable 更新不可設定.
     *
     * */
    private fun updatable(bool: Boolean) {
        if (bool){
            readjustment_registration.setTextColor(ContextCompat.getColor(this, fois.dailyreportsystem.R.color.white))
            readjustment_registration.isEnabled = true

            order_readjustment.setBackgroundResource(fois.dailyreportsystem.R.drawable.radius_frame_blue)
            readjustment.isEnabled = true
        }else {
            readjustment_registration.setTextColor(ContextCompat.getColor(this, fois.dailyreportsystem.R.color.half_transparent))
            readjustment_registration.isEnabled = false
            order_readjustment.setBackgroundResource(fois.dailyreportsystem.R.drawable.radius_frame_gray)
            readjustment.isEnabled = false
        }
    }

    /**
     * 取り消し線の設定をする。
     *
     */
    private fun cancelLine(bool: Boolean) {
        if (bool) {
            research_date1.setTextColor(ContextCompat.getColor(this, R.color.black))
            val paint = research_date1.paint
            paint?.flags = research_date1.paintFlags and Paint.STRIKE_THRU_TEXT_FLAG.inv()
            paint?.isAntiAlias = true
        } else {
            research_date1.setTextColor(ContextCompat.getColor(this, R.color.red))
            val paint = research_date1.paint
            paint?.flags = research_date1.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
            paint?.isAntiAlias = true
        }

    }

    /**
     * setupItem 画面セットアップ.
     *
     * */
    @SuppressLint("SetTextI18n")
    private fun setupItem() {
        if (countHub > 0) {  return }
        hideHub()

        val researchTask = schedule!!.researchTask

        order_maker.text = researchTask!!.MakerName
        order_maker_user.text = researchTask.MakerUserName

        task_name.setText(researchTask.TaskName)

        area_name.text = researchTask.PrefName + " " + researchTask.CityName
        address.setText(researchTask.TaskAddress)

        task_name.setText(researchTask.TaskName)
        address.setText(researchTask.TaskAddress)

        research_date1.text = Date().roundTime().toString("yyyy/MM/dd(E) HH:mm")

        remarks.setText(schedule!!.remarks)

        research_date1.setOnSafeClickListener {
            DateDialogUtil.isClear(false).defaultDate(research_date1.text.toString().toDate(Constant.FORMAT_DATETIME_WEEK_SLASH)!!).showDateDialog(context!!, research_date1) }


        research_date2.setOnSafeClickListener { DateDialogUtil.defaultDate(Date().roundTime()).showDateDialog(context!!, research_date2) }
        research_date3.setOnSafeClickListener { DateDialogUtil.defaultDate(Date().roundTime()).showDateDialog(context!!, research_date3) }


        // 初期表示で値が設定されてない場合は、更新不可とする。
        validation()

        log(context?.javaClass!!.name, "setupItem")
    }

    /**
     *  openDialog ダイアログ表示処理
     * */
    private fun openDialog() {
        var alertDialog: AlertDialog? = null
        val alertBuilder = AlertDialog.Builder(this, R.style.MyDialog)
        val mAlertLayout = this.layoutInflater.inflate(R.layout.guide_popup_window, null)

        val actionTextView = mAlertLayout.findViewById<TextView>(R.id.action_text)
        val cancelTextView = mAlertLayout.findViewById<TextView>(R.id.popup_cancel)
        cancelTextView.setOnSafeClickListener {
            if (alertDialog!!.isShowing) alertDialog!!.dismiss()
        }

        actionTextView.text = this.resources.getString(R.string.order_cancel)
        actionTextView.setTextColor(ContextCompat.getColor(this, R.color.red))
        actionTextView.setOnSafeClickListener {
            deleteCalenderSchedule()
            if (alertDialog!!.isShowing) alertDialog!!.dismiss()
        }

        alertBuilder.setView(mAlertLayout)
        alertDialog = alertBuilder.create()

        alertDialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        val dialogLayoutParam = alertDialog.window!!.attributes

        dialogLayoutParam.gravity = Gravity.BOTTOM
        alertDialog.window!!.attributes = dialogLayoutParam
        alertDialog.window!!.setLayout(resources.displayMetrics.widthPixels, LinearLayout.LayoutParams.WRAP_CONTENT)

        alertDialog.show()
    }

    /* -------------------- 画面遷移 -------------------- */

    private fun forwardReadjustmentList() {
        val intent = Intent(context, ReadjustmentListActivity::class.java)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.fade_in, R.anim.slide_out_down)
    }

    // 端末の戻るボタン
    override fun onBackPressed() {
        forwardReadjustmentList()
    }

    /* -------------------- 通信処理 -------------------- */

    /**
     * getResearch 調査依頼情報取得.
     *
     * */
    private fun getResearch() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) { return }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val taskArray = result.getJSONArray("Schedules")

                        for (i in 0 until taskArray.length()) {
                            val row = taskArray.getJSONObject(i)
                            schedule = Schedule.setSchedule(row)
                        }

                        setupItem()

                        log("result", "ok")
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                } finally { }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ResearchTaskID = this.settings.getString("RESEARCH_TASK_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTask

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * researchTaskModify 調査依頼更新.
     *
     * */
    private fun researchTaskModify() {
        if(!validation()){ return }
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) { return }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {

                        forwardReadjustmentList()

                        log("result", "ok")
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    Toast.makeText(context, context!!.getString(fois.dailyreportsystem.R.string.unexpected_error), Toast.LENGTH_LONG).show()
                    log(context?.javaClass!!.name, ex.toString())
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        var researchDate1 = ""
        if (research_date1.text.toString().isNotBlank()) {
            researchDate1 = research_date1.text.toString().toDate(Constant.FORMAT_DATETIME_WEEK_SLASH)!!.toString(Constant.FORMAT_DATETIME_HYPHEN)
        }

        var researchDate2 = ""
        if (research_date2.text.toString().isNotBlank()) {
            researchDate2 = research_date2.text.toString().toDate(Constant.FORMAT_DATETIME_WEEK_SLASH)!!.toString(Constant.FORMAT_DATETIME_HYPHEN)
        }

        var researchDate3 = ""
        if (research_date3.text.toString().isNotBlank()) {
            researchDate3 = research_date3.text.toString().toDate(Constant.FORMAT_DATETIME_WEEK_SLASH)!!.toString(Constant.FORMAT_DATETIME_HYPHEN)
        }

        schedule!!.researchTask.TaskName = task_name.text.toString()
        schedule!!.researchTask.TaskAddress = address.text.toString()
        schedule!!.researchTask.PreferredDate1 = researchDate1
        schedule!!.researchTask.PreferredDate2 = researchDate2
        schedule!!.researchTask.PreferredDate3 = researchDate3

        // Schedule変更データの作成
        val scheduleModifyData = schedule!!.getBaseModifyData()
        val researchTask = schedule!!.researchTask.getBaseModifyData()

        scheduleModifyData["Remarks"] = remarks.text.toString()
        scheduleModifyData["ResearchTask"] = researchTask

        val list = ArrayList<MutableMap<String, Any?>>()
        list.add(scheduleModifyData)

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.JsonData = Gson().toJson(list)

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskModify

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * getAreaAvailability 空き状況を取得する.
     *
     * */
    private fun getAreaAvailability() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) {
                    ShowMessages().Show(context, context!!.getString(fois.dailyreportsystem.R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")

                    // 認証成功
                    if (status.getInt("StatusCode") == 0) {
                        val scheduleArrayList = result.getJSONArray("Schedules")
                        freeScheduleList = JsonHandling.toScheduleArrayList(scheduleArrayList)
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                    setupItem()
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(fois.dailyreportsystem.R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {  }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })
        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.CityCode = this.settings.getString("RESEARCH_TASK_CITY_CODE")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.AreaAvailability
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * getReceptionTime 受付可能時間を取得.
     *
     * */
    private fun getReceptionTime() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) {
                    ShowMessages().Show(context, context!!.getString(fois.dailyreportsystem.R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")

                    // 認証成功
                    if (status.getInt("StatusCode") == 0) {
                        val receptionTimeJsonArray = result.getJSONArray("ReceptionTimes")
                        receptionTimes = ReceptionTime.toReceptionTimes(receptionTimeJsonArray)
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                    setupItem()
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(fois.dailyreportsystem.R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally { }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })
        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskReceptionTime
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * deleteCalenderSchedule カレンダー情報削除.
     * 依頼状況削除
     * */
    private fun deleteCalenderSchedule() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) {
                    hideHub()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 削除成功
                        onBackPressed()
                    }
                } catch (ex: Exception) {
                    hideHub()
                    log(context?.javaClass!!.name, ex.toString())
                }
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() {hideHub()}
        })
        val params = SendParameter()
        params.UserID = context!!.settings.getString("USER_ID")
        params.ScheduleID = schedule!!.scheduleID
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.CalenderDeleteScheduleURL
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        log(context?.javaClass!!.name, url + jsonParams)
    }


}
