package fois.dailyreportsystem.activity.survey

import android.R.style.Theme_Holo_Light_Dialog_NoActionBar
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Bundle
import android.view.KeyEvent
import android.view.KeyEvent.KEYCODE_ENTER
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.R.layout.regist_image_detail_layout
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.PointOut
import fois.dailyreportsystem.data.PointOutItem
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.image_detail_layout.back_arrow
import kotlinx.android.synthetic.main.image_detail_layout.complete_text
import kotlinx.android.synthetic.main.image_detail_layout.diagnosis_comment
import kotlinx.android.synthetic.main.image_detail_layout.for_report
import kotlinx.android.synthetic.main.image_detail_layout.for_report_area
import kotlinx.android.synthetic.main.image_detail_layout.for_report_check
import kotlinx.android.synthetic.main.image_detail_layout.for_spare
import kotlinx.android.synthetic.main.image_detail_layout.for_spare_area
import kotlinx.android.synthetic.main.image_detail_layout.for_spare_check
import kotlinx.android.synthetic.main.image_detail_layout.head_complete
import kotlinx.android.synthetic.main.image_detail_layout.image_detail_root
import kotlinx.android.synthetic.main.regist_image_detail_layout.*
import org.json.JSONObject
import java.util.*


/**
 * 写真登録画面
 */
class RegistImageDetailActivity : BaseActivity() {
    //region プライベートメンバ
    private var photoType: String = ""
    private var imagePass = ""
    private var researchTaskId = ""
    private var selectedPointOutCategory: PointOut? = null
    private var selectedPointOutItem: PointOutItem? = null
    private var prevRemark = ""

    private lateinit var pointOutList: List<PointOut>
    //endregion

    //region ライフサイクル
    /**
     * 起動：onCreate→onResume
     * スリープから復帰、他画面からの復帰：onRestart→onResume
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        log(context?.javaClass!!.name, "onCreate")
        super.onCreate(savedInstanceState)
        setContentView(regist_image_detail_layout)

        val intent = this.intent
        imagePass = intent.getStringExtra(ConstantParameters.INTENT_KEY_IMAGEPASS)!!
        researchTaskId = intent.getStringExtra(ConstantParameters.INTENT_KEY_RESEARCHTASKID)!!

        back_arrow.setOnSafeClickListener { onBackPressed() }
        head_complete.setOnSafeClickListener { registImageInfo() }
        complete_text.setOnSafeClickListener { registImageInfo()  }

        // 位置カテゴリタップイベント
        out_point_category_select.setOnClickListener { showListSelectDialog(out_point_category_select) }
        out_point_category_select_icon.setOnClickListener { showListSelectDialog(out_point_category_select) }

        // 位置詳細タップイベント
        out_point_item_select.setOnClickListener { showListSelectDialog(out_point_item_select) }
        out_point_item_select_icon.setOnClickListener { showListSelectDialog(out_point_item_select) }

        for_spare_area.setOnClickListener { view -> usageSwitchChange(view) }
        for_report_area.setOnClickListener { view -> usageSwitchChange(view) }

        diagnosis_comment.setOnKeyListener(object : View.OnKeyListener {
            override fun onKey(v: View?, keyCode: Int, event: KeyEvent?): Boolean {
                if (event!!.action == KeyEvent.ACTION_DOWN && keyCode == KEYCODE_ENTER) {
                    return true
                }
                return false
            }
        })
    }

    /**
     * アクティビティ開始時イベント
     */
    override fun onStart() {
        super.onStart()

        getResearchPointOut()

        log(this.javaClass.name, "onStart")
    }

    /**
     * レジューム処理イベント
     */
    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        setupItem()

        log(context?.javaClass!!.name, "onResume")
    }

    private fun setupItem() {
        val bitmap: Bitmap = BitmapFactory.decodeFile(imagePass)
        regist_image.setImageBitmap(bitmap)

        photoType = Constant.ResearchTaskPhotoType.REPORT.SELECTED

        usageSwitchChange(for_report_area)
    }

    /**
     * スリープからの復帰、他画面からの復帰イベント
     */
    override fun onRestart() {
        super.onRestart()
        // リストビューの位置を取得する
        log(context?.javaClass!!.name, "onRestart")
    }

    /**
     * バックグラウンド化イベント
     */
    override fun onPause() {
        super.onPause()

        log(context?.javaClass!!.name, "onPause")
    }
    //endregion

    //region 画面タッチイベント
    /**
     *
     */
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(image_detail_root.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す（EditTextからフォーカスを外す）
        image_detail_root.requestFocus()

        return super.dispatchTouchEvent(ev)
    }

    private fun usageSwitchChange(view: View) {
        val paddingSize = convertDp2Px(10F,this)
        when (view.id) {
            for_spare_area.id -> {
                photoType = Constant.ResearchTaskPhotoType.SPARE.SELECTED
                for_spare_check.setPadding(0,paddingSize,0,paddingSize)
                for_spare_check.setImageResource(R.drawable.circle_blue_check)
                for_spare.setTextColor(ContextCompat.getColor(this,R.color.light_blue))
                for_report_check.setPadding(0,paddingSize,0,paddingSize)
                for_report_check.setImageResource(R.drawable.circle_non_check)
                for_report.setTextColor(ContextCompat.getColor(this,R.color.button_gray))
            }
            for_report_area.id -> {
                photoType = Constant.ResearchTaskPhotoType.REPORT.SELECTED
                for_spare_check.setPadding(0,paddingSize,0,paddingSize)
                for_spare_check.setImageResource(R.drawable.circle_non_check)
                for_spare.setTextColor(ContextCompat.getColor(this,R.color.button_gray))
                for_report_check.setPadding(0,paddingSize,0,paddingSize)
                for_report_check.setImageResource(R.drawable.circle_blue_check)
                for_report.setTextColor(ContextCompat.getColor(this,R.color.light_blue))
            }
        }
    }

    // pxをdpに変換する処理
    private fun convertDp2Px(dp: Float, context: Context): Int {
        val metrics = context.resources.displayMetrics
        return (dp * metrics.density).toInt()
    }

    // リスト選択型ダイアログを表示
    private fun showListSelectDialog(textView: TextView) {
        val listDialog = AlertDialog.Builder(context!!, Theme_Holo_Light_Dialog_NoActionBar)
        when (textView) {
            out_point_category_select -> {
                // 位置の場合
                val categoryNames = pointOutList.map { it.CategoryName }.toTypedArray()
                listDialog.setTitle(R.string.point_out_category_title)
                listDialog.setItems(categoryNames) { _, which ->
                    selectedPointOutCategory = pointOutList[which]
                    out_point_category_select.text = selectedPointOutCategory?.CategoryName
                    selectedPointOutItem = selectedPointOutCategory?.PointOutItems?.firstOrNull()
                    out_point_item_select.text = selectedPointOutItem?.ItemName
                    updateRemark(selectedPointOutItem?.Remarks?: "")
                    context?.savePointOut(selectedPointOutCategory?.PointOutCategoryID?: "", selectedPointOutItem?.PointOutItemID?: "")
                }
            }
            out_point_item_select -> {
                // 位置詳細の場合
                val items = selectedPointOutCategory?.PointOutItems?: return
                val itemNames = items.map { it.ItemName }.toTypedArray()
                listDialog.setTitle(R.string.point_out_item_title)
                listDialog.setItems(itemNames) { _, which ->
                    selectedPointOutItem = items[which]
                    out_point_item_select.text = selectedPointOutItem?.ItemName
                    updateRemark(selectedPointOutItem?.Remarks?: "")
                    context?.savePointOut(selectedPointOutCategory?.PointOutCategoryID?: "", selectedPointOutItem?.PointOutItemID?: "")
                }
            }
        }
        listDialog.create().show()
    }

    private fun updateRemark(newRemark: String) {
        var remarks = diagnosis_comment.text.toString()
        // Remove Preview Remark
        if (prevRemark.isNotEmpty() && (remarks.indexOf(prevRemark) == 0)) {
            remarks = remarks.removePrefix(prevRemark)
        }

        // Add new Remark
        remarks = newRemark.plus(remarks)

        diagnosis_comment.setText(remarks)
        prevRemark = newRemark
    }

    //endregion

    //region 画面遷移
    // 端末の戻るボタン
    override fun onBackPressed() {
        val intent = Intent(this, SurveyPropertyActivity::class.java)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
    }
    //endregion

    //region 通信処理
    private fun getResearchPointOut() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")

                    if (status.getInt("StatusCode") != 0) {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                        return
                    }

                    val pointOuts = result.getJSONArray("PointOuts")
                    val pointOutObject = pointOuts.getJSONObject(0)
                    val categories = pointOutObject.getJSONArray("PointOutCategorys")

                    // PointOutの取得
                    pointOutList = (0 until categories.length()).map{ categoryIndex ->
                        val jsonObject = categories.getJSONObject(categoryIndex)
                        val categoryItems = jsonObject.getJSONArray("PointOutItems")

                        val pointOutItems = (0 until categoryItems.length()).map{ itemIndex ->
                            val itemsJson = categoryItems.getJSONObject(itemIndex)
                            return@map PointOutItem(DisplayOrder = itemsJson.getString("DisplayOrder"),
                                    ItemName = itemsJson.getString("ItemName"),
                                    ItemShortName = itemsJson.getString("ItemShortName"),
                                    PointOutCategoryID = itemsJson.getString("PointOutCategoryID"),
                                    PointOutID = itemsJson.getString("PointOutID"),
                                    PointOutItemID = itemsJson.getString("PointOutItemID"),
                                    Remarks = itemsJson.getString("Remarks"),
                                    SelectedPhotoCount = itemsJson.getString("SelectedPhotoCount"),
                                    UnselectedPhotoCount = itemsJson.getString("UnselectedPhotoCount"))
                        }.toList()

                        return@map PointOut(CategoryName = jsonObject.getString("CategoryName"),
                                DisplayOrder = jsonObject.getString("DisplayOrder"),
                                PointOutCategoryID = jsonObject.getString("PointOutCategoryID"),
                                PointOutID = jsonObject.getString("PointOutID"),
                                PointOutItems =  pointOutItems,
                                SelectedPhotoCount = jsonObject.getString("SelectedPhotoCount"),
                                UnselectedPhotoCount = jsonObject.getString("UnselectedPhotoCount"))
                    }.toList()

                    //選択状態の復旧
                    val savedIds = context?.loadPointOut()
                    val selectedPointOutId = savedIds?.first
                    val selectedPointOutItemId = savedIds?.second
                    selectedPointOutCategory = (pointOutList.firstOrNull { it.PointOutCategoryID == selectedPointOutId }
                            ?: pointOutList.firstOrNull())
                    selectedPointOutItem = (selectedPointOutCategory?.PointOutItems?.firstOrNull { it.PointOutItemID == selectedPointOutItemId }
                            ?: selectedPointOutCategory?.PointOutItems?.firstOrNull())
                    out_point_category_select.text = selectedPointOutCategory?.CategoryName
                    out_point_item_select.text = selectedPointOutItem?.ItemName
                    updateRemark(selectedPointOutItem?.Remarks?: "")
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, "$ex")
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
                hideHub()
            }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.ResearchTaskID = researchTaskId

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskPointOutItems

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * registImageInfo 写真情報登録処理
     *  報告用、予備用の更新
     *  備考欄の更新
     * */
    private fun registImageInfo() {
        val rotation: String = this.getImageRotation(imagePass)
        val params: Dictionary<String, String> = Hashtable<String, String>()
        params.put("UserID", this.settings.getString("USER_ID"))
        params.put("ResearchTaskID", researchTaskId)
        params.put("PhotoDate", DateUtil.getDateFormat("yyyy-MM-dd HH:mm"))
        params.put("PointOutID", selectedPointOutCategory!!.PointOutID)
        params.put("PointOutItemID", selectedPointOutItem!!.PointOutItemID)
        params.put("PointOutCateogryID", selectedPointOutCategory!!.PointOutCategoryID)
        params.put("Rotation", rotation)
        params.put("Selected", photoType)
        params.put("Remarks", diagnosis_comment.text.toString())

        // URL作成
        val url: String = Constant.URL + Constant.ResearchPhotoAdd
        val asyncImageUploader = AsyncImageUploader(this, object : AsyncImageUploader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // ローディングを開始

                        val photoData = result.getJSONArray("ResearchPhotoAttributes")
                    } else {
                        // 失敗時
                        log(context?.javaClass!!.name, status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, "$ex")
                } finally {
                    hideHub()

                    onBackPressed()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        }, params)

        // 非同期通信開始
        // 第1引数：URL
        // 第2引数：imageFilePath
        // 第3引数：rotation
        asyncImageUploader.execute(url, imagePass, rotation)
    }
   //endregion
}
