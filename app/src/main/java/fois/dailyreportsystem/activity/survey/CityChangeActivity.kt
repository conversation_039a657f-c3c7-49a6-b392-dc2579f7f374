package fois.dailyreportsystem.activity.survey

import android.os.Bundle
import com.google.firebase.crashlytics.FirebaseCrashlytics
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.util.loginCheck

class CityChangeActivity : BaseActivity() {


    /* -------------------- ライフサイクル -------------------- */
    /*
    起動：onCreate→onResume
    スリープから復帰、他画面からの復帰：onRestart→onResume
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.main_layout)

        log(context?.javaClass!!.name, "onCreate")
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        log(context?.javaClass!!.name, "onResume")
    }

    // スリープからの復帰、他画面からの復帰
    override fun onRestart() {
        super.onRestart()
        // リストビューの位置を取得する
        log(context?.javaClass!!.name, "onRestart")
    }

    override fun onPause() {
        super.onPause()

        log(context?.javaClass!!.name, "onPause")
    }

    // 端末の戻るボタン
    override fun onBackPressed() {
        finish()
        overridePendingTransition(0, 0)
    }

}
