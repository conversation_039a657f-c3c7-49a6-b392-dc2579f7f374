package fois.dailyreportsystem.activity.login

// 作成日：2017/08/18
// 更新日：2018/10/18

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.content.ContextCompat
import android.view.MotionEvent
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import android.widget.Toast
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import fois.dailyreportsystem.BuildConfig

import fois.dailyreportsystem.R
import fois.dailyreportsystem.activity.*
import fois.dailyreportsystem.activity.message.MessageGroupListActivity
import fois.dailyreportsystem.activity.message.MessageListActivity
import fois.dailyreportsystem.activity.report.*
import fois.dailyreportsystem.activity.request.OrderDetailActivity
import fois.dailyreportsystem.activity.setting.CompanyCodeDeleteActivity
import fois.dailyreportsystem.activity.setting.CompanyCodeRegisterActivity
import fois.dailyreportsystem.activity.setting.CompanyInfoActivity
import fois.dailyreportsystem.activity.setting.SettingActivity
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.Login
import fois.dailyreportsystem.data.adapter.LoginAdapter
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.login_layout.*
import org.json.JSONObject

// ログイン画面のアクティビティ
class LoginActivity : BaseActivity() {

    // 変数宣言
    private var listLogin: ArrayList<Login> = ArrayList()       // リストビュー用配列
    private var list: ArrayList<Login> = ArrayList()                    // 保存されている会社情報用配列
    private var allClear: Boolean = false                       // 全てのアクティビティを破棄する
    private var data : Map<String, String>? = null

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.login_layout)

        // クリックイベント
        // ログイン（タイトルバー）タップ
        login.setOnSafeClickListener {
            login.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
            InputCheck()
        }
        // ログインボタンタップ
        login_button.setOnSafeClickListener {
            login_button.setBackgroundResource(R.drawable.login_button_on)
            InputCheck()
        }

        this.settings.put("LOGIN_ACTIVITY", true)


        if(intent.getStringExtra("NotificationCode") != null){
            data = mapOf(
                "NotificationCode" to (intent.extras!!.getString("NotificationCode") ?: ""),
                "UserID" to (intent.extras!!.getString("UserID") ?: ""),
                "Message" to (intent.extras!!.getString("Message") ?: ""),
                "SendTime" to (intent.extras!!.getString("SendTime") ?: ""),
                "MessageGroupID" to (intent.extras!!.getString("MessageGroupID") ?: ""),
                "MessageGroupName" to (intent.extras!!.getString("MessageGroupName") ?: ""),
                "UnreadCount" to (intent.extras!!.getString("UnreadCount") ?: ""),
                "UnreadTotal" to (intent.extras!!.getString("UnreadTotal") ?: ""),
                "TaskID" to (intent.extras!!.getString("TaskID") ?: ""),
                "MessageID" to (intent.extras!!.getString("MessageID") ?: ""),
                "ScheduleID" to (intent.extras!!.getString("ScheduleID") ?: "")
            )
        }

    }

    override fun onResume() {
        super.onResume()

        // バージョン情報を記述
        val version: TextView? = findViewById(R.id.background) as TextView
        version!!.text = context!!.getString(R.string.version) + BuildConfig.VERSION_NAME

        list.clear()
        listLogin.clear()

        // 設定ファイルから登録されている会社コードと会社名を読み込み
        val getList = this.settings.getString("COMPANY_LIST")
        if(getList != "") {
            val type = object : TypeToken<java.util.ArrayList<Login>>() {}.type
            list = Gson().fromJson(getList, type)

            // 読み込んだデータをリストに入れる
            var login: Login

            // サブタイトル：会社情報
            login = Login()
            login.SubTitle = context!!.getString(R.string.company_info)
            login.type = 0
            listLogin.add(login)

            // 会社リスト
            for (i in 0..list.count() - 1) {
                login = Login()
                login.CompanyNumber = list[i].CompanyNumber
                login.CompanyName = list[i].CompanyName
                if (this.settings.getString("COMPANY_NUMBER") == list[i].CompanyNumber) {
                    // 設定ファイルの会社コード（ログインで使用している会社コード）とリストの会社コードが一致した場合
                    login.checked = true
                }
                login.type = 1
                // リストに追加
                listLogin.add(login)
            }

            // サブタイトル：ユーザー情報
            login = Login()
            login.SubTitle = context!!.getString(R.string.user_info)
            login.type = 0
            listLogin.add(login)

            // 電話番号入力欄
            login = Login()
            login.type = 3
            listLogin.add(login)

            // パスワード入力
            login = Login()
            login.type = 4
            listLogin.add(login)

            // ログイン中の会社コードを入力済みとしてセット
            this.settings.put("COMPANY_NUMBER_INPUT", this.settings.getString("COMPANY_NUMBER"))
        }

        // リストビューにセットする
        ListSet()
    }

    override fun onRestart() {
        super.onRestart()

        // スリープから戻った時リストを空にする
        listLogin.clear()
    }

    /* -------------------- 画面遷移 -------------------- */
    // 画面遷移分岐
    private fun intentDivide() {
        var intent: Intent? = null
        if(data != null){
            var intents :Array<Intent> = arrayOf(Intent(), Intent())
            if(this.settings.getString("USER_ID") == data!!["UserID"]) {
                when (data!!["NotificationCode"]) {
                    // 新着メッセージ
                    "0101" -> {
                        intents = arrayOf(Intent(), Intent())
                        val intent = Intent(this, MainActivity::class.java)
                        intent.putExtra("RE_SETUP_FRAGMENT", Constant.FRAGMENT_SCREEN_MAIN)
                        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        intents[0] = intent
                        intents[1] = Intent(applicationContext, MessageGroupListActivity::class.java)
                    }
                    // 調査依頼状況
                    "0201", "0202", "0203", "0204", "0205" -> {
                        intents = arrayOf(Intent(), Intent())
                        val intent = Intent(this, MainActivity::class.java)
                        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        intent.putExtra("RE_SETUP_FRAGMENT", Constant.FRAGMENT_SCREEN_REQUEST_LIST)
                        intents[0] = intent
                        var intent1 = Intent(applicationContext, OrderDetailActivity::class.java)
                        intent1.putExtra("SCHEDULE_ID", data!!["ScheduleID"])
                        intents[1] = intent1
                    }
                }
                startActivities(intents)
            }else{
                intent = Intent(this, MainActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
                startActivity(intent)
            }
        }else {
            // TODO(ここも修正対象かな)
            when (this.settings.getString("ACTIVITY", "MainActivity")) {
                context!!.getString(R.string.main_activity) -> {
                }
                context!!.getString(R.string.setting_activity) -> intent = Intent(this, SettingActivity::class.java)
                context!!.getString(R.string.company_code_register_activity) -> intent = Intent(this, CompanyCodeRegisterActivity::class.java)
                context!!.getString(R.string.company_code_delete_activity) -> intent = Intent(this, CompanyCodeDeleteActivity::class.java)
                context!!.getString(R.string.company_info_activity) -> intent = Intent(this, CompanyInfoActivity::class.java)
                context!!.getString(R.string.message_group_list_activity) -> intent = Intent(this, MessageGroupListActivity::class.java)
                context!!.getString(R.string.message_list_activity) -> intent = Intent(this, MessageListActivity::class.java)

                // 予定登録と実績登録は日報一覧へ
                context!!.getString(R.string.result_activity1),
                context!!.getString(R.string.result_activity2),
                context!!.getString(R.string.result_activity3),
                context!!.getString(R.string.schedule_activity1),
                context!!.getString(R.string.schedule_activity2),
                context!!.getString(R.string.schedule_activity3),
                context!!.getString(R.string.report_list_activity) -> intent = Intent(this, ReportListActivity::class.java)

                context!!.getString(R.string.photo_process_list_activity) -> intent = Intent(this, PhotoProcessListActivity::class.java)
                context!!.getString(R.string.photo_shoot_list_activity) -> intent = Intent(this, PhotoShootListActivity::class.java)
                context!!.getString(R.string.photo_thumbnail_list_activity) -> intent = Intent(this, PhotoThumbnailListActivity::class.java)

                // 写真選択は写真詳細へ
                context!!.getString(R.string.photo_gallery_activity),
                context!!.getString(R.string.photo_detail_activity) -> intent = Intent(this, PhotoDetailActivity::class.java)

                //それ以外の知らない画面はとりあえずメイン画面へ戻る
                else -> intent = Intent(this, MainActivity::class.java)
            }
            // 邸一覧以外は新しく画面を作る（邸一覧はfinishのみ）
            if (this.settings.getString("ACTIVITY", "MainActivity") != "MainActivity") {
                startActivity(intent)
            } else if (allClear) {
                intent = Intent(this, MainActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
                startActivity(intent)
            }
        }
        this.settings.put("LOGIN_ACTIVITY", false)
        finish()
        overridePendingTransition(R.anim.fade_in, R.anim.slide_out_down)
    }

    /* -------------------- リストビューイベント -------------------- */
    // リストビューセット
    private fun ListSet() {
        // 会社情報をセットする
        val loginAdapter = LoginAdapter(this, listLogin)
        login_listview.adapter = loginAdapter
    }

    /* -------------------- タップイベント -------------------- */
    // 完了ボタンタップ
    private fun InputCheck() {
        // 入力チェック
        when {
        // 会社未選択
            this.settings.getString("COMPANY_NUMBER_SELECT" ) == "" -> {
                ShowMessages().Show(context, context!!.getString(R.string.company_not_select))
                ResetButton()
            }
        // 電話番号未入力
            this.settings.getString("PHONE_NUMBER_INPUT" ) == "" -> {
                ShowMessages().Show(context, context!!.getString(R.string.phone_number_nothing))
                ResetButton()
            }
        // 電話番号が10,11桁ではない
            this.settings.getString("PHONE_NUMBER_INPUT" ).length < 10 -> {
                ShowMessages().Show(context, context!!.getString(R.string.phone_number_mismatch))
                ResetButton()
            }
        // パスワード未入力
            this.settings.getString("PASSWORD_INPUT" ) == "" -> {
                ShowMessages().Show(context, context!!.getString(R.string.password_nothing))
                ResetButton()
            }
        // 入力に問題なし
            else -> {
                // ログイン認証
                Authenticate()
            }
        }
    }

    // ボタンの色を元に戻す
    private fun ResetButton() {
        login.setTextColor(ContextCompat.getColor(this, R.color.white))
        login_button.setBackgroundResource(R.drawable.login_button)
    }

    // 画面タッチイベント
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(main_layout.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す（EditTextからフォーカスを外す）
        main_layout.requestFocus()

        return super.dispatchTouchEvent(ev)
    }

    // 端末のバックボタン制御
    override fun onBackPressed() {
        // バックボタンを押しても何も行わない
    }

    /* -------------------- 通信処理 -------------------- */
    private fun Authenticate() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {
                showHub()
            }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    Toast.makeText(context, context!!.getString(R.string.connect_error), Toast.LENGTH_LONG).show()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功時
                        // ログインユーザ情報の取得
                        val data: JSONObject = result.getJSONObject("Authenticate")
                        val user_data: JSONObject = data.getJSONObject("User")
                        // 戻り先判別
                        if(context!!.settings.getString("COMPANY_NUMBER") != context!!.settings.getString("COMPANY_NUMBER_SELECT")) {
                            // ログイン中の会社コードと入力した会社コードが違う場合、邸一覧へ戻す為設定ファイルに保存
                            context!!.settings.put("ACTIVITY", "MainActivity")
                            // リストの位置を全てリセット
                            context!!.position.clear()
                            allClear = true
                        }
                        // 各種データを設定ファイルに保存
                        if (!context!!.isSameAccount(user_data)) {
                            context!!.removePointOut()
                        }
                        context!!.saveAccount(user_data)
                        // 遷移先判別
                        intentDivide()
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                        ResetButton()
                    }
                } catch (ex: Exception) {
                    Toast.makeText(context, context!!.getString(R.string.unexpected_error), Toast.LENGTH_LONG).show()
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.CompanyNumber = this.settings.getString("COMPANY_NUMBER_SELECT" )
        params.PhoneNumber = this.settings.getString("PHONE_NUMBER_INPUT" )
        params.Password = Constant.GetAES(this.settings.getString("PASSWORD_INPUT" ), Constant.ENCODE_KEY, Constant.ENCODE_IV)

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.AuthenticateURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}
