package fois.dailyreportsystem.activity.fileviewer

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.view.MotionEvent
import android.view.View
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import com.github.barteksc.pdfviewer.listener.OnTapListener
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.util.Constant
import fois.dailyreportsystem.util.ConstantParameters
import fois.dailyreportsystem.util.ShowMessages
import fois.dailyreportsystem.util.either
import fois.dailyreportsystem.util.FileUtil
import kotlinx.android.synthetic.main.file_viewer_layout.*
import java.io.File

/**
 * ファイル表示タイプ
 */
class FileViewTypes {
    companion object {
        /**
         * ファイル表示
         */
        const val File = 0

        /**
         * 工程表表示
         */
        const val WorkSchedule = 1
    }
}

/**
 * ファイルのプレビュー用画面
 */
class FileViewerActivity : BaseActivity(), OnTapListener {
    private var fullscreen = false

    private var fileName = ""
    private var fileExtension = ""
    private var sourcePath = ""

    /* -------------------- ライフサイクル -------------------- */
    /**
     * 生成時アクション
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.file_viewer_layout)

        fileName = intent.getStringExtra(ConstantParameters.FILE_NAME) ?: return
        fileExtension = intent.getStringExtra(ConstantParameters.FILE_EXTENSION) ?: return
        val type = intent.getIntExtra(ConstantParameters.VIEW_TYPE, 0)
        val uri = intent.data ?: return
        sourcePath = uri.path ?: return

        toolbar_title.text = fileName

        fileview_view.settings.builtInZoomControls = true
        fileview_view.settings.allowFileAccess = true
        fileview_view.settings.javaScriptEnabled = true

        val data = (type == FileViewTypes.File).either(makeCenteringHtml(), makeRightAlignHtml())

        fileview_view.loadDataWithBaseURL("file:///android_asset/", data, "text/html", "utf-8", null)

        // クリックイベント
        // 戻るボタンタップ
        close_button.setOnClickListener {
            backMain()
        }

        // アクションボタンタップ
        download.setOnClickListener {
            if (!(Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU &&
                        ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED)) {
                // ストレージの権限がある場合はコピー
                copyFile()
            } else {
                // ストレージの権限が無い場合は権限を求める
                ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE), Constant.PERMISSION_REQUEST_STORAGE)
            }
        }
    }

    private fun makeRightAlignHtml(): String {
        return "<html lang=\"ja\" style=\"margin: 0; padding: 0; width: 100%; \">\n" +
                "  <body style=\" text-align: center; background-color: #f2f1f9; \">\n" +
                "    <img style=\"max-width: 100%; max-height: 100%; \" src=\"$sourcePath\" />\n" +
                "  </body>\n" +
                "</html>"
    }

    private fun makeCenteringHtml(): String {
        return "<html lang=\"ja\" style=\"margin: 0; padding: 0; width: 100%; height: 100%; \">\n" +
                "  <body style=\" align-items: center; justify-content: center; vertical-align: middle; background-color: #f2f1f9; \">\n" +
                "    <img style=\"max-width: 100%; max-height: 100%; \" src=\"$sourcePath\" />\n" +
                "  </body>\n" +
                "</html>"
    }

    /**
     * フォーカス変更時イベント
     */
    @RequiresApi(Build.VERSION_CODES.KITKAT)
    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        hideSystemUI()
    }

    @RequiresApi(Build.VERSION_CODES.KITKAT)
    private fun hideSystemUI() {
        val decorView = window.decorView
        decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                        or View.SYSTEM_UI_FLAG_FULLSCREEN
                        or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                        or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
    }

    // 邸一覧画面へ戻る
    private fun backMain() {
        if (clickPosition == null) {
            clickPosition = true
            finish()
            overridePendingTransition(R.anim.activity_close_enter, R.anim.activity_close_exit)
        }
    }

    /**
     * 端末の戻るボタン
     */
    override fun onBackPressed() {
        backMain()
    }


    /**
     * タップイベント
     */
    override fun onTap(e: MotionEvent?): Boolean {
        return fullscreen()
    }

    // フルスクリーン化
    private fun fullscreen(): Boolean {
        fullscreen = !fullscreen
        LayoutHeader.setExpanded(fullscreen)
        return true
    }

    private fun copyFile() {
        val source = File(sourcePath)
        val downloadDir = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path)
        val dest = File(FileUtil.getUniqueFilePath(downloadDir, fileName, fileExtension))
        source.copyTo(dest, overwrite = true)
        ShowMessages().Show(context, "ファイルをダウンロードしました。\n${dest.absolutePath}")
    }
}