package fois.dailyreportsystem.activity.fileviewer

// kotlin化：2018/04/11
// 更新日：2018/04/11

import android.Manifest
import android.annotation.SuppressLint
import android.annotation.TargetApi
import android.app.AlertDialog
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.text.InputType
import android.view.KeyEvent.Callback
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.EditText
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import com.github.barteksc.pdfviewer.listener.*
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.util.Constant
import fois.dailyreportsystem.util.ConstantParameters
import fois.dailyreportsystem.util.ShowMessages
import fois.dailyreportsystem.util.FileUtil
import kotlinx.android.synthetic.main.pdfview_layout.*
import java.io.File


// PDF画面のアクティビティ
class PDFViewActivity : BaseActivity(), Callback, OnErrorListener, OnPageChangeListener, OnPageScrollListener, OnTapListener, OnRenderListener {

    private var fullscreen = false     // PDFのフルスクリーン化フラグ
    private var handle = Handler()
    private lateinit var runnable: Runnable
    private var fileName = ""
    private var fileExtension = ""
    private var sourcePath = ""

    private var password: String? = null

    /* -------------------- ライフサイクル -------------------- */

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.pdfview_layout)
        fileName = intent.getStringExtra(ConstantParameters.FILE_NAME) ?: return
        fileExtension = intent.getStringExtra(ConstantParameters.FILE_EXTENSION) ?: return
        val uri = intent.data ?: return
        sourcePath = uri.path ?: return

        password = null

        // 閉じるボタン
        pdfViewClose!!.setOnClickListener { close() }

        // アクションボタンタップ
        pdfViewDownload.setOnClickListener {
            if (!(Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU &&
                        ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED)) {
                // ストレージの権限がある場合はコピー
                copyFile()
            } else {
                // ストレージの権限が無い場合は権限を求める
                ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE), Constant.PERMISSION_REQUEST_STORAGE)
            }
        }

        // PDF初回読み込み
        firstSet()

        // スワイプ判別用
        //detector = SimpleGestureFilter(this, this)

        appbar_layout.isLiftOnScroll
    }

    @RequiresApi(Build.VERSION_CODES.KITKAT)
    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        hideSystemUI()
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    override fun onDestroy() {
        super.onDestroy()
    }

    // 端末のバックボタン
    override fun onBackPressed() = close()


    /* -------------------- 処理、描画など -------------------- */
    // 閉じる（Activity終了）
    private fun close() {
        finish()
        overridePendingTransition(R.anim.activity_close_enter, R.anim.activity_close_exit)
    }

    @RequiresApi(Build.VERSION_CODES.KITKAT)
    private fun hideSystemUI() {
        val decorView = window.decorView
        decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                        or View.SYSTEM_UI_FLAG_FULLSCREEN
                        or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                        or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
    }

    private fun copyFile() {
        val source = File(sourcePath)
        val downloadDir = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path)
        val dest = File(FileUtil.getUniqueFilePath(downloadDir, fileName, fileExtension))
        source.copyTo(dest, overwrite = true)
        ShowMessages().Show(context, "ファイルをダウンロードしました。\n${dest.absolutePath}")
    }

    // PDF初回読み込み
    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    private fun firstSet() = try {
        // タイトルを表示
        pdfViewTitleText!!.text = fileName

        // PDFを表示
        pdfViewFile.recycle()
        pdfViewFile.maxZoom = 20.0F
        pdfViewFile.midZoom = 10.0F
        pdfViewFile.fromFile(File(sourcePath))
                .enableSwipe(true) //スワイプ
                .swipeHorizontal(false) //true:横スクロール, false:縦スクロール
                .enableDoubletap(true) //ダブルタップでの拡大
                .defaultPage(0) //初期表示ページ番号
                .onPageChange(this)
                .onPageScroll(this)
                .onTap(this)
                .onError(this)
                .onRender(this)
                .enableAnnotationRendering(false)
                .enableAntialiasing(true)
                .password(password)
                .scrollHandle(null)
                .spacing(5)
                .fitEachPage(true)
                .load()

    } catch (e: SecurityException) {
        e.printStackTrace()

    } catch (e: Exception) {
        e.printStackTrace()
    }


    override fun onError(t: Throwable?) {
        val e = t as? com.shockwave.pdfium.PdfPasswordException ?: return
        val editView = EditText(this)

        // テキスト入力を受け付けるビューを作成します。
        val alertDialog = AlertDialog.Builder(this)
                .setTitle(this.getString(R.string.PDFPasswordFailed)).setView(editView)
                .setNegativeButton(this.getString(R.string.Cancel)) { _, _ -> finish() }
                .setPositiveButton(this.getString(R.string.OK)) { _, _ ->
                    password = editView.text.toString()
                    firstSet()
                }.create()
        editView.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
        editView.onFocusChangeListener = View.OnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                alertDialog.window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
            }
        }
        alertDialog.show()
    }

    override fun onInitiallyRendered(nbPages: Int) {
        firstFadeout()
    }

    override fun onTap(e: MotionEvent?): Boolean {
        return fullscreen()
    }

    @SuppressLint("SetTextI18n")
    override fun onPageChanged(page: Int, pageCount: Int) {
        val viewPage = page + 1
        page_view.text = "$viewPage/$pageCount"
    }

    override fun onPageScrolled(page: Int, positionOffset: Float) {
        fadeout()

    }

    // フルスクリーン化
    private fun fullscreen(): Boolean {
        fullscreen = !fullscreen
        appbar_layout.setExpanded(fullscreen)
        return true
    }


    private fun firstFadeout() {

        runnable = Runnable {
            try {
                val animation = AnimationUtils.loadAnimation(this, R.anim.fadeout_page)
                animation.fillAfter = false

                // アニメーション終了時にサムネイルのパーツを初期化する。
                animation.setAnimationListener(object : Animation.AnimationListener {
                    override fun onAnimationStart(var1: Animation) {}
                    override fun onAnimationRepeat(var1: Animation) {}
                    override fun onAnimationEnd(var1: Animation) {
                        page_view.visibility = View.GONE
                    }
                })

                page_view.startAnimation(animation)
            } catch (e: Exception) {
            }
        }
        handle.postDelayed(runnable, 4000)
    }

    // サムネイルフェードアウト
    private fun fadeout() {
        if(this::runnable.isInitialized) {
            handle.removeCallbacks(runnable)
            page_view.visibility = View.VISIBLE
            handle.postDelayed(runnable, 4000)
        }
    }
}