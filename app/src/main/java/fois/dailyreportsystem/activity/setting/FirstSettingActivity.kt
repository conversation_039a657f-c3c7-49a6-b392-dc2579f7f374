package fois.dailyreportsystem.activity.setting

// 作成日：2017/08/18
// 更新日：2018/03/09

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import android.text.InputFilter
import android.view.MotionEvent
import android.view.inputmethod.InputMethodManager
import android.widget.*
import com.google.gson.Gson
import kotlinx.android.synthetic.main.first_setting_layout.*
import fois.dailyreportsystem.BuildConfig

import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.CompanyList
import fois.dailyreportsystem.data.Worker
import fois.dailyreportsystem.util.*
import org.json.JSONObject

// 初期設定画面のアクティビティ
class FirstSettingActivity : BaseActivity() {

    // 変数宣言
    private var CompanyNumber: String? = null             // 会社コード
    private var PhoneNumber: String? = null             // 電話番号
    private var Password: String? = null                // パスワード
    // 入力制御フィルター（英数字以外入力不可にする）
    private val filters = arrayOf<InputFilter>(PasswordFilter())

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.first_setting_layout)

        this.settings.put("FIRST_SETTING", false)

        // EditText制御
        password_input.filters = filters

        // クリックイベント
        // 登録ボタンタップ
        register.setOnSafeClickListener {
            register.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
            InputCheck()
        }

        first_register.setOnSafeClickListener{
            first_register_button.setImageResource(R.drawable.com_regist_on)
            InputCheck()
        }

        // 写真保存フィールドタップ
        save_photo.setOnSafeClickListener {
            // チェックボックス切替
            save_photo_checkbox.isChecked = !save_photo_checkbox.isChecked
        }

        checkPermissions()
    }

    override fun onResume() {
        super.onResume()

        // バージョン情報を記述
        val version: TextView? = findViewById(R.id.background) as TextView
        version!!.text = context!!.getString(R.string.version) + BuildConfig.VERSION_NAME
    }

    /* -------------------- タップイベント -------------------- */
    // 完了ボタンタップ時
    private fun InputCheck() {
        // 入力チェック
        when {
            // 会社コード未入力
            company_number_input.text.isEmpty() -> {
                ShowMessages().Show(context, context!!.getString(R.string.company_number_nothing))
                ColorClear()
            }
            // 会社コードが6桁ではない
            company_number_input.text.length != 6 -> {
                ShowMessages().Show(context, context!!.getString(R.string.company_number_mismatch))
                ColorClear()
            }
            // 電話番号未入力
            phone_number_input.text.isEmpty() -> {
                ShowMessages().Show(context, context!!.getString(R.string.phone_number_nothing))
                ColorClear()
            }
            // 電話番号が10桁未満
            phone_number_input.text.length < 10 -> {
                ShowMessages().Show(context, context!!.getString(R.string.phone_number_mismatch))
                ColorClear()
            }
            // パスワード未入力
            password_input.text.isEmpty() -> {
                ShowMessages().Show(context, context!!.getString(R.string.password_nothing))
                ColorClear()
            }
            // 入力に問題なし
            else -> {
                // 会社コード、電話番号、パスワードを変数に代入
                CompanyNumber = company_number_input.text.toString()
                PhoneNumber = phone_number_input.text.toString()
                Password = password_input.text.toString()
                // パスワードの暗号化
                Password = Constant.GetAES(Password!!, Constant.ENCODE_KEY, Constant.ENCODE_IV)
                // ログイン認証処理
                LoginAsync()
            }
        }
    }

    // ツールバーとボタンの色を元に戻す
    private fun ColorClear() {
        register.setTextColor(ContextCompat.getColor(this, R.color.white))
        first_register_button.setImageResource(R.drawable.com_regist)
    }

    // 画面タッチイベント
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(main_layout.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す（EditTextからフォーカスを外す）
        main_layout.requestFocus()

        return super.dispatchTouchEvent(ev)
    }

    // 端末のバックボタン制御
    override fun onBackPressed() {
        // バックボタンを押しても何も行わない
    }

    /* -------------------- 通信処理 -------------------- */
    private fun LoginAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功時
                        // 初回起動フラグをfalseに変更
                        context!!.settings.put("FIRST_LAUNCH", false)
                        // ログインユーザ情報の取得
                        val data: JSONObject = result.getJSONObject("Authenticate")
                        val user_data: JSONObject = data.getJSONObject("User")
                        // 電話番号とパスワードを保存
                        context!!.settings.put("PHONE_NUMBER_INPUT", PhoneNumber)
                        context!!.settings.put("PASSWORD_INPUT_AES", Password)
                        // 各種データを設定ファイルに保存
                        context!!.saveAccount(user_data)

                        // 写真保存のON/OFF切替
                        context!!.settings.put("PHOTO_SAVE", save_photo_checkbox!!.isChecked)

                        // 会社コード、会社名をリストに格納
                        val companyList = CompanyList()
                        companyList.CompanyNumber = user_data.getString("CompanyNumber")
                        companyList.CompanyName = user_data.getString("CompanyName")
                        val list: ArrayList<CompanyList> = ArrayList()
                        list.add(companyList)
                        // ユーザリストの登録
                        val userData = Worker()
                        userData.CompanyID = user_data.getString("CompanyID")
                        userData.UserID = user_data.getString("UserID")
                        userData.UserName = user_data.getString("UserName")
                        val userList: ArrayList<Worker> = ArrayList()
                        userList.add(userData)
                        // 設定ファイルに登録した会社情報、会社コード、会社名を保存
                        context!!.settings.put("COMPANY_LIST", Gson().toJson(list))
                        // 設定ファイルにユーザリストの登録
                        context!!.settings.put("USER_LIST", Gson().toJson(userList))

                        // 邸一覧へ遷移
                        finish()
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                    ColorClear()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.CompanyNumber = CompanyNumber
        params.PhoneNumber = PhoneNumber
        params.Password = Password

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.AuthenticateURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}
