package fois.dailyreportsystem.activity.setting

// 作成日：2017/08/18
// 更新日：2018/02/13

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MotionEvent
import android.view.inputmethod.InputMethodManager
import android.widget.*
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.CompanyInfo
import fois.dailyreportsystem.data.adapter.CompanyInfoAdapter
import fois.dailyreportsystem.data.CompanyList
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.company_info_layout.*
import org.json.JSONObject

// 会社情報画面のアクティビティ
class CompanyInfoActivity : BaseActivity() {

    // 変数宣言
    private var listCompanyInfo: ArrayList<CompanyInfo> = ArrayList()       // リストビュー用配列
    private var list: ArrayList<CompanyList> = ArrayList()                    // 保存されている会社情報用配列

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.company_info_layout)

        // クリックイベント
        back_arrow.setOnSafeClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            Back()
        }

        account_change.setOnSafeClickListener{
            account_change_button.setImageResource(R.drawable.login_on)
            Complete()
        }

        // 入力情報のクリア
        this.settings.put("PHONE_NUMBER_INPUT", "")
        this.settings.put("PASSWORD_INPUT", "")
    }

    override fun onResume() {
        super.onResume()

        list.clear()
        listCompanyInfo.clear()

        // 設定ファイルから登録されている会社コードと会社名を読み込み
        val getList = this.settings.getString("COMPANY_LIST")
        val type = object : TypeToken<java.util.ArrayList<CompanyList>>() {}.type
        list = Gson().fromJson(getList, type)

        // 読み込んだデータをリストに入れる
        var companyInfo: CompanyInfo

        // サブタイトル：会社情報
        companyInfo = CompanyInfo()
        companyInfo.SubTitle = context!!.getString(R.string.company_info)
        companyInfo.type = 0
        listCompanyInfo.add(companyInfo)

        // 会社リスト
        for (i in 0..list.count()-1) {
            companyInfo = CompanyInfo()
            companyInfo.CompanyNumber = list[i].CompanyNumber
            companyInfo.CompanyName = list[i].CompanyName
            if (this.settings.getString("COMPANY_NUMBER") == list[i].CompanyNumber) {
                // 設定ファイルの会社コード（ログインで使用している会社コード）とリストの会社コードが一致した場合
                companyInfo.checked = true
            }
            companyInfo.type = 1
            // リストに追加
            listCompanyInfo.add(companyInfo)
        }

        // 会社コード新規登録用
        companyInfo = CompanyInfo()
        companyInfo.type = 2
        listCompanyInfo.add(companyInfo)

        // サブタイトル：ユーザー情報
        companyInfo = CompanyInfo()
        companyInfo.SubTitle = context!!.getString(R.string.user_info)
        companyInfo.type = 0
        listCompanyInfo.add(companyInfo)

        // 電話番号入力欄
        companyInfo = CompanyInfo()
        companyInfo.type = 3
        listCompanyInfo.add(companyInfo)

        // パスワード入力
        companyInfo = CompanyInfo()
        companyInfo.type = 4
        listCompanyInfo.add(companyInfo)

        // リストビューにセットする
        ListSet()
    }

    override fun onRestart() {
        super.onRestart()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        // スリープから戻った時リストを空にする
        listCompanyInfo.clear()
    }

    /* -------------------- 画面遷移 -------------------- */

    // 前の画面へ戻る
    private fun Back() {
        if(clickPosition == null) {
            clickPosition = true
        val intent: Intent = Intent(this, SettingActivity::class.java)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
    }
    }

    // 邸一覧へ
    private fun BackMain() {
        if(clickPosition == null) {
            clickPosition = true
        finish()
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
    }
    }

    /* -------------------- リストビューイベント -------------------- */
    // リストビューセット
    private fun ListSet() {
        // 会社情報をアダプターを使いセットする
        val companyInfoAdapter = CompanyInfoAdapter(this, listCompanyInfo)
        company_info_listview.adapter = companyInfoAdapter
        // リストビューのフィールドタップ時のイベント
        company_info_listview.onItemClickListener = ClickItem
    }

    // リストビューのフィールドタップ時
    private val ClickItem = AdapterView.OnItemClickListener { _, _, position, _ ->
        val companyInfo = this@CompanyInfoActivity.company_info_listview.getItemAtPosition(position) as CompanyInfo
        when (companyInfo.type) {
            1 -> {
                // 既存会社コード、会社名フィールドタップ
                if (companyInfo.CompanyNumber != this.settings.getString("COMPANY_NUMBER", "")) {
                    // 会社コード、会社名を保存
                    this.settings.put("DELETE_COMPANY_NUMBER", companyInfo.CompanyNumber!!)
                    this.settings.put("DELETE_COMPANY_NAME", companyInfo.CompanyName!!)
                    val intent = Intent(application!!, CompanyCodeDeleteActivity::class.java)
                    startActivity(intent)
                    finish()
                    overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
                }
            }
            2 -> {
                // 新しい会社コード登録フィールドタップ
                val intent = Intent(application!!, CompanyCodeRegisterActivity::class.java)
                startActivity(intent)
                finish()
                overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
            }
        }
    }

    /* -------------------- タップイベント -------------------- */
    // 端末のバックボタン制御
    override fun onBackPressed() {
        Back()
    }

    // 画面タッチイベント
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(main_layout.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す（EditTextからフォーカスを外す）
        main_layout.requestFocus()

        return super.dispatchTouchEvent(ev)
    }

    // 別のアカウントでログインボタンタップ
    private fun Complete() {
        // 入力チェック
        when {
        // 会社未選択
            this.settings.getString("COMPANY_NUMBER_SELECT") == "" -> {
                ShowMessages().Show(context, context!!.getString(R.string.company_not_select))
                ButtonClear()
            }
        // 電話番号未入力
            this.settings.getString("PHONE_NUMBER_INPUT") == "" -> {
                ShowMessages().Show(context, context!!.getString(R.string.phone_number_nothing))
                ButtonClear()
            }
        // 電話番号が10,11桁ではない
            this.settings.getString("PHONE_NUMBER_INPUT").length < 10 -> {
                ShowMessages().Show(context, context!!.getString(R.string.phone_number_mismatch))
                ButtonClear()
            }
        // パスワード未入力
            this.settings.getString("PASSWORD_INPUT") == "" -> {
                ShowMessages().Show(context, context!!.getString(R.string.password_nothing))
                ButtonClear()
            }
        // 入力に問題なし
            else -> {
                // ログイン認証
                Authenticate()
            }
        }
    }

    private fun ButtonClear() {
        // 画像を元に戻す
        account_change_button.setImageResource(R.drawable.login)
    }

    /* -------------------- 通信処理 -------------------- */
    private fun Authenticate() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val data: JSONObject = result.getJSONObject("Authenticate")
                        val user_data: JSONObject = data.getJSONObject("User")
                        // 各種データを設定ファイルに保存
                        if (!context!!.isSameAccount(user_data)) {
                            context!!.removePointOut()
                        }
                        context!!.saveAccount(user_data)

                        // 邸一覧画面へ
                        BackMain()
                    } else {
                        // 認証失敗
                        showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                    ButtonClear()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
                hideHub()
            }
        })

        // パラメータ作成
        val params = SendParameter()
        params.CompanyNumber = this.settings.getString("COMPANY_NUMBER_SELECT")
        params.PhoneNumber = this.settings.getString("PHONE_NUMBER_INPUT")
        params.Password = Constant.GetAES(this.settings.getString("PASSWORD_INPUT"), Constant.ENCODE_KEY, Constant.ENCODE_IV)

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.AuthenticateURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}
