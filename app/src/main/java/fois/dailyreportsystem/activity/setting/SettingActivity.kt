package fois.dailyreportsystem.activity.setting

// 作成日：2017/08/18
// 更新日：2018/02/13

import android.content.Intent
import android.os.Bundle
import com.google.gson.Gson
import fois.dailyreportsystem.BuildConfig

import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.setting_layout.*
import org.json.JSONObject

// 設定画面のアクティビティ
class SettingActivity : BaseActivity() {

    // 変数宣言
    // なし

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.setting_layout)

        // クリックイベント
        // バックボタンタップ
        back_arrow.setOnSafeClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            backMain()
        }

        // 会社コードフィールドタップ
        company_number.setOnSafeClickListener {
            selectCompanyNumber()
        }

        // 写真保存フィールドタップ
        save_photo.setOnClickListener {
            // チェックボックス切替
            save_photo_checkbox.isChecked = !save_photo_checkbox.isChecked
        }

        // メールアドレスフィールドタップ
        phone_mail.setOnClickListener {
            selectPhoneMail()
        }


    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        // バージョン情報を記述
        version!!.text = context!!.getString(R.string.version) + BuildConfig.VERSION_NAME

        // 会社コード、名前の取得
        val companyNumber = this.settings.getString("COMPANY_NUMBER" )
        val companyName: String = this.settings.getString("COMPANY_NAME" )
        // 会社コード、会社名の出力
        company_number_select.text = companyNumber
        company_name_output.text = companyName
        // 写真を保存するか否かの設定
        save_photo_checkbox.isChecked = this.settings.getBoolean("PHOTO_SAVE")

        save_photo_checkbox.setOnCheckedChangeListener { _, _ ->
            this.settings.put("PHOTO_SAVE", save_photo_checkbox.isChecked)
        }

        // メールアドレスを取得
        getUser()

    }

    /* -------------------- 画面遷移 -------------------- */

    // 会社コード選択画面（会社情報画面）へ
    private fun selectCompanyNumber() {
        if(clickPosition == null) {
            clickPosition = true
            val intent = Intent(applicationContext, CompanyInfoActivity::class.java)
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
        }
    }

    // メールアドレス変更画面へ
    private fun selectPhoneMail(){
        if(clickPosition == null) {
            clickPosition = true
            val intent = Intent(applicationContext, UpdateUserPhoneMailActivity::class.java)
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
        }
    }

    // 邸一覧画面へ戻る
    private fun backMain() {
        if(clickPosition == null) {
            clickPosition = true
            // 邸一覧画面へ戻る
            finish()
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
        }
    }

    /* -------------------- ビュー操作 -------------------- */
    // メールアドレスセット
    private fun setPhoneMail(string: String) {
        phone_mail_select.text = string
    }
    /* -------------------- タップイベント -------------------- */
    // 端末のバックボタン制御
    override fun onBackPressed() {
        // 邸一覧へ戻る
        backMain()
    }

    /* -------------------- 通信処理 -------------------- */

    // メールアドレスの取得
    private fun getUser() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val user = result.getJSONObject("User")
                        val userPhoneMail = user.getString("UserPhoneMail")

                        // メールアドレスセット
                        setPhoneMail(userPhoneMail)

                        // メールアドレスを保存
                        context!!.settings.put("USER_PHONE_MAIL", userPhoneMail)

                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.User

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

}