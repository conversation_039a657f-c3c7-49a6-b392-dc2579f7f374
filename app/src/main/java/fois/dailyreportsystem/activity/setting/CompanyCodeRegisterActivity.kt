package fois.dailyreportsystem.activity.setting

// 作成日：2017/08/18
// 更新日：2018/02/13

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.content.ContextCompat
import android.text.InputFilter
import android.view.MotionEvent
import android.view.inputmethod.InputMethodManager
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.android.synthetic.main.company_code_register_layout.*

import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.CompanyList
import fois.dailyreportsystem.data.JsonData
import fois.dailyreportsystem.data.Worker
import fois.dailyreportsystem.util.*
import org.json.JSONObject
import java.lang.reflect.Type

// 会社コード登録画面のアクティビティ
class CompanyCodeRegisterActivity : BaseActivity() {

    // 変数宣言
    private var CompanyNumber: String? = null                 // 会社コード
    private var PhoneNumber: String? = null                 // 電話番号
    private var Password: String? = null                    // パスワード
    private val companyList: CompanyList = CompanyList()      // 設定ファイルに保存する会社リスト（1行分）
    private var list: ArrayList<CompanyList> = ArrayList()     // 設定ファイルに保存する会社リスト
    private var getCompanyList: String? = null                       // 設定ファイルから取得した会社リスト
    private var type: Type? = null                            // 型
    // 入力制御フィルター（英数字以外入力不可にする）
    private val filters = arrayOf<InputFilter>(PasswordFilter())

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.company_code_register_layout)

        // EditText制御
        password_input.filters = filters

        // 設定ファイルから会社リスト取得
        getCompanyList = this.settings.getString("COMPANY_LIST")
        type = object : TypeToken<java.util.ArrayList<CompanyList>>() {}.type
        list = Gson().fromJson(getCompanyList, type)

        // クリックイベント
        // 戻るボタンタップ
        back_arrow.setOnSafeClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            BackCompanyInfo()
        }

        // ツールバー登録ボタンタップ
        register.setOnSafeClickListener {
            register.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
            InputCheck()
        }

        // 登録するボタンタップ
        company_number_register.setOnSafeClickListener {
            company_number_register_button.setImageResource(R.drawable.com_regist_on)
            InputCheck()
        }
    }

    override fun onRestart() {
        super.onRestart()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }
    }

    /* -------------------- 画面遷移 -------------------- */

    // 会社コード選択画面（会社情報画面）へ戻る
    private fun BackCompanyInfo() {
        if(clickPosition == null) {
            clickPosition = true
        val intent = Intent(this, CompanyInfoActivity::class.java)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
    }
    }

    // 邸一覧画面へ戻る
    private fun BackMain() {
        if(clickPosition == null) {
            clickPosition = true
        finish()
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
    }
    }

    /* -------------------- タップイベント -------------------- */
    // 端末のバックボタン制御
    override fun onBackPressed() {
        BackCompanyInfo()
    }

    // 画面タッチイベント
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(main_layout.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す
        main_layout.requestFocus()

        return super.dispatchTouchEvent(ev)
    }

    // 完了ボタンタップ、入力チェックして通信処理へ
    private fun InputCheck() {
        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }
        // 入力チェック
        when {
        // 会社コード未入力
            company_number_input.text.isEmpty() -> {
                ShowMessages().Show(context, context!!.getString(R.string.company_number_nothing))
                ColorClear()
            }
        // 会社コードが6桁ではない
            company_number_input.text.length != 6 -> {
                ShowMessages().Show(context, context!!.getString(R.string.company_number_mismatch))
                ColorClear()
            }
        // 電話番号未入力
            phone_number_input.text.isEmpty() -> {
                ShowMessages().Show(context, context!!.getString(R.string.phone_number_nothing))
                ColorClear()
            }
        // 電話番号が10,11桁ではない
            phone_number_input.text.length < 10 -> {
                ShowMessages().Show(context, context!!.getString(R.string.phone_number_mismatch))
                ColorClear()
            }
        // パスワード未入力
            password_input.text.isEmpty() -> {
                ShowMessages().Show(context, context!!.getString(R.string.password_nothing))
                ColorClear()
            }
        // 登録チェック
            else -> {
                CompanyNumber = company_number_input.text.toString()
                var addFlag = true
                for (i in 0..list.count() - 1) {
                    if (CompanyNumber == list[i].CompanyNumber) {
                        // 会社コードが登録されていたら追加不可
                        addFlag = false
                        break
                    }
                }
                if (addFlag) {
                    // 入力した会社コードがまだ登録されていない
                    PhoneNumber = phone_number_input.text.toString()
                    Password = password_input.text.toString()
                    // パスワードの暗号化
                    Password = Constant.GetAES(Password!!, Constant.ENCODE_KEY, Constant.ENCODE_IV)
                    // 通信
                    CompanyNumberCheckAsync()
                } else {
                    // すでに登録されている
                    ShowMessages().Show(context, context!!.getString(R.string.company_number_add_alert))
                    ColorClear()
                }
            }
        }
    }

    private fun ColorClear() {
        register.setTextColor(ContextCompat.getColor(this, R.color.white))
        company_number_register_button.setImageResource(R.drawable.com_regist)
    }

    /* -------------------- 通信処理 -------------------- */
    private fun CompanyNumberCheckAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {
                showHub()
            }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(context, context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功時
                        val data: JSONObject = result.getJSONObject("Authenticate")
                        val user_data: JSONObject = data.getJSONObject("User")
                        // 会社名、会社コードをリストに格納
                        companyList.CompanyNumber = user_data.getString("CompanyNumber")
                        companyList.CompanyName = user_data.getString("CompanyName")
                        list.add(companyList)
                        // ユーザリストの更新
                        val getUserList: String? = context!!.settings.getString("USER_LIST", null)
                        type = object : TypeToken<java.util.ArrayList<Worker>>() {}.type
                        val userList: ArrayList<Worker> = Gson().fromJson(getUserList, type)
                        val worker = Worker()
                        worker.CompanyID = user_data.getString("CompanyID")
                        worker.UserID = user_data.getString("UserID")
                        worker.UserName = user_data.getString("UserName")
                        userList.add(worker)
                        // 会社リスト
                        context!!.settings.put("COMPANY_LIST", Gson().toJson(list))
                        // ユーザーリスト
                        context!!.settings.put("USER_LIST", Gson().toJson(userList))

                        // 各種データを設定ファイルに保存
                        if (!context!!.isSameAccount(user_data)) {
                            context!!.removePointOut()
                        }
                        // 電話番号とパスワードを保存
                        context!!.settings.put("PHONE_NUMBER_INPUT", PhoneNumber)
                        context!!.settings.put("PASSWORD_INPUT_AES", Password)
                        context!!.saveAccount(user_data)

                        // 端末IDの登録
                        SendTokenAsync()
                    } else {
                        // 認証失敗
                        showOKDialog(status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(context, context!!.getString(R.string.unexpected_error))
                    log(context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                    ColorClear()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.CompanyNumber = CompanyNumber
        params.PhoneNumber = PhoneNumber
        params.Password = Password

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.AuthenticateURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

    // 端末IDの保存
    private fun SendTokenAsync() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    return
                }
                // 邸一覧画面へ
                BackMain()
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // パラメータ作成
        val users: Worker = Worker()
        users.CompanyID = this.settings.getString("COMPANY_ID")
        users.UserID = this.settings.getString("USER_ID")
        users.UserName = this.settings.getString("USER_NAME")
        val Users: ArrayList<Worker> = ArrayList()
        Users.add(users)

        val jsonData: JsonData = JsonData()
        jsonData.NewDeviceID = this.settings.getString("NEW_DEVICE_ID")
        jsonData.DeviceType = "Android"
        jsonData.Users = Users

        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.JsonData = Gson().toJson(jsonData)

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.UpdateDeviceIDURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }
}
