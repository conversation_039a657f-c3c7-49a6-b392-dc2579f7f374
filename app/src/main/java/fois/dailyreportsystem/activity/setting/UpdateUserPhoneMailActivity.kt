package fois.dailyreportsystem.activity.setting

// 作成日：2017/08/18
// 更新日：2018/02/13

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MotionEvent
import android.view.inputmethod.InputMethodManager
import androidx.core.content.ContextCompat
import com.google.gson.Gson

import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.setting_update_user_hone_mail.*
import org.json.JSONObject

// 設定画面のアクティビティ
class UpdateUserPhoneMailActivity : BaseActivity() {

    // 変数宣言

    var phoneMail: String? = null

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.setting_update_user_hone_mail)
        // クリックイベント
        // バックボタンタップ
        back_arrow.setOnClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            backSetting()
        }

        // ツールバー変更ボタンタップ
        change.setOnClickListener {
            change.setTextColor(ContextCompat.getColor(this, R.color.half_transparent))
            inputCheck()
        }

        // 変更ボタンタップ
        confirm_button.setOnClickListener {
            confirm_button.setBackgroundResource(R.drawable.register_button_on)
            inputCheck()
        }
    }

    override fun onResume() {
        super.onResume()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }
        // メールアドレスの取得
        phoneMail = this.settings.getString("USER_PHONE_MAIL")
        // メールアドレスの出力
        phone_mail_input.setText(phoneMail)
    }

    /* -------------------- 画面遷移 -------------------- */

    // 邸一覧画面へ戻る
    private fun backSetting() {
        if(clickPosition == null) {
            clickPosition = true
            // 設定画面へ戻る
            val intent = Intent(this, SettingActivity::class.java)
            startActivity(intent)
            finish()
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
        }
    }

    /* -------------------- タップイベント -------------------- */
    // 端末のバックボタン制御

    // 画面タッチイベント
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(main_layout.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す
        main_layout.requestFocus()

        return super.dispatchTouchEvent(ev)
    }

    // 完了ボタンタップ時
    private fun inputCheck() {
        val regex = Regex("[A-Z0-9a-z._+-]+@[A-Za-z0-9.-]+[.]+[A-Za-z]{2,4}")
        // 入力チェック
        when {
            // メールアドレス未入力
            phone_mail_input.text.isEmpty() -> {
                ShowMessages().Show(context, context!!.getString(R.string.user_phone_mail_nothing))
                change.setTextColor(ContextCompat.getColor(this, R.color.white))
                colorClear()
            }
            phone_mail_input.text.toString() == phoneMail -> {
                ShowMessages().Show(context, context!!.getString(R.string.user_phone_mail_no_change))
                change.setTextColor(ContextCompat.getColor(this, R.color.white))
                colorClear()
            }
            // メールアドレスではない
            !regex.matches(phone_mail_input.text.toString()) -> {
                ShowMessages().Show(context, context!!.getString(R.string.user_phone_mail_mismatch))
                change.setTextColor(ContextCompat.getColor(this, R.color.white))
                colorClear()
            }
            // 入力に問題なし
            else -> {
                // メールアドレスの変更処理
                updateUserPhoneMail()
            }
        }
    }

    private fun colorClear() {
        confirm_button.setBackgroundResource(R.drawable.register_button)
    }

    /* -------------------- 通信処理 -------------------- */

    // メールアドレスの変更
    private fun updateUserPhoneMail() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 邸一覧へ戻る
                        backSetting()
                    }
                } catch (ex: Exception) {
                    log(context?.javaClass!!.name, ex.toString())
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = this.settings.getString("USER_ID")
        params.UserPhoneMail = phone_mail_input.text.toString()

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.UpdateUserPhoneMail

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(context?.javaClass!!.name, url + jsonParams)
    }

}
