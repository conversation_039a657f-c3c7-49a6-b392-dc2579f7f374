package fois.dailyreportsystem.activity.setting

// 作成日：2017/09/14
// 更新日：2018/02/13

import android.app.AlertDialog
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.CompanyList
import fois.dailyreportsystem.util.*
import kotlinx.android.synthetic.main.company_code_delete_layout.*
import java.lang.reflect.Type

// 会社コード削除画面のアクティビティ
class CompanyCodeDeleteActivity : BaseActivity() {

    // 変数宣言
    private var CompanyNumber: String? = null                   // 会社コード
    private var CompanyName: String? = null                     // 会社名
    private var list: ArrayList<CompanyList> = ArrayList()      // 設定ファイルに保存する会社リスト
    private var getList: String? = null                         // 設定ファイルから取得した会社リスト
    private var type: Type? = null                              // 型

    /* -------------------- ライフサイクル -------------------- */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.company_code_delete_layout)

        // 設定ファイルから会社リスト取得
        getList = this.settings.getString("COMPANY_LIST", null)
        type = object : TypeToken<java.util.ArrayList<CompanyList>>() {}.type
        list = Gson().fromJson(getList, type)

        // タップイベント
        // 戻るボタンタップ
        back_arrow.setOnSafeClickListener {
            back_arrow.setImageResource(R.drawable.com_headerarrow_on)
            BackCompanyInfo()
        }

        // 会社コード削除タップ
        company_number_delete.setOnSafeClickListener { NumberDelete() }
    }

    override fun onResume() {
        super.onResume()

        // 削除する会社コード、会社名を取得
        CompanyNumber = this.settings.getString("DELETE_COMPANY_NUMBER")
        CompanyName = this.settings.getString("DELETE_COMPANY_NAME")

        // 削除する会社コード、会社名を表示
        company_number_output.text = CompanyNumber
        company_name_output.text = CompanyName
    }

    override fun onRestart() {
        super.onRestart()

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }
    }

    /* -------------------- 画面遷移 -------------------- */
    // 会社情報画面へ戻る
    private fun BackCompanyInfo() {
        if(clickPosition == null) {
            clickPosition = true
        val intent = Intent(this, CompanyInfoActivity::class.java)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
    }
    }

    /* -------------------- タップイベント -------------------- */
    // 端末のバックボタン制御
    override fun onBackPressed() {
        BackCompanyInfo()
    }

    // 会社コード削除タップ
    private fun NumberDelete() {

        // タイムアウトチェック
        if (!this.loginCheck()){
            moveLogin()
            return
        }

        // 現在の時間を保存
        this.settings.put("LAST_HANDLE", System.currentTimeMillis())

        val alertDialog: AlertDialog.Builder = AlertDialog.Builder(this)
        // ダイアログタイトル
        alertDialog.setTitle(context!!.getString(R.string.company_number_delete_dialog_title))
        // ダイアログメッセージ
        alertDialog.setMessage(
                context!!.getString(R.string.company_number_delete_dialog_message1)
                        + CompanyNumber
                        + context!!.getString(R.string.company_number_delete_dialog_message2)
        )
        // ダイアログOKボタン
        alertDialog.setPositiveButton(context!!.getString(R.string.company_number_delete_dialog_yes),
                DialogInterface.OnClickListener { _, _ ->
                    // 現在選択中の会社をリストから削除
                    for (i in 0 until list.count()) {
                        if (list[i].CompanyNumber == CompanyNumber) {
                            list.removeAt(i)
                            break
                        }
                    }
                    // 新しいリストで会社リストを設定ファイルに保存
                    this.settings.put("COMPANY_LIST", Gson().toJson(list))
                    // 会社情報を削除した旨を伝えるメッセージを表示
                    ShowMessages().Show(context, context!!.getString(R.string.company_number_delete_message))
                    // 前の画面に戻る
                    BackCompanyInfo()
                })
        // ダイアログNGボタン
        alertDialog.setNegativeButton(context!!.getString(R.string.company_number_delete_dialog_no),
                DialogInterface.OnClickListener { _, _ ->
                    // いいえの場合、何もしない
                })
        alertDialog.setCancelable(true)
        val dialog: AlertDialog = alertDialog.create()
        dialog.show()
    }
}
