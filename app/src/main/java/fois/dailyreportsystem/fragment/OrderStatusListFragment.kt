package fois.dailyreportsystem.fragment

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ListView
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.activity.MainActivity
import fois.dailyreportsystem.activity.request.OrderDetailActivity
import fois.dailyreportsystem.activity.survey.ReadjustmentListActivity
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.base.BaseFragment
import fois.dailyreportsystem.data.Calender
import fois.dailyreportsystem.data.OrderStatus
import fois.dailyreportsystem.data.OrderStatusListAdapter
import fois.dailyreportsystem.util.*
import fois.dailyreportsystem.util.weekcalendar.WeekCalendar
import org.joda.time.DateTime
import org.json.JSONObject
import java.util.*
import kotlin.collections.ArrayList




class OrderStatusListFragment : BaseFragment() {

    private var orderStatusList: MutableList<OrderStatus> = mutableListOf()
    private var filterList: MutableList<OrderStatus> = mutableListOf()
    private lateinit var weekCalendar: WeekCalendar
    private lateinit var adapter :OrderStatusListAdapter
    private lateinit var order_list: ListView
    private var startDate = Date().startWeek()
    private var endDate = Date().endWeek()
    private var events: ArrayList<DateTime> = arrayListOf()
    private var holidays: ArrayList<DateTime> = arrayListOf()
    private var yearMonth: ArrayList<String> = arrayListOf()

    override fun onAttach(context: Context) {
        super.onAttach(context)
        this._context = context
        basePreference = context.getSharedPreferences("Settings", Context.MODE_PRIVATE)
        _activity = context as MainActivity

    }

    // Fragmentで表示するViewを作成するメソッド
    @SuppressLint("ClickableViewAccessibility")
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        super.onCreateView(inflater, container, savedInstanceState)
        val view = inflater.inflate(R.layout.order_status_list_fragment_layout, container, false)

        weekCalendar = view.findViewById(R.id.weekCalendar) as WeekCalendar
        order_list = view.findViewById(R.id.order_list) as ListView
        //
        weekCalendar.setOnWeekChangeListener { firstDayOfTheWeek, forward ->
            startDate = firstDayOfTheWeek.toDate().startWeek()
            endDate = firstDayOfTheWeek.toDate().endWeek()
            checkCalendar()
            setListView()
            order_list.invalidateViews()
        }
        // 日付クリック
        weekCalendar.setOnDateClickListener {
            if (!this::adapter.isInitialized) {
                checkCalendar()
                setListView()
            }
            val date = it.toDate().startTime()
            // リストに日付を渡す
            adapter.clickDate = date
            order_list.invalidateViews()

            // リストの該当に移動
            val index = filterList.indexOfFirst { orderStatus -> orderStatus.date == date }
            if(index >= 0){ order_list.setSelection(index) }
        }

        order_list.onItemClickListener =  AdapterView.OnItemClickListener { parent, view, position, id ->
            val orderStatus = order_list.getItemAtPosition(position) as OrderStatus
            if (orderStatus != null) {
                if( orderStatus.schedule != null) {
                    val intent = Intent(_context, OrderDetailActivity::class.java)
                    _context.settings.put("SCHEDULE_ID", orderStatus.schedule!!.scheduleID)
                    _context.settings.put("RESEARCH_TASK_ID", orderStatus.schedule!!.researchTaskID)
                    startActivity(intent)
                    _activity.overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
                }
            }
        }

        // リストをスワイプ
        order_list.setOnTouchListener(object: OnSwipeTouchListener() {
            override fun onSwipeLeft() {
                weekCalendar.weekPager.adapter.swipeForward()
                startDate = weekCalendar.weekPager.adapter.date.toDate().startWeek()
                endDate = weekCalendar.weekPager.adapter.date.toDate().endWeek()
                checkCalendar()
                weekCalendar.weekPager.initPager(weekCalendar.weekPager.adapter.date)
                weekCalendar.weekPager.adapter.events = events
                //order_list.invalidateViews()
            }

            override fun onSwipeRight() {
                weekCalendar.weekPager.adapter.swipeBack()
                startDate = weekCalendar.weekPager.adapter.date.toDate().startWeek()
                endDate = weekCalendar.weekPager.adapter.date.toDate().endWeek()
                checkCalendar()
                weekCalendar.weekPager.initPager(weekCalendar.weekPager.adapter.date)
                weekCalendar.weekPager.adapter.events = events
                //order_list.invalidateViews()
            }
        })

        countHub = 0

        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

    }

    override fun onResume() {
        super.onResume()

        getSchedulesList()
        getCalender()

        // 遷移パラメータを表示(再表示)に初期化
        basePreference.edit().putString("RESEARCH_TASK_ID", "").apply()
    }


    // 画面タッチイベント


    private fun checkCalendar(){
        if( yearMonth.indexOf(startDate.toString("yyyyMM")) == -1){
            getCalender()
        }
    }

    private fun setWeekView(){
        if (countHub > 0) return
        if (holidays.count() == 0) {
            // モーダルを消す
            hideHub()
            return
        }
        // 更新
        weekCalendar.weekPager.adapter.events = events
        weekCalendar.holidays = holidays
        weekCalendar.weekPager.adapter.notifyDataSetChanged()

        setListView()
        // モーダルを消す
        hideHub()
    }

    /**
     * setListView List生成処理.
     *
     */
    private fun setListView() {
        // 絞り込み
        filterList = orderStatusList.filter { it.date != null && it.date!! >= startDate && it.date!! < endDate }.toMutableList()
        adapter = OrderStatusListAdapter(_context, filterList)
        adapter.clickDate = Date().startTime()
        order_list.adapter = adapter
        // リストビューを保存した位置から表示する
        order_list.setSelectionFromTop(context!!.position.getInt("ORDER_STATUS_LIST1"), context!!.position.getInt("ORDER_STATUS_LIST2"))
    }

    /**
     * forwardReadjustment
     *
     * */
    fun forwardReadjustment() {
        val intent = Intent(_context, ReadjustmentListActivity::class.java)
        startActivity(intent)
        _activity.overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
    }


    /**
     * getSchedulesList 調査.
     *
     * */
    private fun getSchedulesList() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.connect_error))
                    hideHub()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")

                    // 認証成功
                    if (status.getInt("StatusCode") == 0) {
                        val scheduleArrayList = result.getJSONArray("Schedules")
                        val schedules = JsonHandling.toScheduleArrayList(scheduleArrayList)
                        orderStatusList = mutableListOf()
                        val headerList = mutableListOf<Date>()
                        schedules?.forEach { schedule ->
                            val startDate = schedule.startDateTime!!.toDate() ?: return@forEach
                            val startDateTime = startDate.startTime()
                            if (headerList.find { date -> date == startDateTime } == null){
                                orderStatusList.add(OrderStatus(
                                        date = startDateTime,
                                        schedule = null,
                                        isHeader = true
                                ))
                                headerList.add(startDateTime)
                                events.add(DateTime(startDateTime.addDay(1).startTime()).withMillisOfDay(0))
                            }
                            orderStatusList.add(OrderStatus(
                                    date = startDate,
                                    schedule = schedule,
                                    isHeader = false
                            ))
                        }
                        orderStatusList.sortBy { it.date }
                    } else {
                        // 認証失敗
                        (_activity as MainActivity).showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.unexpected_error))
                    BaseActivity.log(BaseActivity.context?.javaClass!!.name, ex.toString())
                }
                setWeekView()
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() { countHub -= 1 }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = basePreference.getString("USER_ID", "")
        params.OfficeID = basePreference.getString("OFFICE_ID", "")

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTasks

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        BaseActivity.log(BaseActivity.context?.javaClass!!.name, url + jsonParams)
    }

    private fun getCalender() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.connect_error))
                    hideHub()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    // 認証失敗
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val calendarArrayList = result.getJSONArray("Calender")
                        val holidayCalendarList = Calender.toArrayList(calendarArrayList) ?: arrayListOf()
                        holidayCalendarList.forEach {
                            if(it.BusinessDayID == "4"){
                                val date = it.Date.toDate(Constant.FORMAT_DATE_HYPHEN) ?: return@forEach
                                holidays.add(DateTime(date.addDay(1).startTime()).withMillisOfDay(0))
                            }
                        }
                        yearMonth.add(startDate.addMonth(-1).toString("yyyyMM"))
                        yearMonth.add(startDate.toString("yyyyMM"))
                        yearMonth.add(startDate.addMonth(1).toString("yyyyMM"))
                    } else (_activity as MainActivity).showOKDialog("", status.getString("StatusMsg"))
                } catch (ex: Exception) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.unexpected_error))
                    BaseActivity.log(BaseActivity.context?.javaClass!!.name, ex.toString())
                }
                setWeekView()
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() { countHub -= 1 }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = basePreference.getString("USER_ID", "")
        params.Year = startDate.year().toString()
        params.Month =  startDate.month().toString()

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.Clalender
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        BaseActivity.log(BaseActivity.context?.javaClass!!.name, url + jsonParams)
    }
}