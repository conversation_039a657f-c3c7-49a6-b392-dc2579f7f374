package fois.dailyreportsystem.fragment

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.activity.plan.AreaActivity
import fois.dailyreportsystem.activity.MainActivity
import fois.dailyreportsystem.activity.survey.SurveyPropertyActivity
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.base.BaseFragment
import fois.dailyreportsystem.data.*
import fois.dailyreportsystem.data.adapter.SurveyPropertyAdapter
import fois.dailyreportsystem.util.*
import org.json.JSONObject



class SurveyFragment : BaseFragment() {

    private var researchTaskPropertyList: ArrayList<Schedule>? = ArrayList<Schedule>()

    private lateinit var userID: String


    private var listPosition1: Int = 0        // リストビュー位置保存用
    private var listPosition2: Int = 0        // リストビュー位置保存用

    private lateinit var viewMainList: ListView
    private lateinit var searchViewTask: SearchView
    private lateinit var swipeRefresh: SwipeRefreshLayout
    private lateinit var transparentView: View
    private lateinit var mainFragmentLayout: RelativeLayout

    private var itemAdapter: SurveyPropertyAdapter? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        this._context = context
        basePreference = context.getSharedPreferences("Settings", Context.MODE_PRIVATE)
        _activity = context as MainActivity

    }

    // Fragmentで表示するViewを作成するメソッド
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        super.onCreateView(inflater, container, savedInstanceState)
        val view = inflater.inflate(R.layout.survey_property_fragment_layout, container, false)

        basePreference.edit().putString("RESEARCH_ID", "").apply()

        // TextViewをひも付けます
        viewMainList = view.findViewById(R.id.survey_list)
        mainFragmentLayout = view.findViewById(R.id.survey_fragment)
        searchViewTask = view.findViewById(R.id.searchViewTask)
        hideSearchPlate()

        transparentView = view.findViewById(R.id.transparent_view)
        swipeRefresh = view.findViewById(R.id.swipeRefreshLayout) as SwipeRefreshLayout

        val bundle = arguments as Bundle
        userID = bundle.getString("USER_ID", "")

        transparentView.setOnClickListener { keyboardHide() }

        val linear1: LinearLayout = searchViewTask.getChildAt(0) as LinearLayout
        val linear2: LinearLayout = linear1.getChildAt(2) as LinearLayout
        val linear3: LinearLayout = linear2.getChildAt(1) as LinearLayout
        val autoComplete: AutoCompleteTextView = linear3.getChildAt(0) as AutoCompleteTextView

        searchViewTask.setOnQueryTextListener(object : SearchView.OnQueryTextListener {

            override fun onQueryTextSubmit(query: String): Boolean {
                BaseActivity.log("onQueryTextSubmit", query)
                val inputMethodManager = _context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                inputMethodManager.hideSoftInputFromWindow(mainFragmentLayout.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
                mainFragmentLayout.requestFocus()
                return false
            }

            override fun onQueryTextChange(query: String): Boolean {
                BaseActivity.log("onQueryTextChange", query)
                return false
            }
        })

        searchViewTask.setOnQueryTextFocusChangeListener { _, queryTextFocused ->
            if (!queryTextFocused) {
                if(itemAdapter != null) {
                    itemAdapter!!.filter.filter(searchViewTask.query)
                    if (searchViewTask.query.toString().isEmpty()) {
                        searchMode()
                    }
                }
                transparentView.visibility = View.GONE
            } else {
                transparentView.visibility = View.VISIBLE
            }
        }

        swipeRefresh.setOnRefreshListener {
            getOrderList()
        }

        // 検索の文字サイズ変更
        autoComplete.textSize = 14F


        return view
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
    }

    override fun onResume() {
        super.onResume()

        searchViewTask.setQuery(basePreference.getString("SURVEY_LIST_FILTER", ""), false)

        getOrderList()
    }

    // 検索中、検索エリア以外タップ
    private fun keyboardHide() {
        val inputMethodManager = _context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(mainFragmentLayout.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す
        mainFragmentLayout.requestFocus()
    }

    private fun hideSearchPlate() {
        val searchPlateId = searchViewTask.context.resources.getIdentifier("android:id/search_plate", null, null)
        val searchPlateView = searchViewTask.findViewById<View>(searchPlateId) ?: return
        searchPlateView.setBackgroundResource(R.color.transparent)
    }

    private fun setSurveyListView() {

        if (!researchTaskPropertyList.isNullOrEmpty()) {

            viewMainList.visibility = View.VISIBLE

            itemAdapter = SurveyPropertyAdapter(_context, researchTaskPropertyList!!)
            viewMainList.adapter = itemAdapter

            //Click item event
            viewMainList.onItemClickListener = clickItem

            // 更新をかけたときにもフィルターをかける
            itemAdapter!!.filter.filter(searchViewTask.query)
            viewMainList.setSelectionFromTop(_context.position.getInt("SURVEY_LIST1"), _context.position.getInt("SURVEY_LIST2"))
            _context.position.put("SURVEY_LIST1", 0)
            _context.position.put("SURVEY_LIST2", 0)
        } else {
            // 表示できる調査物件なし
            viewMainList.visibility = View.GONE
        }
    }

    // リスト選択
    private val clickItem = AdapterView.OnItemClickListener { _, _, position, _ ->
        viewMainList.safeClick()

        _context.position.put("SURVEY_LIST1", viewMainList.firstVisiblePosition)
        _context.position.put("SURVEY_LIST2", viewMainList.getChildAt(0).top)

        basePreference.edit().putString("SURVEY_LIST_FILTER", searchViewTask.query.toString()).apply()

        val surveyData = viewMainList.adapter.getItem(position) as Schedule
        val researchID = surveyData.researchTaskID
        val intent = Intent(_context, SurveyPropertyActivity::class.java)
        basePreference.edit().putString("RESEARCH_ID", researchID).apply()
        startActivity(intent)
        _activity.overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
    }

    // エリア選択ページへ遷移
    fun forwardSelectArea() {
        val intent = Intent(_context, AreaActivity::class.java)
        startActivity(intent)
        _activity.overridePendingTransition(0, R.anim.fade_out)
    }

    /**
     * searchMode 検索フィルタ実行
     * */
    private fun searchMode() {
        // 検索エリアをフォーカスし、キーボードを表示も
        searchViewTask.requestFocus()
        val inputMethodManager = _context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        inputMethodManager.toggleSoftInput(1, InputMethodManager.SHOW_IMPLICIT)
    }

    /**
     * getOrderList 調査物件の一覧を取得する。
     *
     * */
    private fun getOrderList() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val taskArray = result.getJSONArray("Schedules")

                        researchTaskPropertyList = JsonHandling.toScheduleArrayList(taskArray)

                        // 順番並び替え surveyStatusID：降順、startDateTime：昇順
                        researchTaskPropertyList!!.sortBy { (10 - (it.surveyStatusID ?: "0").toInt()).toString() + it.startDateTime }

                        setSurveyListView()
                    } else {
                        // 認証失敗
                        (_activity as BaseActivity).showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(fois.dailyreportsystem.R.string.unexpected_error))
                    BaseActivity.log(BaseActivity.context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                    swipeRefresh.isRefreshing = false
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
                hideHub()
                swipeRefresh.isRefreshing = false
            }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = userID
        params.OfficeID = basePreference!!.getString("OFFICE_ID", "")

        val areaId = basePreference!!.getString("SELECT_AREA_ID", "0")
        if (areaId != "0") {
            params.AreaID = areaId
        }

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.OrderResearchTasks

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        BaseActivity.log(BaseActivity.context?.javaClass!!.name, url + jsonParams)
    }

}