package fois.dailyreportsystem.fragment

import android.app.AlertDialog
import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.gson.Gson
import fois.dailyreportsystem.R
import android.app.ProgressDialog
import android.content.SharedPreferences
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import androidx.appcompat.widget.Toolbar
import android.view.inputmethod.InputMethodManager
import android.widget.*
import fois.dailyreportsystem.activity.MainActivity
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.base.BaseActivity.Companion.log
import fois.dailyreportsystem.base.BaseFragment
import fois.dailyreportsystem.data.Main
import fois.dailyreportsystem.data.adapter.MainAdapter
import fois.dailyreportsystem.util.AsyncJsonLoader
import fois.dailyreportsystem.util.Constant
import fois.dailyreportsystem.util.SendParameter
import fois.dailyreportsystem.util.ShowMessages
import org.json.JSONObject


class MainFragment : BaseFragment() {

    private var alertDialog: AlertDialog.Builder? = null                // アラートダイアログ
    private lateinit var userID: String

    var itemAdapter: MainAdapter? = null
    var listTask: java.util.ArrayList<Main> = java.util.ArrayList()

    var listPosition1: Int = 0        // リストビュー位置保存用
    var listPosition2: Int = 0        // リストビュー位置保存用

    var searchAreaShow: Boolean = false        // 検索エリアの表示・非表示を示すフラグ

    private lateinit var viewMainList: ListView
    private lateinit var searchViewTask: SearchView
    private lateinit var searchLayout: RelativeLayout
    private lateinit var swipeRefresh: SwipeRefreshLayout
    private lateinit var transparentView: View
    private lateinit var mainFragmentLayout: RelativeLayout
    private lateinit var toolBar: Toolbar
    private lateinit var noData : TextView

    // Fragmentで表示するViewを作成するメソッド
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        super.onCreateView(inflater, container, savedInstanceState)
        var view = inflater!!.inflate(R.layout.main_fragment_layout, container, false)

        mainFragmentLayout = view.findViewById(R.id.main_fragment_layout)

        val bundle = arguments as Bundle
        userID = bundle.getString("USER_ID", "")

        searchViewTask = view.findViewById(R.id.searchViewTask)
        hideSearchPlate()

        transparentView = view.findViewById(R.id.transparent_view)

        transparentView.setOnClickListener { keyboardHide() }

        val linear1: LinearLayout = searchViewTask.getChildAt(0) as LinearLayout
        val linear2: LinearLayout = linear1.getChildAt(2) as LinearLayout
        val linear3: LinearLayout = linear2.getChildAt(1) as LinearLayout
        val autoComplete: AutoCompleteTextView = linear3.getChildAt(0) as AutoCompleteTextView

        // 検索の文字サイズ変更
        autoComplete.textSize = 14F

        searchViewTask.setOnQueryTextListener(object : SearchView.OnQueryTextListener {

            override fun onQueryTextSubmit(query: String): Boolean {
                log("onQueryTextSubmit", query)
                val inputMethodManager = _context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                inputMethodManager.hideSoftInputFromWindow(mainFragmentLayout!!.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
                mainFragmentLayout!!.requestFocus()
                return false
            }

            override fun onQueryTextChange(query: String): Boolean {
                log("onQueryTextChange", query)
                return false
            }
        })

        searchViewTask.setOnQueryTextFocusChangeListener { _, queryTextFocused ->
            if (!queryTextFocused) {
                itemAdapter!!.filter.filter(searchViewTask.query)

                // 検索文字列が無い場合検索エリアを非表示
                if (searchViewTask.query.toString().isEmpty()) {
                    SearchMode(false)
                }

                transparentView.visibility = View.GONE
            } else {
                transparentView.visibility = View.VISIBLE
            }
        }

        toolBar = view.findViewById(R.id.toolbar)
        toolBar.setOnClickListener { keyboardHide() }

        searchLayout = view.findViewById(R.id.search_layout)

        viewMainList = view.findViewById(R.id.list)
        viewMainList.onItemClickListener = ClickItem

        swipeRefresh = view.findViewById(R.id.LayoutListView) as SwipeRefreshLayout
        swipeRefresh.setOnRefreshListener {
            GetTaskList()
        }

        noData = view.findViewById(R.id.noData) as TextView

        return view
    }

    override fun onStart() {
        super.onStart()

        GetTaskList()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        this._context = context
        basePreference = context.getSharedPreferences("Settings", Context.MODE_PRIVATE)
        _activity = context as MainActivity
    }

    // 検索アイコンタップ
    private fun SearchMode(show: Boolean) {
        if (show) {
            searchAreaShow = true
            // 検索エリアをフォーカスし、キーボードを表示
            searchViewTask.requestFocus()
            val inputMethodManager = _context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            inputMethodManager.toggleSoftInput(1, InputMethodManager.SHOW_IMPLICIT)
        }
    }

    private fun hideSearchPlate() {
        val searchPlateId = searchViewTask.context.resources.getIdentifier("android:id/search_plate", null, null)
        val searchPlateView = searchViewTask.findViewById<View>(searchPlateId) ?: return
        searchPlateView.setBackgroundResource(R.color.transparent)
    }

    // リスト選択
    private val ClickItem = AdapterView.OnItemClickListener { parent, view, position, id ->
        val tasks = viewMainList.getItemAtPosition(position) as Main

        val builder = AlertDialog.Builder(_context)
        builder.setTitle(tasks.TaskName)
        builder.setMessage(tasks.TaskInfo)

        //閉じるボタン
        builder.setNegativeButton("閉じる") { dialog, which ->
            dialog!!.cancel()
        }

        val dialog = builder.create()
        dialog!!.show()
    }

    // 検索中、検索エリア以外タップ
    private fun keyboardHide() {
        val inputMethodManager = _context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(mainFragmentLayout.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す
        mainFragmentLayout.requestFocus()
    }

    private fun GetTaskList() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    hideHub()
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(fois.dailyreportsystem.R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val taskArray = result.getJSONArray("Tasks")
                        listTask.clear()
                        var taskIDList = ""
                        for (i in 0 until taskArray.length()) {
                            val taskRow = taskArray.getJSONObject(i)
                            val task = Main()
                            task.TaskID = taskRow.getString("TaskID")
                            taskIDList += taskRow.getString("TaskID") + ","
                            task.ShootingPhoto = taskRow.getString("ShootingPhoto")
                            task.TaskIconAddDatetime = taskRow.getString("TaskIconAddDatetime")
                            task.TaskIconUrl = taskRow.getString("TaskIconUrl")
                            task.TaskInfo = taskRow.getString("TaskInfo")
                            task.TaskName = taskRow.getString("TaskName")
                            task.TaskNameKana = taskRow.getString("TaskNameKana")
                            task.AreaName = taskRow.getString("AreaName")
                            task.ContentVersionName = taskRow.getString("ContentVersionName")
                            task.FctUserName = taskRow.getString("FctUserName")
                            task.UnreadMessageCount = taskRow.getString("UnreadMessageCount")
                            task.TaskPlanStartDate = taskRow.getString("TaskPlanStartDate")
                            task.TaskPlanCompleteDate = taskRow.getString("TaskPlanCompleteDate")
                            val workStatus = taskRow.getString("WorkStatus")
                            task.WorkStatus = workStatus
                            if (workStatus != "0") {
                                // 当日の予定・実績が登録されている場合、作業内容も取得
                                val contentNameArray: java.util.ArrayList<String> = java.util.ArrayList()
                                val subContentNameArray: java.util.ArrayList<String> = java.util.ArrayList()
                                val workContentsArray = taskRow.getJSONArray("WorkContents")
                                for (j in 0 until workContentsArray.length()) {
                                    contentNameArray.add(workContentsArray.getJSONObject(j).getString("ContentName"))
                                    subContentNameArray.add(workContentsArray.getJSONObject(j).getString("SubContentName"))
                                }
                                task.ContentNames = contentNameArray
                                task.SubContentNames = subContentNameArray
                            }
                            task.AttachFile = taskRow.getString("AttachFile")
                            listTask.add(task)
                        }
                        if (taskIDList.isNotEmpty()) {
                            taskIDList = taskIDList.substring(0, taskIDList.length - 1)
                            basePreference.edit().putString("TASK_ID_LIST", taskIDList).apply()
                        }
                        setTaskListView()
                    } else {
                        // 認証失敗
                        (_activity as BaseActivity).showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(fois.dailyreportsystem.R.string.unexpected_error))
                    log(BaseActivity.context?.javaClass!!.name, ex.toString())
                } finally {
                    hideHub()
                    swipeRefresh.isRefreshing = false
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() { hideHub() }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = userID

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.TasksURL

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        log(BaseActivity.context?.javaClass!!.name, url + jsonParams)
    }

    // リストビューセット
    private fun setTaskListView() {
        if (listTask.size != 0) {
            // 表示できる邸あり
            viewMainList.visibility = View.VISIBLE
            noData.visibility = View.GONE

            itemAdapter = MainAdapter(_context, listTask)
            viewMainList.adapter = itemAdapter
            viewMainList.setSelectionFromTop(listPosition1, listPosition2)

            //Click item event
            viewMainList.onItemClickListener = ClickItem

            // 更新をかけたときにもフィルターをかける
            itemAdapter!!.filter.filter(searchViewTask.query)
        } else {
            // 表示できる邸無し
            viewMainList.visibility = View.INVISIBLE
            noData.visibility = View.VISIBLE
        }
    }

}

