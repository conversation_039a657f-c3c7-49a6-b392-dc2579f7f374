package fois.dailyreportsystem.fragment

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import org.json.JSONObject
import android.widget.TextView
import androidx.viewpager.widget.ViewPager
import java.util.*
import fois.dailyreportsystem.activity.MainActivity
import fois.dailyreportsystem.activity.plan.PlanDetailActivity
import fois.dailyreportsystem.base.BaseFragment
import fois.dailyreportsystem.data.*
import fois.dailyreportsystem.data.adapter.PlanEventListAdapter
import fois.dailyreportsystem.util.*
import fois.dailyreportsystem.util.calendarview.EventDay
import kotlinx.android.synthetic.main.calendar_view.view.*
import kotlinx.android.synthetic.main.plan_list_layout.*
import kotlin.collections.ArrayList


class PlanScheduleFragment : BaseFragment() {
    private lateinit var userID: String

    private var planScheduleList: MutableList<CalendarSchedule> = mutableListOf()

    var calendar: Calendar? = null

    private val events = mutableListOf<EventDay>()
    private var holidayCalendarList: ArrayList<Calender> = arrayListOf<Calender>()
    private var areasList: MutableList<AreaData>? = null

    private lateinit var mHeadDateView: TextView


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        super.onCreateView(inflater, container, savedInstanceState)
        countHub = 0
        return inflater.inflate(R.layout.plan_list_layout, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // 呼び出し側からの値を取得
        val bundle = arguments as Bundle
        userID = bundle.getString("USER_ID", "")

        initCalendar()

    }

    private fun initCalendar() {
        calendar = Calendar.getInstance()

        // 現在日時で再度設定する。
        main_calendar.calendarViewPager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {  }
            override fun onPageSelected(position: Int) { }
            override fun onPageScrollStateChanged(state: Int) {
                when (state) {
                    // 完全に表示され、進行中のアニメーションがない状況
                    ViewPager.SCROLL_STATE_IDLE -> {
                        // 別の月に移動したか
                        val changeCalendar = main_calendar.currentPageDate
                        if (changeCalendar.time.month() != calendar!!.time.month()) {
                            // 移動後の日付情報を更新する
                            calendar!!.set(Calendar.MONTH, changeCalendar.time.month() - 1)
                            main_calendar.setSelectedDay(calendar)

                            // 次の情報を取得
                            getSchedules()
                        }
                    }
                }
            }
        })

        // 日付単位クリック
        main_calendar.setOnDayClickListener { eventDay -> clickCalendarEvent(eventDay.calendar) }

    }
    override fun onResume() {
        super.onResume()

        basePreference.edit().remove("SCHEDULE_ID")

        getSchedules()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        _activity = context as MainActivity
    }

    private fun getYearMonth():String{
        if(calendar == null) return ""
        return calendar!!.time.year().toString() + calendar!!.time.month().toString()
    }

    fun getSchedules(){

        planScheduleList.clear()
        events.clear()

        getCalender()

        // エリアを取得する　初期起動の場合など未保存の場合は、自分の予定を表示する
        areasList = _context.loadAreas()
        if (areasList == null) {
            areasList = mutableListOf()
            areasList!!.add(AreaData.mySchedule())
        }
        // 自分の予定がチェックされていれば取得
        if (areasList!!.find { it.areaID == "0" && it.checked } != null) getUserSchedules()

        // チェックされたエリアの予定を取得
        val findAreasList = areasList!!.filter { it.areaID != "0" && it.checked }.toMutableList()
        if(findAreasList.size > 0) getAreaScheduled(findAreasList)
    }

    fun setHeaderView(headerDateView: TextView) {
        mHeadDateView = headerDateView
        main_calendar.setMonthLabel(mHeadDateView)
        main_calendar.setDate(calendar)
    }

    fun getNowDisplayCalendar(): Calendar? {
        return calendar
    }

    private fun setCalender(){
        if (countHub > 0) return
        if (holidayCalendarList.count() == 0) return
        setHolidayEvent(holidayCalendarList)
        setCalendarEvent(planScheduleList)

        // 取得した日付を自動タップする
        run loop@{

            val event = events.find { it.calendar.time.year() == calendar!!.time.year() && it.calendar.time.month() == calendar!!.time.month() && it.calendar.time.day() == calendar!!.time.day()  }
            if (event != null){
                clickCalendarEvent( event.calendar )
            } else {
                event_list.adapter = PlanEventListAdapter(_context, arrayListOf<CalendarSchedule>(), Date())
            }
        }
    }

    /**
     *
     */
    private fun clickCalendarEvent(cal: Calendar){
        calendar = cal
        val eventList = events.filter {
            it.calendar.time.year() == cal.time.year() && it.calendar.time.month() == cal.time.month() && it.calendar.time.day() == cal.time.day() && it.scheduleData != null
        }.toMutableList()

        if (eventList.count() > 0) {
            // クリックした日のデータを取得する
            val adapterList = PlanEventListAdapter(_context, eventList[0].scheduleData, cal.time)
            event_list.adapter = adapterList
            if (event_list.onItemClickListener == null) {
                // イベント詳細へ
                event_list.onItemClickListener = AdapterView.OnItemClickListener { adapterView, _, i, _ ->
                    event_list.safeClick()

                    val cs = adapterView.getItemAtPosition(i) as CalendarSchedule

                    val intent = Intent(_context, PlanDetailActivity::class.java)

                    basePreference.edit().putString("SCHEDULE_ID", cs.schedule.scheduleID).apply()
                    intent.putExtra("SCHEDULE", cs.schedule)
                    intent.putExtra("SCHEDULE_ID", cs.schedule.scheduleID)
                    startActivityForResult(intent, i)
                    _activity.overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
                }
            }
        } else {
            event_list.adapter = PlanEventListAdapter(_context, arrayListOf<CalendarSchedule>(), cal.time)
        }
    }


    private fun setCalendarEvent(planScheduleList: MutableList<CalendarSchedule>) {
        var planList: MutableMap<Date, MutableList<CalendarSchedule>> = mutableMapOf()

        val distinctSchedule = planScheduleList.distinctBy { it.schedule.scheduleID + it.schedule.startDateTime + it.schedule.endDateTime }

        // 日別に置き換える
        distinctSchedule.forEach {
            if (it.schedule.startDateTime == null) return@forEach
            val startDate = it.schedule.startDateTime!!.toDate() ?: return@forEach
            val endDate = it.schedule.endDateTime!!.toDate() ?: return@forEach
            val startDateTime = startDate.startTime()
            // 配列がなければ初期化してから追加
            if (planList[startDateTime] == null) {
                planList[startDateTime] = arrayListOf<CalendarSchedule>()
            }
            planList[startDateTime]!!.add(it.copy())

            // 日を跨ぐスケジュールの場合
            if (startDate.year() != endDate.year() || startDate.month() != endDate.month() || startDate.day() != endDate.day()){
                val cal = startDate.toCalendar()
                do{
                    cal.add(Calendar.DAY_OF_YEAR, 1)
                    val startTime = cal.time.startTime()
                    // 配列がなければ初期化してから追加
                    if (planList[startTime] == null) {
                        planList[startTime] = arrayListOf<CalendarSchedule>()
                    }
                    planList[startTime]!!.add(it.copy())
                    // 終了日までループ
                } while (cal.time.year() != endDate.year() || cal.time.month() != endDate.month() || cal.time.day() != endDate.day() )
            }
        }

        // 日付ごとにイベントを生成する
        holidayCalendarList.forEach{
            val date = it.Date.toDate("yyyy-MM-dd")!!.startTime()
            val cal = date.toCalendar()
            if(planList[date] != null){
                if(it.BusinessDayID == "4") {
                    events.add(EventDay(cal, "●", Color.RED, planList[date]))
                }else{
                    events.add(EventDay(cal, "●", planList[date]))
                }
            }
            else  if (planList[date] == null && it.BusinessDayID == "4"){
                events.add(EventDay(cal, "", Color.RED, null))
            }
        }

        main_calendar.setEvents(events)

        hideHub()
    }

    private fun setHolidayEvent(holidayList: ArrayList<Calender>){
        if(events.count() > 0){
            main_calendar.setEvents(events)
        }
    }

    @SuppressLint("SimpleDateFormat")
    private fun getUserSchedules() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.connect_error))
                    hideHub()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    // 認証失敗
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val scheduleArrayList = result.getJSONArray("Schedules")

                        for (i in 0 until scheduleArrayList.length()) {
                            val row = scheduleArrayList.getJSONObject(i)
                            val rowData = Schedule.setSchedule(row)
                            val calendarSchedule = CalendarSchedule(schedule = rowData, areaData = areasList?.get(0)!!)
                            planScheduleList.add(calendarSchedule)
                        }

                    } else (_activity as MainActivity).showOKDialog("", status.getString("StatusMsg"))
                } catch (ex: Exception) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.unexpected_error))
                    BaseActivity.log(BaseActivity.context?.javaClass!!.name, ex.toString())
                }
                setCalender()
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() { countHub -= 1 }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = userID
        params.TargetUserID = userID
        params.Year = calendar!!.time.year().toString()
        params.Month = calendar!!.time.month().toString()

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.UserScheduledURL
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        BaseActivity.log(BaseActivity.context?.javaClass!!.name, url + jsonParams)
    }

    private fun getAreaScheduled(areasList: MutableList<AreaData>) {
        countHub += areasList.size
        areasList.forEach {
            val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
                // 実行前
                override fun preExecute() {}

                // 実行後
                override fun postExecute(result: JSONObject?) {
                    countHub -= 1
                    if (result == null) {
                        ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.connect_error))
                        hideHub()
                        return
                    }
                    try {
                        val status = result.getJSONObject("Status")
                        if (status.getInt("StatusCode") == 0) {
                            // 認証成功
                            val scheduleArrayList = result.getJSONArray("Schedules")
                            for (i in 0 until scheduleArrayList.length()) {
                                val row = scheduleArrayList.getJSONObject(i)
                                val rowUserID = row.getString("UserID")
                                // 自身のスケジュールはエリアから除外する。
                                if (rowUserID == userID) continue

                                val rowData = Schedule.setSchedule(row)
                                val calendarSchedule = CalendarSchedule(schedule = rowData, areaData = it)
                                planScheduleList.add(calendarSchedule)
                            }
                        } else {
                            // 認証失敗
                            (_activity as MainActivity).showOKDialog("", status.getString("StatusMsg"))
                        }
                    } catch (ex: Exception) {
                        ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.unexpected_error))
                        BaseActivity.log(BaseActivity.context?.javaClass!!.name, ex.toString())
                    }
                    setCalender()
                }

                // 実行中
                override fun progressUpdate(progress: Int?) {}

                // キャンセル
                override fun cancel() { countHub -= 1 }
            })
            // パラメータ作成
            val params = SendParameter()
            params.UserID = userID
            params.AreaID = it.areaID
            params.Year = calendar!!.time.year().toString()
            params.Month = calendar!!.time.month().toString()
            // JSON形式のパラメータ作成
            val jsonParams = Gson().toJson(params)
            // URL作成
            val url: String = Constant.URL + Constant.AreaScheduledURL
            // 非同期通信開始
            // 第一引数：URL、第二引数：パラメータ(JSON形式)
            asyncJsonLoader.execute(url, jsonParams)
            BaseActivity.log(BaseActivity.context?.javaClass!!.name, url + jsonParams)
        }
    }

    private fun getCalender() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.connect_error))
                    hideHub()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    // 認証失敗
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val calendarArrayList = result.getJSONArray("Calender")
                        holidayCalendarList = Calender.toArrayList(calendarArrayList) ?: arrayListOf()

                    } else (_activity as MainActivity).showOKDialog("", status.getString("StatusMsg"))
                } catch (ex: Exception) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.unexpected_error))
                    BaseActivity.log(BaseActivity.context?.javaClass!!.name, ex.toString())
                }
                setCalender()
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() { countHub -= 1 }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = userID
        params.Year = calendar!!.time.year().toString()
        params.Month = calendar!!.time.month().toString()

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.Clalender
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        BaseActivity.log(BaseActivity.context?.javaClass!!.name, url + jsonParams)
    }
}