package fois.dailyreportsystem.fragment

import android.content.Context
import android.content.Intent
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.os.Bundle
import android.view.*
import android.widget.*
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.activity.*
import fois.dailyreportsystem.activity.request.OrderDetailActivity
import fois.dailyreportsystem.activity.request.OrderRegistrationActivity
import fois.dailyreportsystem.activity.request.PrefActivity
import fois.dailyreportsystem.activity.survey.ReadjustmentListActivity
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.base.BaseFragment
import fois.dailyreportsystem.data.*
import fois.dailyreportsystem.util.*
import fois.dailyreportsystem.util.weekview.*
import org.json.JSONObject
import java.util.*
import kotlin.collections.ArrayList
import kotlin.collections.HashMap


class FreeStatusCalendarFragment : BaseFragment(), MonthLoader.MonthChangeListener {

    private var freeScheduleList: ArrayList<Schedule>? = ArrayList()
    private var researchTaskList: ArrayList<Schedule>? = ArrayList()
    private var receptionTimes: ArrayList<ReceptionTime>? = ArrayList()
    private var holidayCalendarList: ArrayList<Calender> = arrayListOf<Calender>()
    private var offList: HashMap<Date, ArrayList<DatePairData>> = hashMapOf()

    private lateinit var userID: String
    private lateinit var officeID: String

    private var weekView: FreeWeekView? = null

    private var city: City? = null

    var event: WeekViewEvent? = null
    private var monthButton : Button? = null

    // イベントがないと使っているライブラリが落ちるので、生成をしておく
    private val eventList = ArrayList<WeekViewEvent>()

    // Fragmentで表示するViewを作成するメソッド
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        super.onCreateView(inflater, container, savedInstanceState)
        val view = inflater.inflate(R.layout.free_status_calendar_fragment_layout, container, false)

        userID = context!!.settings.getString("USER_ID" )
        officeID = context!!.settings.getString("OFFICE_ID")

        weekView = view.findViewById(R.id.weekView) as FreeWeekView
        weekView!!.isHorizontalFlingEnabled = false
        weekView!!.goToHour(7.0)

        monthButton = view.findViewById(R.id.month) as Button
        monthButton!!.text = Date().toString("M月")

        setupDateTimeInterpreter(true)

        countHub = 0

        return view
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        _activity = context as MainActivity

    }

    override fun onResume() {
        super.onResume()

        weekView!!.monthChangeListener = this

        city = context!!.loadCity()
        if (city != null) {
            getAreaAvailability()
            getResearchTasks()
        }else{
            showNoAreaToast()
        }
        getResearchTaskReceptionTime()
        getCalender()

        // 日付クリックで現在日へ移動
        monthButton!!.setOnClickListener {
            weekView!!.goToToday()
            weekView!!.goToHour(7.0)
        }

        // 空きをロングタップ
        //if (basePreference.getString("COMPANY_ROLE","") == Constant.MAKER) {
            weekView!!.setEmptyViewClickListener { calendar ->
                weekView!!.safeClick()
                if (city == null) {
                    showNoAreaToast()
                }else{
                    if(validation(calendar.time.roundTime())) {
                        forwardRequestAdd(calendar)
                    }
                }
            }
            weekView!!.setEmptyViewLongPressListener { calendar ->
                weekView!!.safeClick()
                if (city == null) {
                    showNoAreaToast()
                }else{
                    if(validation(calendar.time.roundTime())) {
                        forwardRequestAdd(calendar)
                    }
                }
            }
        //}

        // イベントタップ
        weekView!!.setOnEventClickListener { event, _ ->
            weekView!!.safeClick()
            val schedule  = event.schedule
            if (schedule != null) {
                val statusId = try { Integer.parseInt(schedule.surveyStatusID!!) } catch (e: Exception){ 0 }
                if (statusId != 0) {
                    forwardRequestDetail(schedule)
                }
            }
        }

        // 日付変更
        weekView!!.setScrollListener { newFirstVisibleDay, oldFirstVisibleDay ->

            // スクロール制限
            if (newFirstVisibleDay != null) {
                monthButton!!.text = newFirstVisibleDay.time.toString("M月")
                val newCal = newFirstVisibleDay.time.toInt("yyyyMMdd")
                val today = Date().toInt("yyyyMMdd")
                if (newCal <= today){  weekView!!.goToToday() }
            }

            if (oldFirstVisibleDay != null) {
                val oldCal = oldFirstVisibleDay.time.toInt("yyyyMMdd")
                val later = Date().addMonth(3).toInt("yyyyMMdd")
                if (oldCal > later) {
                    val calendar = Calendar.getInstance()
                    calendar.time = Date().addMonth(3)
                    weekView!!.goToDate(calendar)
                }
            }

        }

        val touchIDs: MutableList<Int> = mutableListOf();

        val paint: Paint = Paint()
        paint.color = Color.argb(255, 255, 0, 255)
        paint.strokeWidth = 20f
        paint.style = Paint.Style.STROKE

        val canvas = Canvas()

    }

    private fun validation(startDate: Date): Boolean{
        // 昨日日付チェック
        if (startDate < Date().startTime()) {
            return false
        }

        // 3ヵ月以内チェック 3ヵ月後を超えてればエラー
        val endDate = startDate.addMinutes(120)
        if (endDate > endDate.addMonth(3)) {
            return false
        }
        // 空き状況チェック
        if (freeScheduleList!!.find { schedule ->
                    val startDateTime = schedule.startDateTime!!.toDate() ?: return false
                    val endDatetime = schedule.endDateTime!!.toDate() ?: return false
                    (startDate >= startDateTime && startDate < endDatetime) || (endDate > startDateTime && endDate <= endDatetime)
                } != null) {
            return false
        }

        // 稼働時間が範囲外の場合
        if (receptionTimes!!.isNotEmpty() && receptionTimes!!.find { receptionTime ->
                    val sDate = receptionTime.StartDate.toDate(Constant.FORMAT_DATE_SLASH) ?: return false
                    val eDate = receptionTime.EndDate.toDate(Constant.FORMAT_DATE_SLASH) ?: return false
                    // 開始日 <= [選択] <= 終了日
                    if (sDate.startTime() <= startDate && endDate <= eDate.endTime()) {
                        // 設定値
                        val sTime  = receptionTime.StartTime.toDate("HH:mm") ?: return false
                        val eTime = receptionTime.EndTime.toDate("HH:mm") ?: return false
                        val sMinute = sTime.hour() * 60 + sTime.minute()
                        val eMinute = eTime.hour() * 60 + eTime.minute()
                        // 選択値
                        val startMinute = startDate.hour() * 60 + startDate.minute()
                        val endMinute = endDate.hour() * 60 + endDate.minute()
                        // 8:00 <= [選択] <= 17:00
                        ( sMinute <= startMinute && endMinute <= eMinute)
                    } else {
                        false
                    }
                } == null) {
            return false
        }
        return true
    }


    private fun showNoAreaToast(){
        val ts = Toast.makeText(context, context!!.getString(R.string.no_selected_area), Toast.LENGTH_SHORT)
        ts.setGravity(Gravity.CENTER,0,0)
        ts.show()
    }

    /**
     * Set up a date time interpreter which will show short date values when in week view and long
     * date values otherwise.
     * @param shortDate True if the date values should be short.
     */
    private fun setupDateTimeInterpreter(shortDate: Boolean) {
        weekView!!.dateTimeInterpreter = object : DateTimeInterpreter {
            override fun interpretDate(date: Calendar): String {
                return date.time.toString("d")
            }
            override fun interpretTime(hour: Int): String {
                return "$hour:00"
            }
            override fun interpretDayOfWeek(date: Calendar): String {
                return date.time.toString("EEE")
            }
        }
    }

    /**
     * setFreeCalendar カレンダーイベント登録処理.
     *
     */
    private fun asyncDidReceived() {
        if (countHub > 0) { return }
        hideHub()

        // イベントクリア
        eventList.clear()
        // 空き状況をセット
        if (freeScheduleList != null) {
            freeScheduleList?.forEach {
                eventList.add(setWeekEvent(it))
            }
        }
        // 依頼をセット
        if (researchTaskList != null){
            researchTaskList?.forEach {
                eventList.add(setWeekEvent(it))
            }
        }
        //
        if (holidayCalendarList != null){
            holidayCalendarList?.forEach {
                if(it.BusinessDayID != "1") {
                    val date = it.Date.toDate(Constant.FORMAT_DATE_HYPHEN) ?: return@forEach
                    val start = it.StartTime.toDate() ?: return@forEach
                    val end = it.EndTime.toDate() ?: return@forEach
                    if (offList[date.startTime()] == null) offList[date.startTime()] = arrayListOf()
                    offList[date.startTime()]!!.add(DatePairData(start = start.startTime(), end = end.endTime()))
                }
            }
        }
        //
        if (receptionTimes != null){
            receptionTimes?.forEach {
                val startDate = it.StartDate.toDate(Constant.FORMAT_DATE_SLASH) ?: return@forEach
                val endDate = it.EndDate.toDate(Constant.FORMAT_DATE_SLASH) ?: return@forEach
                val startTime = it.StartTime.toDate("HH:mm") ?: return@forEach
                var startHour = startTime.hour()
                var startMinute = startTime.minute()
                val endTime = it.EndTime.toDate("HH:mm") ?: return@forEach
                var endHour = endTime.hour()
                var endMinute = endTime.minute()

                var loopDate = startDate
                do{
                    // 休日の場合はスキップ
                    if(offList[loopDate.startTime()] == null) {
                        val start = loopDate.setTime(startHour,startMinute)
                        val end = loopDate.setTime(endHour,endMinute)

                        offList[loopDate.startTime()] = arrayListOf()
                        offList[loopDate.startTime()]!!.add(DatePairData(start = start.startTime(), end = start))
                        offList[loopDate.startTime()]!!.add(DatePairData(start = end, end = end.endTime()))
                    }
                    loopDate = loopDate.addDay(1)
                }while (loopDate < endDate)
            }
        }
        if(offList.count() > 0){
            weekView!!.offList = offList
        }

        // カレンダー更新
        weekView!!.notifyDatasetChanged()
    }

    /**
     * setWeekEvent 予定ありのイベントを生成する.
   *
     */
    private fun setWeekEvent(scheduleData: Schedule): WeekViewEvent {

        // weekViewに表示するためイベントを設定する。
        return WeekViewEvent(scheduleData)
    }

    override fun onMonthChange(newYear: Int, newMonth: Int): MutableList<out WeekViewEvent> {

        return eventList
    }

    /**
     * forwardReadjustment
     *
     * */
    fun forwardReadjustment() {
        val intent = Intent(_context, ReadjustmentListActivity::class.java)
        startActivity(intent)
        _activity.overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
    }

    private fun forwardRequestAdd(calendar: Calendar) {
        _context.removeResearchTask()
        val intent = Intent(_context, OrderRegistrationActivity::class.java)
        basePreference.edit().putString("SELECT_REQUEST_DAY", calendar.time.roundTime().toString("yyyy/MM/dd(E) HH:mm")).apply()
        startActivity(intent)
        _activity.overridePendingTransition(R.anim.slide_in_up, R.anim.fade_out)
    }

    private fun forwardRequestDetail(schedule: Schedule) {
        val intent = Intent(_context, OrderDetailActivity::class.java)
        _context.settings.put("RESEARCH_TASK_ID", schedule.researchTaskID)
        _context.settings.put("SCHEDULE_ID", schedule.scheduleID)
        startActivity(intent)
        _activity.overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
    }


    /**
     * forwardPref 都道府県選択画面へ遷移.
     *
     * */
    fun forwardPref() {
        val intent = Intent(_context, PrefActivity::class.java)
        startActivity(intent)
        _activity.overridePendingTransition(R.anim.slide_in_up, R.anim.fade_out)
    }

    /**
     * getSchedulesList 空き状況.
     *
     * */
    private fun getAreaAvailability() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.connect_error))
                    hideHub()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")

                    // 認証成功
                    if (status.getInt("StatusCode") == 0) {
                        val scheduleArrayList = result.getJSONArray("Schedules")
                        freeScheduleList = JsonHandling.toScheduleArrayList(scheduleArrayList)
                        // カレンダーにイベント登録
                    } else {
                        // 認証失敗
                        (_activity as MainActivity).showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(fois.dailyreportsystem.R.string.unexpected_error))
                    BaseActivity.log(BaseActivity.context?.javaClass!!.name, ex.toString())
                }
                asyncDidReceived()
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() {}
        })
        // パラメータ作成
        val params = SendParameter()
        params.UserID = userID
        params.CityCode = city!!.CityCode
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.AreaAvailability
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        BaseActivity.log(BaseActivity.context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * getResearchTasks 調査依頼を取得.
     *
     * */
    private fun getResearchTasks() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.connect_error))
                    hideHub()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")

                    // 認証成功
                    if (status.getInt("StatusCode") == 0) {
                        val scheduleArrayList = result.getJSONArray("Schedules")
                        researchTaskList = JsonHandling.toScheduleArrayList(scheduleArrayList)
                        // カレンダーにイベント登録
                    } else {
                        // 認証失敗
                        (_activity as MainActivity).showOKDialog("", status.getString("StatusMsg"))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(fois.dailyreportsystem.R.string.unexpected_error))
                    BaseActivity.log(BaseActivity.context?.javaClass!!.name, ex.toString())
                }
                asyncDidReceived()
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() {}
        })
        // パラメータ作成
        val params = SendParameter()
        params.UserID = userID
        params.OfficeID = officeID
        params.AreaID = city!!.AreaID
        params.SurveyStatusID = ""
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTasks
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        BaseActivity.log(BaseActivity.context?.javaClass!!.name, url + jsonParams)
    }


    /**
     * getResearchTaskReceptionTime 営業時間を取得.
     *
     * */
    private fun getResearchTaskReceptionTime() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.connect_error))
                    hideHub()
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    // 認証成功
                    if (status.getInt("StatusCode") == 0) {
                        val receptionTimeJsonArray = result.getJSONArray("ReceptionTimes")
                        receptionTimes = ReceptionTime.toReceptionTimes(receptionTimeJsonArray)
                    } else {
                        // 認証失敗
                        ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.unexpected_error))
                    }
                } catch (ex: Exception) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.unexpected_error))
                    BaseActivity.log(BaseActivity.context?.javaClass!!.name, ex.toString())
                }
                asyncDidReceived()
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() {}
        })
        // パラメータ作成
        val params = SendParameter()
        params.UserID = userID
        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.ResearchTaskReceptionTime
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        BaseActivity.log(BaseActivity.context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * getCalender 祝日情報取得.
     *
     * */
    private fun getCalender() {
        countHub += 1
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() { showHub() }
            // 実行後
            override fun postExecute(result: JSONObject?) {
                countHub -= 1
                if (result == null) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.connect_error))
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    // 認証失敗
                    if (status.getInt("StatusCode") == 0) {
                        // 認証成功
                        val calendarArrayList = result.getJSONArray("Calender")
                        holidayCalendarList = Calender.toArrayList(calendarArrayList) ?: arrayListOf()

                    } else (_activity as MainActivity).showOKDialog("", status.getString("StatusMsg"))
                } catch (ex: Exception) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.unexpected_error))
                    BaseActivity.log(BaseActivity.context?.javaClass!!.name, ex.toString())
                }
                asyncDidReceived()
            }
            // 実行中
            override fun progressUpdate(progress: Int?) {}
            // キャンセル
            override fun cancel() { countHub -= 1 }
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = userID
        params.Year = Date().addMonth(1).year().toString()
        params.Month = Date().addMonth(1).month().toString()

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)

        // URL作成
        val url: String = Constant.URL + Constant.Clalender
        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)
        BaseActivity.log(BaseActivity.context?.javaClass!!.name, url + jsonParams)
    }
}