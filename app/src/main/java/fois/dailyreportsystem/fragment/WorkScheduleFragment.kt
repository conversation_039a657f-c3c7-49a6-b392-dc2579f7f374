package fois.dailyreportsystem.fragment

import android.app.DatePickerDialog
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.appcompat.app.AlertDialog
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.activity.MainActivity
import fois.dailyreportsystem.activity.fileviewer.FileViewTypes
import fois.dailyreportsystem.activity.fileviewer.FileViewerActivity
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.base.BaseFragment
import fois.dailyreportsystem.data.Pref
import fois.dailyreportsystem.data.WorkScheduleSearchConditions
import fois.dailyreportsystem.util.*
import org.json.JSONObject
import java.io.File
import java.io.FileOutputStream
import java.util.*
import kotlin.collections.ArrayList

/**
 * 工程表検索用画面
 */
class WorkScheduleFragment : BaseFragment() {
    private val viewDateFormat = "yyyy/MM/dd(E)"
    private val jsonDateFormat = "yyyyMMdd"
    private val workScheduleExtension = ".jpg"

    private var prefList = ArrayList<Pref>()
    private var selectedStart1Date: Calendar? = null
    private var selectedStart2Date: Calendar? = null
    private var selectedComp1Date: Calendar? = null
    private var selectedComp2Date: Calendar? = null

    private lateinit var conditions: WorkScheduleSearchConditions
    private lateinit var userID: String

    private lateinit var mainFragmentLayout: RelativeLayout
    private lateinit var searchButton: TextView
    private lateinit var taskNameInput: EditText
    private lateinit var taskWorkNoInput: EditText
    private lateinit var constructionStart1Date: TextView
    private lateinit var constructionStart1Arrow: ImageView
    private lateinit var constructionStart2Date: TextView
    private lateinit var constructionStart2Arrow: ImageView
    private lateinit var constructionComp1Date: TextView
    private lateinit var constructionComp1Arrow: ImageView
    private lateinit var constructionComp2Date: TextView
    private lateinit var constructionComp2Arrow: ImageView
    private lateinit var notStartedCheckbox: CheckBox
    private lateinit var underConstructionCheckbox: CheckBox
    private lateinit var completionCheckbox: CheckBox
    private lateinit var prefText: TextView
    private lateinit var prefArrow: ImageView
    private lateinit var maker1Input: EditText
    private lateinit var maker2Input: EditText
    private lateinit var fctUserNameInput: EditText
    private lateinit var constructionCompanyNameInput: EditText
    private lateinit var transparentView: View


    override fun onAttach(context: Context) {
        super.onAttach(context)
        this._context = context
        basePreference = context.getSharedPreferences(_context.WORKSCHEDULE, Context.MODE_PRIVATE)
        _activity = context as MainActivity

        conditions = WorkScheduleSearchConditions(_context)
    }

    // Fragmentで表示するViewを作成するメソッド
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        super.onCreateView(inflater, container, savedInstanceState)
        val view = inflater.inflate(R.layout.workschedule_fragment_layout, container, false)

        // 各コントロールの取得
        mapLayoutControl(view)

        val bundle = arguments as Bundle
        userID = bundle.getString("USER_ID", "")

        // クリックイベント
        mapClickEvents()

        // 検索条件の復元
        resumeValues()

        getPrefs()

        return view
    }

    private fun mapLayoutControl(view: View) {
        mainFragmentLayout = view.findViewById(R.id.workschedule_fragment_layout)
        searchButton = view.findViewById(R.id.search_button)
        taskNameInput = view.findViewById(R.id.task_name_input)
        taskWorkNoInput = view.findViewById(R.id.task_work_no_input)
        constructionStart1Date = view.findViewById(R.id.construction_start1_date)
        constructionStart1Arrow = view.findViewById(R.id.construction_start1_arrow)
        constructionStart2Date = view.findViewById(R.id.construction_start2_date)
        constructionStart2Arrow = view.findViewById(R.id.construction_start2_arrow)
        constructionComp1Date = view.findViewById(R.id.construction_comp1_date)
        constructionComp1Arrow = view.findViewById(R.id.construction_comp1_arrow)
        constructionComp2Date = view.findViewById(R.id.construction_comp2_date)
        constructionComp2Arrow = view.findViewById(R.id.construction_comp2_arrow)
        notStartedCheckbox = view.findViewById(R.id.not_started_checkbox)
        underConstructionCheckbox = view.findViewById(R.id.under_construction_checkbox)
        completionCheckbox = view.findViewById(R.id.completion_checkbox)
        prefText = view.findViewById(R.id.pref_text)
        prefArrow = view.findViewById(R.id.pref_arrow)
        maker1Input = view.findViewById(R.id.maker1_input)
        maker2Input = view.findViewById(R.id.maker2_input)
        fctUserNameInput = view.findViewById(R.id.fct_user_name_input)
        constructionCompanyNameInput = view.findViewById(R.id.construction_company_name_input)
        transparentView = view.findViewById(R.id.transparent_view)
    }

    private fun mapClickEvents() {
        transparentView.setOnClickListener { keyboardHide() }
        searchButton.setOnClickListener {
            searchButton.setBackgroundResource(R.drawable.search_button_on)
            showResult()
        }
        listOf(constructionStart1Date, constructionStart1Arrow).setSharedOnSafeClickListener { constructionStart1Click() }
        listOf(constructionStart2Date, constructionStart2Arrow).setSharedOnSafeClickListener { constructionStart2Click() }
        listOf(constructionComp1Date, constructionComp1Arrow).setSharedOnSafeClickListener { constructionComp1Click() }
        listOf(constructionComp2Date, constructionComp2Arrow).setSharedOnSafeClickListener { constructionComp2Click() }
        listOf(prefText, prefArrow).setSharedOnSafeClickListener { showListSelectDialog() }
        listOf(
                taskNameInput, taskWorkNoInput, maker1Input, maker2Input, fctUserNameInput, constructionCompanyNameInput
        ).setSharedOnFocusChangeListener { _, hasFocus ->
            transparentView.visibility = hasFocus.either(View.VISIBLE, View.GONE)
        }
    }

    private fun resumeValues() {
        taskNameInput.setText(conditions.TaskName)
        taskWorkNoInput.setText(conditions.TaskWorkNo)
        selectedStart1Date = convertDateString(conditions.ConstructionStart1)
        selectedStart2Date = convertDateString(conditions.ConstructionStart2)
        selectedComp1Date = convertDateString(conditions.ConstructionComp1)
        selectedComp2Date = convertDateString(conditions.ConstructionComp2)
        constructionStart1Date.text = makeDateString(selectedStart1Date)
        constructionStart2Date.text = makeDateString(selectedStart2Date)
        constructionComp1Date.text = makeDateString(selectedComp1Date)
        constructionComp2Date.text = makeDateString(selectedComp2Date)
        notStartedCheckbox.isChecked = conditions.notStarted
        underConstructionCheckbox.isChecked = conditions.underConstruction
        completionCheckbox.isChecked = conditions.completion
        maker1Input.setText(conditions.MakerName1)
        maker2Input.setText(conditions.MakerName2)
        fctUserNameInput.setText(conditions.FctUserName)
        constructionCompanyNameInput.setText(conditions.ConstructionCompanyName)
    }

    private fun constructionStart1Click() {
        showDatePickerDialog(selectedStart1Date) { calendar ->
            selectedStart1Date = calendar
            constructionStart1Date.text = makeDateString(calendar)
        }
    }

    private fun constructionStart2Click() {
        showDatePickerDialog(selectedStart2Date) { calendar ->
            selectedStart2Date = calendar
            constructionStart2Date.text = makeDateString(calendar)
        }
    }

    private fun constructionComp1Click() {
        showDatePickerDialog(selectedComp1Date) { calendar ->
            selectedComp1Date = calendar
            constructionComp1Date.text = makeDateString(calendar)
        }
    }

    private fun constructionComp2Click() {
        showDatePickerDialog(selectedComp2Date) { calendar ->
            selectedComp2Date = calendar
            constructionComp2Date.text = makeDateString(calendar)
        }
    }

    /**
     * 検索結果表示
     */
    fun showResult() {
        conditions.TaskName = taskNameInput.text.toString()
        conditions.TaskWorkNo = taskWorkNoInput.text.toString()
        conditions.notStarted = notStartedCheckbox.isChecked
        conditions.underConstruction = underConstructionCheckbox.isChecked
        conditions.completion = completionCheckbox.isChecked
        conditions.ConstructionStart1 = makeDateString(selectedStart1Date, jsonDateFormat)
        conditions.ConstructionStart2 = makeDateString(selectedStart2Date, jsonDateFormat)
        conditions.ConstructionComp1 = makeDateString(selectedComp1Date, jsonDateFormat)
        conditions.ConstructionComp2 = makeDateString(selectedComp2Date, jsonDateFormat)
        conditions.MakerName1 = maker1Input.text.toString()
        conditions.MakerName2 = maker2Input.text.toString()
        conditions.FctUserName = fctUserNameInput.text.toString()
        conditions.ConstructionCompanyName = constructionCompanyNameInput.text.toString()

        conditions.update(_context)

        val params = SendParameter()
        val officeID = _context.settings.getString("OFFICE_ID")
        params.UserID = userID
        params.OfficeID = if (officeID == "null") "" else officeID
        params.JsonData = conditions.toJson()


        val baseDir = File(_context.cacheDir, "Files")
        val fileName = "工程表"

        startDownload(params, baseDir, fileName) {
            val intent: Intent = Intent(this._context, FileViewerActivity::class.java).apply {
                putExtra(ConstantParameters.FILE_NAME, fileName)
                putExtra(ConstantParameters.FILE_EXTENSION, workScheduleExtension)
                putExtra(ConstantParameters.VIEW_TYPE, FileViewTypes.WorkSchedule)
                data = Uri.parse(it)
            }
            startActivity(intent)
            _activity.overridePendingTransition(R.anim.activity_open_enter, R.anim.activity_open_exit)
        }
    }

    // 検索中、検索エリア以外タップ
    private fun keyboardHide() {
        val inputMethodManager = _context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        // キーボードを隠す
        inputMethodManager.hideSoftInputFromWindow(mainFragmentLayout.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        // メインにフォーカスを移す
        mainFragmentLayout.requestFocus()
    }

    /* -------------------- ダイアログ、スピナー -------------------- */
    //DatePickerDialogを表示
    private fun showDatePickerDialog(baseDate: Calendar?, result: (Calendar?) -> Unit) {
        val calendar: Calendar = baseDate ?: Calendar.getInstance()

        val datePickerDialog = DatePickerDialog(
                _context,
                DatePickerDialog.OnDateSetListener { _, year, month, dayOfMonth ->
                    val cal: Calendar = Calendar.getInstance()
                    cal.set(year, month, dayOfMonth)
                    result(cal)
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DATE)
        )
        datePickerDialog.setButton(DialogInterface.BUTTON_NEUTRAL, "クリア") { _, _ ->
            result(null)
        }

        datePickerDialog.show()
    }

    private fun makeDateString(calendar: Calendar?, format: String = viewDateFormat): String {
        if (calendar == null) {
            return ""
        }

        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH)
        val day = calendar.get(Calendar.DATE)
        return DateUtil.dateToFormat(year, month + 1, day, 0, 0, format)
    }

    private fun convertDateString(baseString: String?, baseFormat: String = jsonDateFormat): Calendar? {
        val date = baseString?.toDate(baseFormat) ?: return null
        val calendar = Calendar.getInstance()
        calendar.set(date.year(), date.month() - 1, date.day())
        return calendar
    }

    // リスト選択型ダイアログを表示
    private fun showListSelectDialog() {
        val listDialog = AlertDialog.Builder(BaseActivity.context!!, android.R.style.Theme_Holo_Light_Dialog_NoActionBar)
        val prefNames = prefList.map { it.PrefName }.toTypedArray()
        listDialog.setTitle(R.string.workschedule_pref)
        listDialog.setItems(prefNames) { _, which ->
            val selectedPref = prefList[which]
            prefText.text = selectedPref.PrefName
            conditions.PrefID = selectedPref.PrefID
        }

        listDialog.create().show()
    }

    // 都道府県の取得
    private fun getPrefs() {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.connect_error))
                    return
                }

                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") != 0) {
                        // 認証失敗
                        ShowMessages().Show(BaseActivity.context, status.getString("StatusMsg"))
                        return
                    }

                    // 認証成功
                    val prefArray = result.getJSONArray("Prefs")
                    prefList = prefArray.map {
                        Pref(
                                PrefID = it.getString("PrefID"),
                                PrefKana = "",
                                PrefName = it.getString("PrefName"),
                                RegionID = "",
                                RegionName = "",
                                IsHeader = false
                        )
                    } as ArrayList<Pref>
                    prefList.add(0, Pref("", "", "", "", "", false))

                    val target = prefList.firstOrNull { it.PrefID == conditions.PrefID }
                            ?: prefList.first()
                    conditions.PrefID = target.PrefID
                    prefText.text = target.PrefName

                } catch (ex: Exception) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.unexpected_error))
                    BaseActivity.log(BaseActivity.context?.javaClass!!.name, "$ex")
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // パラメータ作成
        val params = SendParameter()
        params.UserID = userID
        params.RegionID = ""

        // RegionIDは空文字で送信し、全量取得対象とする.

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url: String = Constant.URL + Constant.Prefs

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        BaseActivity.log(BaseActivity.context?.javaClass!!.name, url + jsonParams)
    }

    /**
     * ファイルダウンロード処理
     * */
    private fun startDownload(params: SendParameter, baseDir: File, fileName: String, completion: (String) -> Unit) {
        // URL作成
        val url = Constant.URL + Constant.WorkSchedule

        val asyncFileLoader = AsyncFileLoader(_context, object : AsyncFileLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {
                showHub()
            }

            // 実行後
            override fun postExecute(result: ByteArray?) {
                if (result == null) {
                    return
                }
                var outputStream: FileOutputStream? = null

                try {
                    // 保存先のディレクトリが無ければ作成する
                    if (!baseDir.exists()) {
                        baseDir.mkdir()
                    }

                    val fileFullName = fileName + workScheduleExtension
                    val absoluteFilePath = baseDir.absolutePath + "/" + fileFullName

                    outputStream = FileOutputStream(absoluteFilePath)
                    outputStream.write(result)
                    // 読み込みが終ったら、出力ストリームに貯めた出力バイトをファイルへ一気に書き込みます
                    outputStream.flush()

                    completion(absoluteFilePath)
                } catch (ex: Exception) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.unexpected_error))
                    BaseActivity.log(BaseActivity.context?.javaClass!!.name, "$ex")
                } finally {
                    hideHub()
                    outputStream?.close()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
                hideHub()
            }
        }, params.toMap("OfficeID"))

        // 非同期通信開始
        // 第1引数：URL
        asyncFileLoader.execute(url)
    }

}