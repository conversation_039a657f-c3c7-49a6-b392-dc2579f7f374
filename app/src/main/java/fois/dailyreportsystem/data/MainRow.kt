package fois.dailyreportsystem.data

// 作成日：2017/10/23
// 更新日：2018/04/06

import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView

class MainRow {
    var taskInfo: LinearLayout? = null
    var taskIcon: ImageView? = null
    var taskName: TextView? = null
    var areaName: TextView? = null
    var fctUser: TextView? = null
    var taskPlanDate: TextView? = null
    var messageArea: RelativeLayout? = null
    var processArea: RelativeLayout? = null
    var processIcon: ImageView? = null
    var processText: TextView? = null
    var reportArea: RelativeLayout? = null
    var unreadBadge: TextView? = null
    var workContentArea: LinearLayout? = null
    var reportContent: LinearLayout? = null
    var reportSubContent: LinearLayout? = null
    var fileListArea: RelativeLayout? = null
    var workScheduleArea: RelativeLayout? = null
}