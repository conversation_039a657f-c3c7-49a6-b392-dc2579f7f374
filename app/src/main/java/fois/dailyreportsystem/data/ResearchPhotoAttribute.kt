package fois.dailyreportsystem.data

import android.os.Parcelable
import com.google.gson.JsonObject
import kotlinx.android.parcel.Parcelize
import org.json.JSONException
import org.json.JSONObject

@Parcelize
data class ResearchPhotoAttribute(
        var DisplayOrder: String,
        var PhotoDate: String,
        var PhotoID: String,
        var PointOutCategoryID: String,
        var PointOutCategoryName: String,
        var PointOutID: String,
        var PointOutItemID: String,
        var PointOutItemName: String,
        var PointOutName: String,
        var Remarks: String,
        var ResearchTaskID: String,
        var Rotation: String,
        var Selected: String,
        var PointOutSelectedPhotoCount: String,
        var PointOutUnselectedPhotoCount: String,
        var PointOutCategorySelectedPhotoCount: String,
        var PointOutCategoryUnselectedPhotoCount: String,
        var PointOutItemSelectedPhotoCount: String,
        var PointOutItemUnselectedPhotoCount: String

): Parcelable {
    companion object {
        @JvmStatic
        fun init(): ResearchPhotoAttribute {
            return ResearchPhotoAttribute(
                    DisplayOrder = "",
                    PhotoDate = "",
                    PhotoID = "",
                    PointOutCategoryID = "",
                    PointOutCategoryName = "",
                    PointOutID = "",
                    PointOutItemID = "",
                    PointOutItemName = "",
                    PointOutName = "",
                    Remarks = "",
                    ResearchTaskID = "",
                    Rotation = "",
                    Selected = "",
                    PointOutSelectedPhotoCount = "",
                    PointOutUnselectedPhotoCount = "",
                    PointOutCategorySelectedPhotoCount = "",
                    PointOutCategoryUnselectedPhotoCount = "",
                    PointOutItemSelectedPhotoCount = "",
                    PointOutItemUnselectedPhotoCount = ""
            )
        }
        fun init(row: JSONObject): ResearchPhotoAttribute{
            return ResearchPhotoAttribute(
                    DisplayOrder = try {row.getString("DisplayOrder") } catch (e:JSONException) {""},
                    PhotoDate = try {row.getString("PhotoDate") } catch (e:JSONException) {""},
                    PhotoID = try {row.getString("PhotoID") } catch (e:JSONException) {""},
                    PointOutCategoryID = try {row.getString("PointOutCategoryID")  } catch (e:JSONException) {""},
                    PointOutCategoryName = try {row.getString("PointOutCategoryName") } catch (e:JSONException) {""},
                    PointOutID = try {row.getString("PointOutID") } catch (e:JSONException) {""},
                    PointOutItemID = try {row.getString("PointOutItemID") } catch (e:JSONException) {""},
                    PointOutItemName = try {row.getString("PointOutItemName") } catch (e:JSONException) {""},
                    PointOutName = try {row.getString("PointOutName") } catch (e:JSONException) {""},
                    Remarks = try {row.getString("Remarks") } catch (e:JSONException) {""},
                    ResearchTaskID = try {row.getString("ResearchTaskID") } catch (e:JSONException) {""},
                    Rotation = try { row.getString("Rotation")  } catch (e:JSONException) {""},
                    Selected = try {row.getString("Selected")  } catch (e:JSONException) {""},
                    PointOutSelectedPhotoCount = try {row.getString("PointOutSelectedPhotoCount") } catch (e:JSONException) {""},
                    PointOutUnselectedPhotoCount = try {row.getString("PointOutUnselectedPhotoCount")  } catch (e:JSONException) {""},
                    PointOutCategorySelectedPhotoCount = try {row.getString("PointOutCategorySelectedPhotoCount")  } catch (e:JSONException) {""},
                    PointOutCategoryUnselectedPhotoCount =try { row.getString("PointOutCategoryUnselectedPhotoCount")  } catch (e:JSONException) {""},
                    PointOutItemSelectedPhotoCount =try { row.getString("PointOutItemSelectedPhotoCount") } catch (e:JSONException) {""},
                    PointOutItemUnselectedPhotoCount = try {row.getString("PointOutItemUnselectedPhotoCount") } catch (e:JSONException) {""}
            )
        }
    }
}
