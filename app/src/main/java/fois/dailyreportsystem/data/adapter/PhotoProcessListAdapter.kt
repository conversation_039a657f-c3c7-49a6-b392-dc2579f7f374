package fois.dailyreportsystem.data.adapter

// 作成者：フン
// 作成日：2017/08/17
// 更新者：近藤
// 更新日：2017/10/10

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.TextView
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.PhotoProcessList
import fois.dailyreportsystem.data.PhotoProcessListRow

class PhotoProcessListAdapter(private val _context: Context, data: ArrayList<PhotoProcessList>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = java.util.ArrayList<PhotoProcessList>()
    private var originalData = java.util.ArrayList<PhotoProcessList>()

    init {
        this._data = data
        this.originalData = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        var convertView = convertView

        val row: PhotoProcessListRow
        val p = this.getItem(position) as PhotoProcessList

        if (convertView == null) {
            row = PhotoProcessListRow()
            convertView = this._inflater.inflate(R.layout.row_photo_process_list, null)
            row.processName = convertView!!.findViewById(R.id.process_name) as TextView
            row.photoCount = convertView.findViewById(R.id.process_photo_count) as TextView
            convertView.tag = row
        } else {
            row = convertView.tag as PhotoProcessListRow
        }

        row.processName!!.text = p.RegionName
        row.photoCount!!.text = p.photoCount.toString()
        return convertView
    }
}