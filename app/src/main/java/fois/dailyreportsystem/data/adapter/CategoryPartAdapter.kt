package fois.dailyreportsystem.data.adapter

import android.content.Context
import android.content.SharedPreferences
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.PointOut
import fois.dailyreportsystem.data.PointOutItem
import fois.dailyreportsystem.data.ResearchPhotoAttribute
import fois.dailyreportsystem.util.Constant

class CategoryPartAdapter(private val _context: Context, data: ArrayList<PointOut>) : BaseAdapter() {

    private var _inflater: LayoutInflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    private var _data: ArrayList<PointOut> = data

    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? = _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    data class DiagnosisPartView(var categoryNameText: TextView,
                                 var reportText: TextView,
                                 var reserveText: TextView,
                                 var slashText: TextView,
                                 var bg:LinearLayout)

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        var view = convertView
        var rowView: DiagnosisPartView

        if (view == null) {
            view = _inflater!!.inflate(R.layout.row_diagnosis_part, null)
            rowView = DiagnosisPartView(
                    categoryNameText = view.findViewById<TextView>(R.id.category_name),
                    reportText = view.findViewById<TextView>(R.id.sum_report),
                    reserveText = view.findViewById<TextView>(R.id.sum_reserve),
                    slashText = view.findViewById<TextView>(R.id.slash),
                    bg = view.findViewById<LinearLayout>(R.id.bg_photo)
            )

            view.tag = rowView
        } else {
            rowView = view.tag as DiagnosisPartView
        }

        val pointOut = this.getItem(position) as PointOut

        rowView.categoryNameText.text = pointOut.CategoryName

        setVisibility(pointOut, rowView)

        return view!!
    }


    fun reflesh(data: ArrayList<PointOut>) {
        _data = data
        notifyDataSetChanged()
    }

    /**
     * 可視性の変更を行い、枚数をセットする
     */
    fun setVisibility(pointOut: PointOut, view: DiagnosisPartView) {
        view.reportText.visibility = if (pointOut.SelectedPhotoCount == "0" && pointOut.UnselectedPhotoCount == "0") { View.INVISIBLE } else { View.VISIBLE }
        view.reserveText.visibility = if (pointOut.SelectedPhotoCount == "0" && pointOut.UnselectedPhotoCount == "0") { View.INVISIBLE } else { View.VISIBLE }
        view.slashText.visibility = if (pointOut.SelectedPhotoCount == "0" && pointOut.UnselectedPhotoCount == "0") { View.INVISIBLE } else { View.VISIBLE }
        view.bg.visibility = if (pointOut.SelectedPhotoCount == "0" && pointOut.UnselectedPhotoCount == "0") { View.INVISIBLE } else { View.VISIBLE }

        view.reportText.text = pointOut.SelectedPhotoCount
        view.reserveText.text = pointOut.UnselectedPhotoCount
    }
}