package fois.dailyreportsystem.data.adapter

import android.Manifest
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.ImageView
import android.widget.TextView
import androidx.core.app.ActivityCompat
import com.artifex.mupdfdemo.MuPDFActivity
import fois.dailyreportsystem.R
import fois.dailyreportsystem.activity.fileviewer.FileViewTypes
import fois.dailyreportsystem.activity.fileviewer.FileViewerActivity
import fois.dailyreportsystem.activity.fileviewer.PDFViewActivity
import fois.dailyreportsystem.activity.report.FileListActivity
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.AttachFilesInfo
import fois.dailyreportsystem.data.FileListRow
import fois.dailyreportsystem.util.*
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.*
import kotlin.collections.ArrayList
import kotlin.collections.set

/**
 * ファイル一覧のアダプタ
 */
class FileListAdapter(private val _context: Context, taskName: String, files: List<AttachFilesInfo>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _files: List<AttachFilesInfo> = ArrayList()
    private var _taskName = ""
    private var _userID = ""

    private var imageMap: HashMap<String, ArrayList<ImageView>> = HashMap()
    private var iconCacheDir: File? = null
    private var fileList: Array<File>

    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? = _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)

    init {
        this._files = files
        this._taskName = taskName
        this._userID = _context.settings.getString("USER_ID")
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        // キャッシュ用 ディレクトリ、ファイルリストの取得
        iconCacheDir = File(_context.cacheDir, "Icons")
        if (!iconCacheDir!!.exists()) {
            iconCacheDir!!.mkdir()
        }

        fileList = iconCacheDir!!.listFiles()

        // ファイルの存在確認と指定ファイルの読み込み
        checkAndGetIconImage()
    }

    // ファイルの存在確認と指定ファイルの読み込み
    private fun checkAndGetIconImage() {
        // 同一アイコンのまとめと存在確認フィルタ
        _files.groupBy { it.attachFileIconID }
                .map { it.value.first() }
                .filter {
                    val targetFile = File(iconCacheDir, it.attachFileIconID)
                    return@filter !targetFile.exists()
                }
                .forEach { getImageAsync(it) }
    }

    /**
     * 要素数取得
     */
    override fun getCount(): Int {
        return this._files.size
    }

    /**
     * アイテム取得
     */
    override fun getItem(position: Int): Any {
        return this._files[position]
    }

    /**
     * ID取得
     */
    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    /**
     * View取得
     */
    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        var convertView = convertView

        val info = this.getItem(position) as AttachFilesInfo

        convertView = this._inflater.inflate(R.layout.row_file_list, null)
        val row = FileListRow()
        row.extensionImage = convertView!!.findViewById(R.id.extension_image) as ImageView
        row.fileName = convertView.findViewById(R.id.file_name) as TextView
        row.fileName!!.text = info.fileName
        row.downloadImage = convertView.findViewById(R.id.download_image) as ImageView
        row.downloadImage!!.visibility = info.isInternalBrowsing().either(View.GONE, View.VISIBLE)

        listOf(row.extensionImage!!, row.fileName!!, row.downloadImage!!).setSharedOnSafeClickListener { showItem(position) }

        // マップに場所とビューを格納
        if (imageMap[info.attachFileIconID] == null) {
            imageMap[info.attachFileIconID] = ArrayList()
        }

        imageMap[info.attachFileIconID]?.add(row.extensionImage!!)

        val targetFile = File(iconCacheDir, info.attachFileIconID)

        // ファイルが存在する場合
        if (targetFile.exists()) {
            val fileInputStream = FileInputStream(targetFile)
            val bitmap = BitmapFactory.decodeStream(fileInputStream)
            row.extensionImage!!.setImageBitmap(bitmap)
        }

        return convertView
    }

    private fun showItem(position: Int) {
        // タップ済み確認
        if ((_context as FileListActivity).clickPosition != null) {
            return
        }

        // タップ位置を保存
        _context.clickPosition = true
        val target = getItem(position) as AttachFilesInfo
        val isInternalBrowsing = target.isInternalBrowsing()

        val baseDir = isInternalBrowsing.either(File(_context.cacheDir, "Files"),
                File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path))
        val fileName = _taskName + "-" + target.fileName
        if (checkPermission(_context, isInternalBrowsing)) {
            startDownload(target, baseDir, fileName) {
                if (isInternalBrowsing) {
                    sendFileViewer(it, target, fileName)
                } else {
                    // 完了表示
                    ShowMessages().Show(_context, "ファイルをダウンロードしました。\n$it")
                }
            }
        }
    }

    private fun checkPermission(activity: FileListActivity, internalBrowsing: Boolean): Boolean {
        if (internalBrowsing) {
            return true
        }

        return if (ActivityCompat.checkSelfPermission(activity, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {
            true
        } else {
            ActivityCompat.requestPermissions(activity, arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE), Constant.PERMISSION_REQUEST_STORAGE)
            false
        }
    }

    private fun sendFileViewer(filePath: String, target: AttachFilesInfo, fileName: String) {
        val uri = Uri.parse(filePath)
        val intent: Intent
        when {
            target.fileExtension.toLowerCase(Locale.ROOT) != ".pdf" -> {
                intent = Intent(_context, FileViewerActivity::class.java)
            }
            // Androidバージョンによって読み込み先切替
            Build.VERSION.SDK_INT < 21 -> {
                // Android 4.4以前
                intent = Intent(_context, MuPDFActivity::class.java)
                intent.action = Intent.ACTION_VIEW
            }
            else -> {
                // Android 5以降
                intent = Intent(_context, PDFViewActivity::class.java)
            }
        }

        intent.putExtra(ConstantParameters.FILE_NAME, fileName)
        intent.putExtra(ConstantParameters.FILE_EXTENSION, target.fileExtension)
        intent.putExtra(ConstantParameters.VIEW_TYPE, FileViewTypes.File)
        intent.data = uri
        try {
            _context.startActivity(intent)
            (_context as FileListActivity).overridePendingTransition(R.anim.activity_open_enter, R.anim.activity_open_exit)
        } catch (e: ActivityNotFoundException) {
            e.printStackTrace()
        }

    }

    // 邸アイコン取得
    private fun getImageAsync(attachFileInfo: AttachFilesInfo) {
        val file = File(iconCacheDir, attachFileInfo.attachFileIconID)

        val asyncBitmapLoader = AsyncBitmapLoader(object : AsyncBitmapLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: Bitmap?) {
                if (result == null) {
                    return
                }
                try {
                    // 画像をキャッシュに保存
                    val fileOutputStream = FileOutputStream(file)
                    result.compress(Bitmap.CompressFormat.PNG, 100, fileOutputStream)
                    // ファイルリスト更新
                    fileList = (iconCacheDir ?: return).listFiles()
                    // 取得した画像をセット
                    setImage(attachFileInfo.attachFileIconID, result)
                } catch (ex: Exception) {
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // 非同期通信開始
        asyncBitmapLoader.execute(attachFileInfo.attachFileIconUrl)
    }

    private fun setImage(attachFileIconID: String, bitmap: Bitmap) {
        (imageMap[attachFileIconID] ?: return).forEach {
            it.setImageBitmap(bitmap)
        }
    }

    /**
     * ファイルダウンロード処理
     * */
    private fun startDownload(attachFileInfo: AttachFilesInfo, baseDir: File, fileName: String, completion: (String) -> Unit) {
        // URL作成
        val url = Constant.URL + Constant.AttachFile
        val view = _context as? FileListActivity
        val params = SendParameter()
        params.UserID = _userID
        params.AttachFileID = attachFileInfo.attachFileID

        val asyncFileLoader = AsyncFileLoader(_context, object : AsyncFileLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {
                view?.showHub()
            }

            // 実行後
            override fun postExecute(result: ByteArray?) {
                if (result == null) {
                    return
                }
                var outputStream: FileOutputStream? = null

                try {
                    // 保存先のディレクトリが無ければ作成する
                    if (!baseDir.exists()) {
                        baseDir.mkdir()
                    }

                    val absoluteFilePath = FileUtil.getUniqueFilePath(baseDir, fileName, attachFileInfo.fileExtension)
                    outputStream = FileOutputStream(absoluteFilePath)
                    outputStream.write(result)
                    // 読み込みが終ったら、出力ストリームに貯めた出力バイトをファイルへ一気に書き込みます
                    outputStream.flush()

                    completion(absoluteFilePath)
                } catch (ex: Exception) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.unexpected_error))
                    BaseActivity.log(BaseActivity.context?.javaClass!!.name, "$ex")
                } finally {
                    view?.hideHub()
                    view?.clickPosition = null
                    outputStream?.close()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
                view?.hideHub()
                view?.clickPosition = null
            }
        }, params.toMap())

        // 非同期通信開始
        // 第1引数：URL
        asyncFileLoader.execute(url)
    }
}
