package fois.dailyreportsystem.data.adapter

// 作成日：2017/08/22
// 更新日：2018/02/16

import android.animation.ObjectAnimator
import android.content.Context
import android.content.SharedPreferences
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.TextView
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.MessageList
import fois.dailyreportsystem.data.MessageListRow
import java.util.ArrayList

class MessageListAdapter(private val _context: Context, data: ArrayList<MessageList>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = ArrayList<MessageList>()
    private var originalData = ArrayList<MessageList>()
    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? =  _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)
    // ポジション定義
    private var lastPosition: Int = -1

    init {
        this._data = data
        this.originalData = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        var convertView = convertView

        val row: MessageListRow = MessageListRow()
        val p = this.getItem(position) as MessageList

        when (p.layoutType) {
            0 -> {
                // 日付表示
                convertView = this._inflater.inflate(R.layout.row_message_list_date, null)
                row.messageDate = convertView!!.findViewById(R.id.message_date) as TextView
                row.messageDate!!.text = p.MessageDate
            }
            1 -> {
                // 自分のメッセージ
                convertView = this._inflater.inflate(R.layout.row_message_list_mine, null)
                row.messageText = convertView!!.findViewById(R.id.message_text) as TextView
                row.messageTime = convertView.findViewById(R.id.message_send_time) as TextView
                row.messageText!!.text = p.MessageText
                row.messageTime!!.text = p.MessageTime
            }
            2,3 -> {
                // 相手のメッセージ
                convertView = this._inflater.inflate(R.layout.row_message_list, null)
                row.messageSender = convertView!!.findViewById(R.id.message_sender) as TextView
                row.messageText = convertView.findViewById(R.id.message_text) as TextView
                row.messageTime = convertView.findViewById(R.id.message_send_time) as TextView
                if (p.layoutType == 2) {
                    // 名前表示
                    row.messageSender!!.visibility = View.VISIBLE
                    row.messageSender!!.text = p.CompanyName + "/" + p.UserName
                } else {
                    // 名前非表示
                    row.messageSender!!.visibility = View.GONE
                }
                row.messageText!!.text = p.MessageText
                row.messageTime!!.text = p.MessageTime
            }
        }

        if (position > lastPosition) {
            val animation: ObjectAnimator = ObjectAnimator.ofFloat(convertView, "alpha", 0f, 1f)
            animation.duration = 1000
            animation.start()
            lastPosition = position
        }

        return convertView!!
    }
}
