package fois.dailyreportsystem.data.adapter

// 作成日：2017/08/17
// 更新日：2018/04/06

import android.content.Context
import android.content.SharedPreferences
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.City
import kotlin.collections.ArrayList

class CityAdapter(private val _context: Context, data: ArrayList<City>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = ArrayList<City>()

    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? = _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)

    init {
        this._data = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        var convertView = convertView

        val cityData = this.getItem(position) as City

        convertView = this._inflater.inflate(R.layout.row_city, null)

        var cityName = convertView!!.findViewById(R.id.city_name) as TextView

        var cityNameKana = convertView!!.findViewById(R.id.city_name_kana) as TextView

        cityName.text = cityData.CityName

        cityNameKana.text = cityData.CityKana

        return convertView
    }

}
