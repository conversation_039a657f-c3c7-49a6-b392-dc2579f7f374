package fois.dailyreportsystem.data.adapter

// 作成日：2019/09/23

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.Schedule
import fois.dailyreportsystem.util.DateUtil
import kotlin.collections.ArrayList

class ReadjustmentListAdapter(private val _context: Context, data: ArrayList<Schedule>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = ArrayList<Schedule>()

    private var dateUtil = DateUtil()

    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? = _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)


    data class RowView(
            var date: TextView,
            var statusBar: Button,
            var taskName: TextView,
            var address: TextView,
            var orderStatus: TextView)

    init {
        this._data = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    @SuppressLint("SetTextI18n")
    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        var convertView = convertView

        val schedule = this.getItem(position) as Schedule


        convertView = this._inflater.inflate(R.layout.row_readjustment_list, null)

        var taskData = schedule!!.researchTask

        var areaName = convertView!!.findViewById(R.id.area_name) as TextView
        var address = convertView!!.findViewById(R.id.address) as TextView
        var companyName = convertView!!.findViewById(R.id.company_name) as TextView
        var taskName = convertView!!.findViewById(R.id.task_name) as TextView

        areaName.text = taskData!!.PrefName
        address.text = taskData.CityName + taskData.TaskAddress
        companyName.text = taskData!!.MakerName
        taskName.text = taskData!!.TaskName + " " + _context.resources.getString(R.string.honorific)

        return convertView
    }

}
