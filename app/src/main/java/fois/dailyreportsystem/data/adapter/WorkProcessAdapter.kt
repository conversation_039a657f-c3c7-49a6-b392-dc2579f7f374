package fois.dailyreportsystem.data.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.CheckBox
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.Calender
import fois.dailyreportsystem.data.RegistrationInfo
import fois.dailyreportsystem.data.Report
import fois.dailyreportsystem.data.ReportRow
import fois.dailyreportsystem.data.WorkProcess
import fois.dailyreportsystem.data.WorkProcessRow
import fois.dailyreportsystem.util.DateUtil
import fois.dailyreportsystem.util.setOnSafeClickListener
import kotlinx.android.synthetic.main.report_schedule1_layout.report_work_stop_area
import kotlinx.android.synthetic.main.report_schedule1_layout.report_work_stop_checkbox
import java.text.SimpleDateFormat
import java.util.Calendar

class WorkProcessAdapter(private val _context: Context, data: ArrayList<WorkProcess>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = java.util.ArrayList<WorkProcess>()
    private var originalData = java.util.ArrayList<WorkProcess>()

    init {
        this._data = data
        this.originalData = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val registrationInfo: RegistrationInfo = RegistrationInfo()

        var convertView = convertView

        val row: WorkProcessRow
        val p = this.getItem(position) as WorkProcess

        if (convertView == null) {
            row = WorkProcessRow()
            convertView = this._inflater.inflate(R.layout.row_work_process, null)
            row.workProcessArea = convertView.findViewById(R.id.work_process_area) as LinearLayout
            row.processFullName = convertView!!.findViewById(R.id.work_process_content) as TextView
            row.selected = convertView.findViewById(R.id.work_process_checkbox) as CheckBox

            convertView.tag = row
        } else {
            row = convertView.tag as WorkProcessRow
        }

        row.processFullName?.text = p.ProcessName + " " + p.ProcessSubName
        row.selected?.isChecked = p.Selected == true

        return convertView
    }
}