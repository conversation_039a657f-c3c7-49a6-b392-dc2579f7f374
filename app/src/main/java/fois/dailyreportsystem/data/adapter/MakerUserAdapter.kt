package fois.dailyreportsystem.data.adapter
import android.content.Context
import android.content.SharedPreferences
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.ImageView
import android.widget.TextView
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.ResearchTaskUser
import fois.dailyreportsystem.util.loadResearchTask
import java.util.*

class MakerUserAdapter(private val _context: Context, data: ArrayList<ResearchTaskUser>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = ArrayList<ResearchTaskUser>()

    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? = _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)

    init {
        this._data = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    }

    data class RowView(  var userName: TextView, var companyName: TextView, var checkImage: ImageView)

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        var convertView = convertView

        val userData = this.getItem(position) as ResearchTaskUser

        convertView = this._inflater.inflate(R.layout.row_maker_user, null)

        val rowView = RowView(
                userName = convertView.findViewById(R.id.user_name),
                companyName = convertView.findViewById(R.id.company_name),
                checkImage = convertView.findViewById(R.id.check_image))


        // 一時保存データがあれば処理
        val researchTask = _context.loadResearchTask() ?: return convertView
        if ( researchTask.MakerUserID == userData.UserID){
            rowView.checkImage.visibility = View.VISIBLE
        }else{
            rowView.checkImage.visibility = View.GONE
        }

        rowView.userName.text = userData.UserName
        rowView.companyName.text = userData.CompanyName

        return convertView
    }

}