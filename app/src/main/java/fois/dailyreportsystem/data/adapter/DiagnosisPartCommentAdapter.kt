package fois.dailyreportsystem.data.adapter

import android.content.Context
import android.content.SharedPreferences
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.core.content.ContextCompat
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.PointOutItem
import fois.dailyreportsystem.util.Constant
import fois.dailyreportsystem.util.safeClick
import fois.dailyreportsystem.util.setDrawable

class DiagnosisPartCommentAdapter(private val _context: Context, data: List<PointOutItem>, map: MutableMap<String, (PointOutItem, String) -> Unit>) : BaseAdapter() {
    private var _inflater: LayoutInflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    private var _data: List<PointOutItem> = data

    private lateinit var _viewData: List<PointOutItem>

    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? = _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)

    var shutterAction: (data: PointOutItem, select: String) -> Unit by map

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    fun reflesh(data: List<PointOutItem>){
        _data = data
        notifyDataSetChanged()
    }

    data class DiagnosisPartView(var diagnosiscommentText: TextView,
                                 var forSpareShutter: LinearLayout,
                                 var forReportShutter: LinearLayout,
                                 var reportText: TextView,
                                 var reserveText: TextView,
                                 var reportBG: LinearLayout,
                                 var reserveBG: LinearLayout)

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {

        var view = convertView
        var rowView: DiagnosisPartView

        if (view == null) {
            view = _inflater!!.inflate(R.layout.row_diagnosis_part_comment, null)
            rowView = DiagnosisPartView(
                    diagnosiscommentText = view.findViewById<TextView>(R.id.diagnosis_part_comment_text),
                    forSpareShutter = view.findViewById<LinearLayout>(R.id.for_spare_shutter),
                    forReportShutter = view.findViewById<LinearLayout>(R.id.for_report_shutter),
                    reportText = view.findViewById<TextView>(R.id.text_report_item),
                    reserveText = view.findViewById<TextView>(R.id.text_reserve_item),
                    reportBG = view.findViewById<LinearLayout>(R.id.bg_report),
                    reserveBG = view.findViewById<LinearLayout>(R.id.bg_reserve)
            )

            view.tag = rowView
        }else{
            rowView = view.tag as DiagnosisPartView
        }

        val diagnosis = this.getItem(position) as PointOutItem

        rowView.diagnosiscommentText.text = diagnosis.ItemName
        // 枚数をテキストにセット
        rowView.reportText.text = diagnosis.SelectedPhotoCount

        rowView.reserveText.text = diagnosis.UnselectedPhotoCount

        rowView.forSpareShutter.setOnClickListener {
            it.safeClick()
            // 連打防止、撮影終了時に再撮影可能
            shutterAction(diagnosis, Constant.ResearchTaskPhotoType.SPARE.SELECTED)
        }
        rowView.forReportShutter.setOnClickListener {
            it.safeClick()
            // 連打防止、撮影終了時に再撮影可能
            shutterAction(diagnosis, Constant.ResearchTaskPhotoType.REPORT.SELECTED)
        }

        // 可視性をセット
        setVisibility(diagnosis, rowView)

        return view!!
    }

    /**
     * 可視性の変更を行う
     */
    fun setVisibility(diagnosis: PointOutItem, view: DiagnosisPartView){
        // 枚数が０枚なら非表示
        view.reportText.visibility  = if (diagnosis.SelectedPhotoCount == "0") { View.INVISIBLE } else { View.VISIBLE }
        view.reportBG.visibility    = if (diagnosis.SelectedPhotoCount == "0") { View.INVISIBLE } else { View.VISIBLE }
        view.reserveText.visibility = if (diagnosis.UnselectedPhotoCount == "0") { View.INVISIBLE } else { View.VISIBLE }
        view.reserveBG.visibility   = if (diagnosis.UnselectedPhotoCount == "0") { View.INVISIBLE } else { View.VISIBLE }

    }
}