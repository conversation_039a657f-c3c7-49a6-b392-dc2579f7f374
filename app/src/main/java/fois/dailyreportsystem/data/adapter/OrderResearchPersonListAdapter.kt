package fois.dailyreportsystem.data.adapter

import android.content.Context
import android.content.SharedPreferences
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.ResearchTaskUser
import fois.dailyreportsystem.data.Schedule
import fois.dailyreportsystem.util.toDate
import fois.dailyreportsystem.util.toString
import java.util.*

class OrderResearchPersonListAdapter(private val _context: Context, data: ArrayList<ResearchTaskUser>, schedule : Schedule) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = ArrayList<ResearchTaskUser>()
    private var _schedule : Schedule

    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? = _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)

    init {
        this._data = data
        this._schedule = schedule
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    }

    data class RowView( var checkImage: ImageView, var userName: TextView, var requestDate: TextView)

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        var convertView = convertView

        val userData = this.getItem(position) as ResearchTaskUser

        convertView = this._inflater.inflate(R.layout.row_research_person, null)

        val rowView = RowView(
                checkImage = convertView.findViewById(R.id.check_image),
                userName = convertView.findViewById(R.id.search_person_name),
                requestDate = convertView.findViewById(R.id.request_date))

        if (userData.UserID == _schedule.userID) {
            rowView.checkImage.setImageResource(R.drawable.circle_blue_check)
            rowView.userName.setTextColor(ContextCompat.getColor(_context, R.color.light_blue))
        }else{
            rowView.checkImage.setImageDrawable(null)
            rowView.userName.setTextColor(ContextCompat.getColor(_context, R.color.black))
        }

        rowView.userName.text = userData.UserName

        if (userData.RequestDate != "") {
            rowView.requestDate.text = userData.RequestDate.toDate()!!.toString("MM/dd(E) HH:mm")
        } else {
            rowView.requestDate.visibility = View.GONE
        }

        return convertView
    }

}