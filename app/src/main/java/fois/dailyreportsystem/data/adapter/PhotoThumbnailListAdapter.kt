package fois.dailyreportsystem.data.adapter

// 作成日：
// 更新日：2018/04/11

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.ImageView
import android.widget.TextView
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.PhotoThumbnailList
import fois.dailyreportsystem.data.PhotoThumbnailListRow
import fois.dailyreportsystem.util.AsyncBitmapLoader
import java.io.*

class PhotoThumbnailListAdapter(private val _context: Context, data: ArrayList<PhotoThumbnailList>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = java.util.ArrayList<PhotoThumbnailList>()
    private var originalData = java.util.ArrayList<PhotoThumbnailList>()

    private var imageMap: HashMap<Int, ImageView> = HashMap()
    private var photoMap: HashMap<Int, Bitmap> = HashMap()
    private var dir: File? = null
    private var fileList: Array<File>

    init {
        this._data = data
        this.originalData = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater

        // キャッシュ用 ディレクトリ、ファイルリストの取得
        dir = _context.cacheDir
        fileList = dir!!.listFiles()
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        var convertView = convertView

        val row: PhotoThumbnailListRow = PhotoThumbnailListRow()
        val p = this.getItem(position) as PhotoThumbnailList

        when (p.type) {
            0 -> {
                // サブタイトル
                convertView = this._inflater.inflate(R.layout.row_sub_title, null)
                row.contentName = convertView!!.findViewById(R.id.sub_title) as TextView
                row.contentName!!.text = p.ContentName
            }
            1 -> {
                // サムネイル
                convertView = this._inflater.inflate(R.layout.row_photo_thumbnail_list, null)

                // ビューの読み込み
                row.thumbnailImage = convertView!!.findViewById(R.id.photo_thumbnail_image) as ImageView
                row.takeDate = convertView.findViewById(R.id.photo_thumbnail_date) as TextView
                row.takeTime = convertView.findViewById(R.id.photo_thumbnail_time) as TextView
                row.workerName = convertView.findViewById(R.id.photo_thumbnail_worker_name) as TextView

                // マップに場所とビューを格納
                imageMap.put(position, row.thumbnailImage!!)

                if (photoMap[position] == null) {
                    // イメージの取得
                    if (fileList.isNotEmpty()) {
                        for (i in 0..fileList.size - 1) {
                            if (fileList[i].toString().substring(fileList[i].toString().lastIndexOf("/") + 1) == p.CacheFileName) {
                                // キャッシュに画像が保存されている場合
                                try {
                                    val fileInputStream: FileInputStream = FileInputStream(fileList[i])
                                    val bitmap: Bitmap = BitmapFactory.decodeStream(fileInputStream)
                                    photoMap[position] = bitmap
                                    SetImage(position, bitmap)
                                } catch (e: FileNotFoundException) {
                                    e.printStackTrace()
                                }
                                break
                            }
                            if (i == fileList.size - 1) {
                                // キャッシュに画像が保存されていない場合、通信して取得
                                val file: File = File(_context.cacheDir, p.CacheFileName)
                                GetImageAsync(p.SmallPhotoUrl!!, position, file)
                            }
                        }
                    } else {
                        // キャッシュに画像が保存されていない場合、通信して取得
                        val file: File = File(_context.cacheDir, p.CacheFileName)
                        GetImageAsync(p.SmallPhotoUrl!!, position, file)
                    }
                } else {
                    SetImage(position, photoMap[position]!!)
                }

                // 各種値を表示
                row.takeDate!!.text = p.PhotoDate
                row.takeTime!!.text = p.PhotoTime
                row.workerName!!.text = p.WorkerName
            }
            2 -> {
                // 写真追加
                convertView = this._inflater.inflate(R.layout.row_photo_thumbnail_add, null)
            }
        }

        return convertView!!
    }

    private fun SetImage(position: Int, bitmap: Bitmap) {
        imageMap[position]!!.setImageBitmap(bitmap)
    }

    private fun GetImageAsync(url: String, position: Int, file: File) {
        val asyncBitmapLoader = AsyncBitmapLoader(object : AsyncBitmapLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: Bitmap?) {
                if (result == null) {
                    return
                }
                try {
                    // 画像をキャッシュに保存
                    val fileOutputStream: FileOutputStream = FileOutputStream(file)
                    result.compress(Bitmap.CompressFormat.PNG, 100, fileOutputStream)
                    // ファイルリスト更新
                    fileList = dir!!.listFiles()
                    photoMap[position] = result
                    // 取得した画像をセット
                    SetImage(position, result)
                } catch (ex: Exception) {}
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // 非同期通信開始
        asyncBitmapLoader.execute(url)
    }
}