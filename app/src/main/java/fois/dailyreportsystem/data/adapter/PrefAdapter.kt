package fois.dailyreportsystem.data.adapter

// 作成日：2017/08/17
// 更新日：2018/04/06

import android.content.Context
import android.content.SharedPreferences
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.Pref
import kotlin.collections.ArrayList

class PrefAdapter(private val _context: Context, data: ArrayList<Pref>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = ArrayList<Pref>()

    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? = _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)

    init {
        this._data = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        var convertView = convertView

        val prefData = this.getItem(position) as Pref

        if (prefData.IsHeader) {
            convertView = this._inflater.inflate(R.layout.row_region, null)

            var regionName = convertView!!.findViewById(R.id.region_name) as TextView

            regionName.text = prefData.RegionName
        } else {

            convertView = this._inflater.inflate(R.layout.row_pref, null)

            var prefName = convertView!!.findViewById(R.id.pref_name) as TextView

            prefName.text = prefData.PrefName
        }

        return convertView
    }

}
