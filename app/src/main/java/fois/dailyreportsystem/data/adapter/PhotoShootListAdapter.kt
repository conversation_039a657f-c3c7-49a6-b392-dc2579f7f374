package fois.dailyreportsystem.data.adapter

// 作成日：2017/08/24
// 更新日：2018/02/02

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.TextView
import fois.dailyreportsystem.R
import android.content.SharedPreferences
import android.widget.FrameLayout
import android.widget.ListView
import fois.dailyreportsystem.data.PhotoShootList
import fois.dailyreportsystem.data.PhotoShootListRow

class PhotoShootListAdapter(private val _context: Context, data: ArrayList<PhotoShootList>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = java.util.ArrayList<PhotoShootList>()
    private var originalData = java.util.ArrayList<PhotoShootList>()

    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? =  _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)

    init {
        this._data = data
        this.originalData = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        var convertView = convertView

        val row: PhotoShootListRow
        val p = this.getItem(position) as PhotoShootList

        if (convertView == null) {
            row = PhotoShootListRow()
            convertView = this._inflater.inflate(R.layout.row_photo_shoot_list, null)
            row.contentName = convertView!!.findViewById(R.id.photo_shoot_content_name) as TextView
            row.photoCount = convertView.findViewById(R.id.photo_shoot_count) as TextView
            row.cameraButton = convertView.findViewById(R.id.photo_shoot_camera_button) as FrameLayout
            row.cameraButton!!.setOnClickListener { view ->
                (parent as ListView).performItemClick(view, position, R.id.photo_shoot_camera.toLong())
            }
            convertView.tag = row
        } else {
            row = convertView.tag as PhotoShootListRow
        }

        row.contentName!!.text = p.ContentName
        row.photoCount!!.text = p.photoCount.toString() + _context.getString(R.string.photo_count)
        return convertView
    }
}