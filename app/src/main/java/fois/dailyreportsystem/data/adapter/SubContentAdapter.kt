package fois.dailyreportsystem.data.adapter

// 作成日：2017/10/12
// 更新日：2017/12/21

import android.content.Context
import android.content.DialogInterface
import androidx.appcompat.app.AlertDialog
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.SubContent
import fois.dailyreportsystem.data.SubContentRow

class SubContentAdapter(private val _context: Context, data: ArrayList<SubContent>) : BaseAdapter() {

    private var _inflater: LayoutInflater
    private var _data = java.util.ArrayList<SubContent>()
    private var originalData = java.util.ArrayList<SubContent>()
    private val usedAmountList = _context.resources.getStringArray(R.array.used_count_list)

    init {
        this._data = data
        this.originalData = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        var convertView = convertView

        val row: SubContentRow = SubContentRow()
        val p = this.getItem(position) as SubContent

        when (p.type) {
            1 -> {
                // 工程名
                convertView = this._inflater.inflate(R.layout.row_report_sub_content_title, null)
                row.subContentTitle = convertView!!.findViewById(R.id.report_sub_content_title) as TextView
                row.subContentTitle!!.text = p.ContentName
            }
            2 -> {
                // 作業項目名
                convertView = this._inflater.inflate(R.layout.row_report_sub_content, null)
                row.subContentArea = convertView!!.findViewById(R.id.report_sub_content_area) as LinearLayout
                row.subContentName = convertView.findViewById(R.id.report_sub_content_name) as TextView
                row.subContentCheckbox = convertView.findViewById(R.id.report_sub_content_check) as CheckBox
                row.usedAmount = convertView.findViewById(R.id.report_used_amount) as LinearLayout
                if (p.usedFlag == 1) {
                    row.usedAmount!!.visibility = View.GONE
                } else {
                    row.usedLargeArea = convertView.findViewById(R.id.report_used_large_area) as LinearLayout
                    row.usedLargeSelect = convertView.findViewById(R.id.report_used_large_select) as TextView
                    row.usedSmallArea = convertView.findViewById(R.id.report_used_small_area) as LinearLayout
                    row.usedSmallSelect = convertView.findViewById(R.id.report_used_small_select) as TextView
                    if (p.isCheck) {
                        row.usedLargeSelect!!.text = p.UsedAmountLarge
                        row.usedSmallSelect!!.text = p.UsedAmountSmall
                    }
                    row.usedLargeArea!!.setOnClickListener { ShowListSelectDialog(row.usedLargeSelect!!) }
                    row.usedSmallArea!!.setOnClickListener { ShowListSelectDialog(row.usedSmallSelect!!) }
                }
                row.subContentName!!.text = p.SubContentName
                row.subContentCheckbox!!.isChecked = p.isCheck
                row.subContentArea!!.setOnClickListener {
                    row.subContentCheckbox!!.isChecked = !row.subContentCheckbox!!.isChecked
                }
            }
        }
        return convertView!!
    }

    private fun ShowListSelectDialog(textView: TextView) {
        val listDialog = AlertDialog.Builder(_context, android.R.style.Theme_Holo_Light_Dialog_NoActionBar)
        listDialog.setItems(usedAmountList, DialogInterface.OnClickListener { dialog, which ->
            textView.text = usedAmountList[which]
        })
        listDialog.create().show()
    }
}