package fois.dailyreportsystem.data.adapter

// 作成日：2017/08/21
// 更新日：2017/12/25

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.TextView
import fois.dailyreportsystem.R
import fois.dailyreportsystem.activity.message.MessageGroupListActivity
import fois.dailyreportsystem.activity.message.MessageListActivity
import fois.dailyreportsystem.data.MessageGroupList
import fois.dailyreportsystem.data.MessageGroupListRow
import fois.dailyreportsystem.util.put
import fois.dailyreportsystem.util.setOnSafeClickListener
import fois.dailyreportsystem.util.settings
import java.util.ArrayList

class MessageGroupListAdapter(private val _context: Context, data: ArrayList<MessageGroupList>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = ArrayList<MessageGroupList>()
    private var originalData = ArrayList<MessageGroupList>()

    init {
        this._data = data
        this.originalData = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        var convertView = convertView

        val row: MessageGroupListRow = MessageGroupListRow()
        val p = this.getItem(position) as MessageGroupList

        convertView = this._inflater.inflate(R.layout.row_message_group_list, null)
        row.group = convertView!!.findViewById(R.id.message_list_group_name) as TextView
        row.unRead = convertView.findViewById(R.id.message_list_unread_badge) as TextView

        row.group!!.text = p.MessageGroupName

        if (p.messageUnreadCount == 0) {
            row.unRead!!.visibility = View.INVISIBLE
        } else {
            row.unRead!!.visibility = View.VISIBLE
            row.unRead!!.text = p.messageUnreadCount.toString()
        }

        convertView.setOnSafeClickListener {
            convertView.isEnabled = true
            forwardMessageDetail(position)
        }

        return convertView
    }

    private fun forwardMessageDetail(position: Int) {
        // タップ済み確認
        if((this._context as MessageGroupListActivity).clickPosition == null) {
            // タップ位置を保存
            (this._context as MessageGroupListActivity).clickPosition = true
            val p = this.getItem(position) as MessageGroupList
            val sp = _context.settings
            sp.put("MESSAGE_GROUP_ID", p.MessageGroupID)
            sp.put("MESSAGE_GROUP_NAME", p.MessageGroupName)
            sp.put("MESSAGE_UNREAD_COUNT", p.messageUnreadCount)
            val intent: Intent = Intent(this._context, MessageListActivity::class.java)
            this._context.startActivity(intent)
            (this._context as MessageGroupListActivity).finish()
            (this._context as MessageGroupListActivity).overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
        }
    }
}