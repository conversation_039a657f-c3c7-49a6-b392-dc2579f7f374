package fois.dailyreportsystem.data.adapter

// 作成日：2017/09/19
// 更新日：2018/02/16

import android.content.Context
import android.content.SharedPreferences
import android.graphics.Color
import android.text.InputFilter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.Login
import fois.dailyreportsystem.data.LoginRow
import fois.dailyreportsystem.util.Constant
import fois.dailyreportsystem.util.PasswordFilter
import java.util.ArrayList

class LoginAdapter(private val _context: Context, data: ArrayList<Login>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = ArrayList<Login>()
    // チェックボックス排他制御用配列
    private var companySelectArray = ArrayList<ImageView>()
    private var checkArray = ArrayList<Boolean>()
    // 会社コード、名前保存用配列
    private var companyNumberArray = ArrayList<String>()
    private var companyNameArray = ArrayList<String>()
    private var companyNumberLayoutMap: HashMap<Int, TextView> = HashMap()
    private var companyNameLayoutMap: HashMap<Int, TextView> = HashMap()
    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? =  _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)
    // 入力制御フィルター（英数字以外入力不可にする）
    private val filters = arrayOf<InputFilter>(PasswordFilter())

    init {
        this._data = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        for (i in 0.._data.size-1) {
            // チェック状態のみ全部あらかじめ作っておく（排他制御の為）
            val p = this.getItem(i) as Login
            checkArray.add(p.checked)
            companySelectArray.add(ImageView(_context))

            if (p.type == 1) {
                companyNumberArray.add(p.CompanyNumber!!)
                companyNameArray.add(p.CompanyName!!)
            } else {
                companyNumberArray.add("")
                companyNameArray.add("")
            }
        }
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        var convertView = convertView

        val row: LoginRow = LoginRow()
        val p = this.getItem(position) as Login

        when (p.type) {
            0 -> {
                // サブタイトル
                convertView = this._inflater.inflate(R.layout.row_setting_sub_title, null)
                row.SubTitle = convertView!!.findViewById(R.id.setting_sub_title) as TextView
                row.SubTitle!!.text = p.SubTitle
            }
            1 -> {
                // 会社リスト
                convertView = this._inflater.inflate(R.layout.row_company_info, null)
                // ビューの読み込み
                row.CompanyInfoItem = convertView!!.findViewById(R.id.company_info_item) as RelativeLayout
                row.CompanyNumber = convertView.findViewById(R.id.company_number_output) as TextView
                row.CompanyName = convertView.findViewById(R.id.company_name_output) as TextView
                row.CompanyCheck = convertView.findViewById(R.id.company_check) as LinearLayout
                row.CompanyCheckImage = convertView.findViewById(R.id.company_check_image) as ImageView
                companySelectArray[position] = row.CompanyCheckImage!!
                // 会社コードと会社名をリストに表示
                row.CompanyNumber!!.text = p.CompanyNumber
                row.CompanyName!!.text = p.CompanyName
                // 会社コードと会社名のレイアウトをマップに登録
                companyNumberLayoutMap[position] = row.CompanyNumber!!
                companyNameLayoutMap[position] = row.CompanyName!!
                // 最後にログインしていたアカウント判別
                if (checkArray[position]) {
                    // 最後にログインしていたアカウント
                    // 文字色を青に
                    row.CompanyNumber!!.setTextColor(Color.argb(255,50,103,204))
                    row.CompanyName!!.setTextColor(Color.argb(255,50,103,204))
                    // チェック状態を表示
                    row.CompanyCheckImage!!.visibility = View.VISIBLE
                } else {
                    // ログインされていなかったアカウント
                    // 文字色を黒に
                    row.CompanyNumber!!.setTextColor(Color.argb(255,0,0,0))
                    row.CompanyName!!.setTextColor(Color.argb(255,0,0,0))
                    // チェック状態を非表示
                    row.CompanyCheckImage!!.visibility = View.GONE
                }
                // チェックボックス排他制御、会社コード保存
                row.CompanyInfoItem!!.setOnClickListener {
                    if (!checkArray[position]) {
                        OnlyCheck(position)
                    }
                }
                row.CompanyCheck!!.setOnClickListener {
                    if (!checkArray[position]) {
                        OnlyCheck(position)
                    }
                }
            }
            3 -> {
                // 電話番号入力
                convertView = this._inflater.inflate(R.layout.row_setting_phone, null)
                row.InputEdit = convertView!!.findViewById(R.id.phone_number_input) as EditText
                row.InputEdit!!.setOnFocusChangeListener { v, hasFocus ->
                    if(!hasFocus) {
                        // フォーカスが外れたら電話番号を設定ファイルに保存
                        sharedPreference!!.edit().putString("PHONE_NUMBER_INPUT", row.InputEdit!!.text.toString()).commit()
                    }
                }
            }
            4 -> {
                // パスワード入力
                convertView = this._inflater.inflate(R.layout.row_setting_password, null)
                row.InputEdit = convertView!!.findViewById(R.id.password_input) as EditText
                row.InputEdit!!.filters = filters
                row.InputEdit!!.setOnFocusChangeListener { v, hasFocus ->
                    if(!hasFocus) {
                        // フォーカスが外れたらパスワードを設定ファイルに保存
                        sharedPreference!!.edit().putString("PASSWORD_INPUT", row.InputEdit!!.text.toString()).commit()
                        sharedPreference!!.edit().putString("PASSWORD_INPUT_AES",
                                Constant.GetAES(row.InputEdit!!.text.toString(), Constant.ENCODE_KEY, Constant.ENCODE_IV)).commit()
                    }
                }
            }
        }
        return convertView!!
    }

    // チェックボックス排他制御
    private fun OnlyCheck(position: Int) {
        // 排他制御
        // 選択状態のコード、名前を登録
        sharedPreference!!.edit().putString("COMPANY_NUMBER_SELECT", companyNumberArray[position]).commit()
        sharedPreference.edit().putString("COMPANY_NAME_SELECT", companyNameArray[position]).commit()
        // 配列の個数分繰り返し
        for (i in 0.._data.size-1) {
            // チェックした箇所のチェックを表示し、それ以外は非表示にする
            if (i == position) {
                checkArray[i] = true
                companySelectArray[i].visibility = View.VISIBLE
                // チェックした箇所の文字色を青にする
                companyNumberLayoutMap[i]!!.setTextColor(Color.argb(255,50,103,204))
                companyNameLayoutMap[i]!!.setTextColor(Color.argb(255,50,103,204))
            } else {
                checkArray[i] = false
                companySelectArray[i].visibility = View.GONE
                if (_data[i].type == 1) {
                    // チェックされていない箇所の文字色を黒にする
                    companyNumberLayoutMap[i]!!.setTextColor(Color.argb(255,0,0,0))
                    companyNameLayoutMap[i]!!.setTextColor(Color.argb(255,0,0,0))
                }
            }
        }
    }
}
