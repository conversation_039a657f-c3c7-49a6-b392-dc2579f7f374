package fois.dailyreportsystem.data.adapter

// 作成日：2019/06/17
// 更新日：2019/06/17

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.core.content.ContextCompat
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.Schedule
import fois.dailyreportsystem.data.SurveyPropertyRow
import fois.dailyreportsystem.util.Constant
import fois.dailyreportsystem.util.DateUtil
import fois.dailyreportsystem.util.toDate
import fois.dailyreportsystem.util.toString
import java.io.File

class SurveyPropertyAdapter(private val _context: Context, data: List<Schedule>) : BaseAdapter(), Filterable {
    private var _inflater: LayoutInflater
    private var _data: List<Schedule>
    private var _data_filter: List<Schedule>
    private var originalData: List<Schedule>
    private var valueFilter: ValueFilter? = null

    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? = _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)

    private var dir: File? = null
    private var fileList: Array<File>

    private val dateUtil = DateUtil()

    init {
        this._data = data
        this.originalData = data
        this._data_filter = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater

        // キャッシュ用 ディレクトリ、ファイルリストの取得
        dir = _context.cacheDir
        fileList = dir!!.listFiles()
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getFilter(): Filter {
        if (valueFilter == null) {
            valueFilter = ValueFilter()
        }
        return valueFilter!!
    }

    @SuppressLint("SetTextI18n")
    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {

        val row = SurveyPropertyRow()
        val itemData = this.getItem(position) as Schedule

        val view = _inflater!!.inflate(R.layout.row_survey_item, null)

        val researchTask = itemData.researchTask

        row.rowBackground = view.findViewById(R.id.row_survey) as RelativeLayout        // 調査日時
        row.surveyDate = view.findViewById(R.id.survey_date) as TextView        // 調査日時
        row.limitDate = view.findViewById(R.id.limit_date) as TextView            // 調査期限日
        row.areaName = view.findViewById(R.id.area_name) as TextView            // 都道府県（地域）
        row.address = view.findViewById(R.id.address) as TextView                // 担当者
        row.propertyKind = view.findViewById(R.id.property_kind) as TextView        // 着工予定日
        row.propertyName = view.findViewById(R.id.property_name) as TextView    // メッセージ
        row.surveyName = view.findViewById(R.id.survey_name) as TextView    // 工程

        // 調査日を変換
        var surveyDate: String = researchTask!!.PreferredDate1

        if (surveyDate.isNotEmpty()) {
            surveyDate = dateUtil.stringDateToFormat(surveyDate, "yyyy年MM月dd日 (E) HH:mm") + "～"
        }

        // 各種テキストセット
        row.surveyDate!!.text = surveyDate           // 調査日
        row.areaName!!.text = researchTask.PrefName      // 都道府県（地域）
        row.address!!.text = researchTask.CityName + researchTask.TaskAddress      // 調査先住所
        row.propertyKind!!.text = researchTask.MakerShortName + " " + researchTask.MakerName
        row.propertyName!!.text = researchTask.TaskName + " 様邸"
        row.surveyName!!.text = itemData.userName


        // 調査期限等を判定
        when (itemData.surveyStatusID) {
            // 報告書出力済み
            Constant.OrderStatusCode.COMPLETE.status -> {
                row.limitDate!!.text = _context.resources.getString(R.string.report_complete)
                row.limitDate!!.setTextColor(ContextCompat.getColor(_context, R.color.white))
                row.limitDate!!.setBackgroundResource(R.drawable.radius_frame_black)

                view.setBackgroundColor(ContextCompat.getColor(_context, R.color.date_gray))
            }
            // 現地調査済み
            Constant.OrderStatusCode.RESEARCHED.status -> {
                row.limitDate!!.text = _context.resources.getString(R.string.researched)
                row.limitDate!!.setTextColor(ContextCompat.getColor(_context, R.color.white))
                row.limitDate!!.setBackgroundResource(R.drawable.radius_frame_blue)

                view.setBackgroundColor(ContextCompat.getColor(_context, R.color.message_send))
            }
            // 受付済み
            Constant.OrderStatusCode.ACCEPTED.status -> {
                // 調査期限
                var date = researchTask.ReportDate.toDate("yyyy-MM-dd")
                row.limitDate!!.text = _context.resources.getString(R.string.limit_date, date!!.toString("M/dd"))
                row.limitDate!!.setBackgroundResource(R.drawable.radius_frame_yellow)
                row.limitDate!!.setTextColor(ContextCompat.getColor(_context, R.color.black))

                // 行の背景色だけ変更する
                view.setBackgroundColor(ContextCompat.getColor(_context, R.color.white))

            }
        }

        return view
    }

    private inner class ValueFilter : Filter() {
        override fun performFiltering(constraint: CharSequence?): FilterResults {
            val results = FilterResults()
            _data = originalData
            _data_filter = originalData
            if (constraint != null && constraint.isNotEmpty()) {
                val filterList = ArrayList<Schedule>()
                for (i in 0 until _data.size) {
                    if (_data_filter[i].researchTask!!.TaskName.toLowerCase().contains(constraint.toString().toLowerCase()) ||
                            _data_filter[i].userName!!.toLowerCase().contains(constraint.toString().toLowerCase())) {
                        val surveyItem = _data_filter[i]
                        filterList.add(surveyItem)
                    }
                }
                results.count = filterList.size
                results.values = filterList
            } else {
                results.count = originalData.size
                results.values = originalData
            }
            sharedPreference!!.edit().putInt("LIST_COUNT", results.count).apply()
            return results

        }

        override fun publishResults(constraint: CharSequence, results: FilterResults) {
            _data = results.values as List<Schedule>
            notifyDataSetChanged()
        }

    }
}
