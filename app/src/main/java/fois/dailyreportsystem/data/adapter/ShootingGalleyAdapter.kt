package fois.dailyreportsystem.data.adapter


import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import android.graphics.Bitmap
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.core.content.ContextCompat
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.ResearchPhotoAttribute
import fois.dailyreportsystem.util.*
import org.json.JSONObject
import java.io.File
import java.io.FileOutputStream
import java.util.HashMap

class ShootingGalleyAdapter(private val _context: Context, data: List<ResearchPhotoAttribute>) : BaseAdapter(), Filterable {

    private var _inflater: LayoutInflater
    private var _data: List<ResearchPhotoAttribute>
    private var _dataFilter: List<ResearchPhotoAttribute>
    private var originalData: List<ResearchPhotoAttribute>

    private var imageMap: HashMap<String, ImageView> = HashMap()

    private var dir: File? = null
    private var fileList: Array<File>

    private var valueFilter: ValueFilter? = null


    init {
        this._data = data
        this.originalData = data
        this._dataFilter = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater

        dir = _context.cacheDir
        fileList = dir!!.listFiles()
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getFilter(): Filter {
        if (valueFilter == null) {
            valueFilter = ValueFilter()
        }
        return valueFilter!!
    }

    data class GalleyView(var imageView: ImageView,
                          var categoryName: TextView,
                          var itemName: TextView,
                          var spareCheck: ImageView,
                          var spareText: TextView,
                          var reportCheck: ImageView,
                          var reportText: TextView,
                          var comment: TextView)

    @SuppressLint("SetTextI18n")
    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        var view = convertView
        var rowView: GalleyView

        if (view == null) {
            view = _inflater!!.inflate(R.layout.row_shooting_gralley, null)
            rowView = GalleyView(
                    imageView = view.findViewById(R.id.shooting_image),
                    categoryName = view.findViewById(R.id.category_name),
                    itemName = view.findViewById(R.id.item_name),
                    spareText = view.findViewById(R.id.spare_text),
                    spareCheck = view.findViewById(R.id.spare_check),
                    reportCheck = view.findViewById(R.id.report_check),
                    reportText = view.findViewById(R.id.report_text),
                    comment = view.findViewById(R.id.comment))

            view.tag = rowView
        }else{
            rowView = view.tag as GalleyView
        }

        val itemData = this.getItem(position) as ResearchPhotoAttribute

        rowView.categoryName.text = itemData.PointOutCategoryName
        rowView.itemName.text = itemData.PointOutItemName

        // 初期値の予備用、出力用設定
        switchSpareReport(view, rowView, itemData, itemData.Selected)

        // 切替処理
        rowView.spareCheck.setOnClickListener {  switchSpareReport(view, rowView, itemData, Constant.ResearchTaskPhotoType.SPARE.SELECTED) }
        rowView.spareText.setOnClickListener {   switchSpareReport(view, rowView, itemData, Constant.ResearchTaskPhotoType.SPARE.SELECTED) }
        rowView.reportCheck.setOnClickListener { switchSpareReport(view, rowView, itemData, Constant.ResearchTaskPhotoType.REPORT.SELECTED) }
        rowView.reportText.setOnClickListener {  switchSpareReport(view, rowView, itemData, Constant.ResearchTaskPhotoType.REPORT.SELECTED) }

        rowView.comment.text = itemData.Remarks


        // 画像
        rowView.imageView.setImageDrawable(null)
        imageMap[itemData.PhotoID] = rowView.imageView
        val file = File(_context.cacheDir, itemData.PhotoID + "_" + itemData.PhotoDate)
        getImageAsync(itemData.PhotoID, file)

        return view!!
    }

    private fun switchSpareReport(view: View?, rowView: GalleyView, dataItem: ResearchPhotoAttribute, select: String) {
        if (select == Constant.ResearchTaskPhotoType.SPARE.SELECTED) {
            rowView.spareCheck.setImageResource(R.drawable.circle_blue_check)
            rowView.spareText.setTextColor(ContextCompat.getColor(_context, R.color.light_blue))

            rowView.reportCheck.setImageResource(R.drawable.circle_non_check)
            rowView.reportText.setTextColor(ContextCompat.getColor(_context, R.color.gray))

            view!!.setBackgroundColor(ContextCompat.getColor(_context, R.color.light_gray))
        } else {
            rowView.spareCheck.setImageResource(R.drawable.circle_non_check)
            rowView.spareText.setTextColor(ContextCompat.getColor(_context, R.color.gray))

            rowView.reportCheck.setImageResource(R.drawable.circle_blue_check)
            rowView.reportText.setTextColor(ContextCompat.getColor(_context, R.color.light_blue))

            view!!.setBackgroundColor(ContextCompat.getColor(_context, R.color.white))
        }

        // 選択状態に変更があった場合は更新する
        if (dataItem.Selected != select) {
            dataItem.Selected = select
            updateImageInfo(dataItem.PhotoID, select)
        }
    }

    private fun setImage(photoID: String, bitmap: Bitmap) {
        imageMap[photoID]!!.setImageBitmap(bitmap)
    }

    // 写真取得
    private fun getImageAsync(photoID: String, file: File) {
        val asyncBitmapLoader = AsyncBitmapLoader(object : AsyncBitmapLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: Bitmap?) {
                if (result == null) {
                    return
                }
                try {
                    // 画像をキャッシュに保存
                    val fileOutputStream = FileOutputStream(file)
                    result.compress(Bitmap.CompressFormat.JPEG, 100, fileOutputStream)
                    // ファイルリスト更新
                    fileList = dir!!.listFiles()
                    // 取得した画像をセット
                    setImage(photoID, result)
                } catch (ex: Exception) {
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        val userId = _context.settings.getString("USER_ID")

        var url = Constant.URL + Constant.GetResearchPhoto + "?UserID=$userId&PhotoID=${photoID}&Size=2"

        // 非同期通信開始
        asyncBitmapLoader.execute(url)
    }


    /**
     * updateImageInfo 写真情報更新処理
     *  報告用、予備用の更新
     *  備考欄の更新
     * */
    private fun updateImageInfo(photoID: String, select: String) {
        val asyncJsonLoader = AsyncJsonLoader(object : AsyncJsonLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: JSONObject?) {
                if (result == null) {
                    return
                }
                try {
                    val status = result.getJSONObject("Status")
                    if (status.getInt("StatusCode") == 0) {
                        BaseActivity.log("result", "ok")
                    }
                } catch (ex: Exception) {
                    BaseActivity.log(BaseActivity.context?.javaClass!!.name, ex.toString())
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        val params = SendParameter()
        params.UserID = _context.settings.getString("USER_ID")
        params.PhotoID = photoID
        params.Selected = select

        // JSON形式のパラメータ作成
        val jsonParams = Gson().toJson(params)
        // URL作成
        val url = Constant.URL + Constant.ResearchPhotoModifySelected

        // 非同期通信開始
        // 第一引数：URL、第二引数：パラメータ(JSON形式)
        asyncJsonLoader.execute(url, jsonParams)

        BaseActivity.log(BaseActivity.context?.javaClass!!.name, url + jsonParams)
    }

    private inner class ValueFilter : Filter() {
        override fun performFiltering(constraint: CharSequence?): FilterResults {
            val results = FilterResults()
            _data = originalData
            _dataFilter = originalData
            if (constraint != null && constraint.isNotEmpty()) {
                // 全ての場合はオリジナルをセットする
                if (constraint == "2") {
                    results.count = originalData.size
                    results.values = originalData
                } else {
                    val filterList = ArrayList<ResearchPhotoAttribute>()

                    for (i in 0 until _data.size) {
                        if (_dataFilter[i].Selected == Constant.ResearchTaskPhotoType.REPORT.SELECTED) {
                            val photoData = _dataFilter[i]
                            filterList.add(photoData)
                        }
                    }
                    results.count = filterList.size
                    results.values = filterList
                }
            } else {
                results.count = originalData.size
                results.values = originalData
            }

            return results

        }

        override fun publishResults(constraint: CharSequence, results: FilterResults) {
            _data = results.values as List<ResearchPhotoAttribute>
            notifyDataSetChanged()
        }

    }
}
