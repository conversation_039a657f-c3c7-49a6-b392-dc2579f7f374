package fois.dailyreportsystem.data.adapter
import android.content.Context
import android.content.SharedPreferences
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.ResearchTaskMakers
import fois.dailyreportsystem.data.Schedule
import fois.dailyreportsystem.util.loadResearchTask
import java.util.*

class MakersAdapter(private val _context: Context, data: ArrayList<ResearchTaskMakers>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = ArrayList<ResearchTaskMakers>()

    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? = _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)

    init {
        this._data = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    }

    data class RowView(  var companyName: TextView, var checkImage: ImageView, var nextImage: ImageView)

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        var convertView = convertView

        val makerData = this.getItem(position) as ResearchTaskMakers

        convertView = this._inflater.inflate(R.layout.row_maker, null)

        val rowView = RowView(
                companyName = convertView.findViewById(R.id.company_name),
                checkImage = convertView.findViewById(R.id.check_image),
                nextImage = convertView.findViewById(R.id.next_image))


        // 一時保存データがあれば処理
        val researchTask = _context.loadResearchTask() ?: return convertView
        if ( researchTask.MakerID == makerData.CompanyID){
            rowView.checkImage.visibility = View.VISIBLE
            rowView.nextImage.visibility = View.GONE
        }else{
            rowView.checkImage.visibility = View.GONE
            rowView.nextImage.visibility = View.VISIBLE
        }

        rowView.companyName.text = makerData.CompanyName

        return convertView
    }

}