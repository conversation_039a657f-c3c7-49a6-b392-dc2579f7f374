package fois.dailyreportsystem.data.adapter

// 作成日：2017/08/17
// 更新日：2018/04/06

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.PorterDuff
import android.net.Uri
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.core.content.ContextCompat
import fois.dailyreportsystem.R
import fois.dailyreportsystem.activity.MainActivity
import fois.dailyreportsystem.activity.fileviewer.FileViewTypes
import fois.dailyreportsystem.activity.fileviewer.FileViewerActivity
import fois.dailyreportsystem.activity.message.MessageListActivity
import fois.dailyreportsystem.activity.report.FileListActivity
import fois.dailyreportsystem.activity.report.PhotoProcessListActivity
import fois.dailyreportsystem.activity.report.ReportListActivity
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.Main
import fois.dailyreportsystem.data.MainRow
import fois.dailyreportsystem.data.WorkScheduleSearchConditions
import fois.dailyreportsystem.util.*
import java.io.File
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*

class MainAdapter(private val _context: Context, data: ArrayList<Main>) : BaseAdapter(), Filterable {
    private val workScheduleExtension = ".jpg"

    private var _inflater: LayoutInflater
    private var _data = java.util.ArrayList<Main>()
    private var _data_filter = java.util.ArrayList<Main>()
    private var originalData = java.util.ArrayList<Main>()
    private var valueFilter: ValueFilter? = null

    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? = _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)

    private var imageMap: HashMap<Int, ImageView> = HashMap()
    private var dir: File? = null
    private var fileList: Array<File>

    init {
        this._data = data
        this.originalData = data
        this._data_filter = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater

        // キャッシュ用 ディレクトリ、ファイルリストの取得
        dir = _context.cacheDir
        fileList = dir!!.listFiles()
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getFilter(): Filter {
        if (valueFilter == null) {
            valueFilter = ValueFilter()
        }
        return valueFilter!!
    }
    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        var convertView = convertView

        val row: MainRow = MainRow()
        val p = this.getItem(position) as Main

        convertView = this._inflater.inflate(R.layout.row_main_item, null)

        row.taskIcon = convertView!!.findViewById(R.id.task_icon) as ImageView            // 邸画像
        row.taskInfo = convertView.findViewById(R.id.task_info_layout) as LinearLayout        // 邸情報用フィールド
        row.taskName = convertView.findViewById(R.id.task_name) as TextView            // 邸名
        row.areaName = convertView.findViewById(R.id.area_name) as TextView            // 都道府県（地域）
        row.fctUser = convertView.findViewById(R.id.fct_user) as TextView                // 担当者
        row.taskPlanDate = convertView.findViewById(R.id.task_plan_date) as TextView        // 着工予定日
        row.unreadBadge = convertView.findViewById(R.id.unread_badge) as TextView            // 未読カウントバッジ
        row.messageArea = convertView.findViewById(R.id.message_area) as RelativeLayout    // メッセージ
        row.processArea = convertView.findViewById(R.id.process_area) as RelativeLayout    // 工程
        row.processIcon = convertView.findViewById(R.id.process_icon) as ImageView    // 工程
        row.processText = convertView.findViewById(R.id.process_text) as TextView    // 工程
        row.reportArea = convertView.findViewById(R.id.report_area) as RelativeLayout        // 日報
        row.workContentArea = convertView.findViewById(R.id.task_report) as LinearLayout                // 当日の工程・作業内容表示領域
        row.reportContent = convertView.findViewById(R.id.task_report_content) as LinearLayout          // 当日の工程
        row.reportSubContent = convertView.findViewById(R.id.task_report_sub_content) as LinearLayout   // 当日の作業内容
        row.fileListArea = convertView.findViewById(R.id.filelist_area) as RelativeLayout   // ファイルリスト
        row.workScheduleArea = convertView.findViewById(R.id.workschedule_area) as RelativeLayout   // 工程表エリア

        // マップに場所とビューを格納
        imageMap.put(position, row.taskIcon!!)

        // レイアウトタップで遷移
        row.messageArea!!.setOnClickListener {
            forwardMessageDetail(position)
        }
        row.processArea!!.setOnClickListener {
            forwardProcessList(position)
        }
        row.reportArea!!.setOnClickListener {
            forwardReportList(position)
        }
        row.fileListArea!!.setOnClickListener {
            forwardFileList(position)
        }
        row.workScheduleArea!!.setOnClickListener {
            forwardWorkSchedule(position)
        }

        // 着工予定日用に日付変換
        val simpleDateFormat: SimpleDateFormat = SimpleDateFormat("M月d日（E）")
        var taskPlanDate: String = p.TaskPlanStartDate!!
        if (!taskPlanDate.isNullOrEmpty() && taskPlanDate.length == 8) {
            val date: Date = SimpleDateFormat("yyyyMMdd").parse(taskPlanDate)
            taskPlanDate = "着工予定日：" + simpleDateFormat.format(date)
        }

        // 各種テキストセット
        row.taskName!!.text = p.TaskName        // 邸名
        row.areaName!!.text = p.AreaName        // 都道府県（地域）
        row.fctUser!!.text = p.FctUserName    // 担当者
        row.taskPlanDate!!.text = taskPlanDate    // 着工予定日
        // 未読カウントセット
        if (p.UnreadMessageCount == "0") {
            // 未読が0の場合、バッジを非表示
            row.unreadBadge!!.visibility = View.INVISIBLE
        } else {
            if (p.UnreadMessageCount!!.toInt() > 99) {
                // 未読が100件以上ある場合99にして表示
                row.unreadBadge!!.text = "99"
            } else {
                row.unreadBadge!!.text = p.UnreadMessageCount
            }
        }

        // 邸情報領域色変更、予定・実績内容表示
        if (p.WorkStatus != "0") {
            // 当日の予定・実績登録済み
            // 邸情報領域色変更
            if (p.WorkStatus == "1") {
                // 予定
                row.taskInfo!!.setBackgroundResource(R.color.main_schedule_color)
            } else {
                // 実績
                row.taskInfo!!.setBackgroundResource(R.color.main_result_color)
            }
            // 作業内容表示
            row.workContentArea!!.visibility = View.VISIBLE
            for (i in 0..p.ContentNames.size-1) {
                val contentName: TextView = TextView(_context)
                contentName.text = p.ContentNames[i]
                contentName.maxLines = 1
                contentName.ellipsize = TextUtils.TruncateAt.END
                contentName.gravity = Gravity.CENTER_VERTICAL
                contentName.textSize = 11F
                contentName.setTextColor(Color.BLACK)
                if (p.ShootingPhoto == "1"){
                    contentName.setTextColor(Color.RED)
                }
                row.reportContent!!.addView(contentName, LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT))
                val subContentName: TextView = TextView(_context)
                subContentName.text = p.SubContentNames[i]
                subContentName.maxLines = 1
                subContentName.ellipsize = TextUtils.TruncateAt.END
                subContentName.gravity = Gravity.CENTER_VERTICAL
                subContentName.textSize = 11F
                subContentName.setTextColor(Color.BLACK)
                if (p.ShootingPhoto == "1"){
                    subContentName.setTextColor(Color.RED)
                }
                row.reportSubContent!!.addView(subContentName, LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT))
                if (i == 3) {
                    // MAX4件まで表示
                    break
                }
            }
            // 着工予定日の文字サイズ変更
            row.taskPlanDate!!.textSize = 11F
        } else {
            // 当日の予定・実績なし
            // 作業情報領域を非表示
            row.workContentArea!!.visibility = View.GONE
            // 邸情報領域色変更（デフォルト）
            row.taskInfo!!.setBackgroundResource(R.color.list)
        }

        // 邸画像の取得
        if (fileList.isNotEmpty()) {
            for (i in 0..fileList.size - 1) {
                if (fileList[i].toString().indexOf("_") != -1) {
                    if (fileList[i].toString().substring(fileList[i].toString().lastIndexOf("/")+1, fileList[i].toString().lastIndexOf("_")) == p.TaskID) {
                        // キャッシュに邸の画像が保存されている場合
                        if (fileList[i].toString().substring(fileList[i].toString().lastIndexOf("_")+1) == p.TaskIconAddDatetime) {
                            // 更新日時も一致したらキャッシュから読み込み
                            try {
                                val fileInputStream: FileInputStream = FileInputStream(fileList[i])
                                val bitmap: Bitmap = BitmapFactory.decodeStream(fileInputStream)
                                SetImage(position, bitmap)
                            } catch (e: FileNotFoundException) {
                                e.printStackTrace()
                            }
                        } else {
                            // 更新日時が違う場合キャッシュから削除し、新しく取得
                            try {
                                // 削除
                                val file: File = File(fileList[i].toString())
                                file.delete()
                                // 取得
                                val newFile: File = File(_context.cacheDir, p.TaskID + "_" + p.TaskIconAddDatetime)
                                GetImageAsync(p.TaskIconUrl!!, position, newFile)
                            } catch (e: FileNotFoundException) {
                                e.printStackTrace()
                            }
                        }
                        break
                    }
                }
                if (i == fileList.size - 1) {
                    // キャッシュに画像が保存されていない場合、通信して取得
                    val file: File = File(_context.cacheDir, p.TaskID + "_" + p.TaskIconAddDatetime)
                    GetImageAsync(p.TaskIconUrl!!, position, file)
                }
            }
        } else {
            // キャッシュに画像が保存されていない場合、通信して取得
            val file: File = File(_context.cacheDir, p.TaskID + "_" + p.TaskIconAddDatetime)
            GetImageAsync(p.TaskIconUrl!!, position, file)
        }

        // ファイルリストアイコンの表示
        row.fileListArea!!.visibility = p.hasFile.either(View.VISIBLE, View.INVISIBLE)

        // 未撮影の予定の有無に応じてProcessボタンの表示を変更
        if (p.ShootingPhoto == "1"){
            row.processArea!!.setBackgroundResource(R.color.text_red)
            row.processIcon!!.setColorFilter(Color.parseColor("#FFFFFF"), PorterDuff.Mode.SRC_IN)
            row.processText!!.setTextColor(ContextCompat.getColor(_context, R.color.white))
        }

        return convertView
    }


    private fun forwardMessageDetail(position: Int) {
        // タップ済み確認
        if((this._context as MainActivity).clickPosition == null) {
            // タップ位置を保存
            (this._context as MainActivity).clickPosition = true
            val p = this.getItem(position) as Main
            sharedPreference!!.edit().putString("TASK_ID", p.TaskID).apply()
            sharedPreference.edit().putString("TASK_NAME", p.TaskName).apply()
            sharedPreference.edit().putString("MESSAGE_GROUP_NAME", p.TaskName).apply()
            val intent: Intent = Intent(this._context, MessageListActivity::class.java)
            this._context.startActivity(intent)
            (this._context as MainActivity).overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
        }
    }

    private fun forwardProcessList(position: Int) {
        // タップ済み確認
        if((this._context as MainActivity).clickPosition == null) {
            // タップ位置を保存
            (this._context as MainActivity).clickPosition = true
            val p = this.getItem(position) as Main
            sharedPreference!!.edit().putString("TASK_ID", p.TaskID).apply()
            sharedPreference.edit().putString("TASK_NAME", p.TaskName).apply()
            val intent: Intent = Intent(this._context, PhotoProcessListActivity::class.java)
            this._context.startActivity(intent)
            (this._context as MainActivity).overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
        }
    }

    private fun forwardReportList(position: Int) {
        // タップ済み確認
        if((this._context as MainActivity).clickPosition == null) {
            // タップ位置を保存
            (this._context as MainActivity).clickPosition = true
            val p = this.getItem(position) as Main
            sharedPreference!!.edit().putString("TASK_ID", p.TaskID).apply()
            sharedPreference.edit().putString("TASK_NAME", p.TaskName).apply()
            val intent: Intent = Intent(this._context, ReportListActivity::class.java).apply {
                putExtra(ConstantParameters.INTENT_KEY_TASKID, p.TaskID)
                putExtra(ConstantParameters.INTENT_KEY_TASKNAME, p.TaskName)
            }
            this._context.startActivity(intent)
            (this._context as MainActivity).overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
        }
    }

    private fun forwardFileList(position: Int) {
        // タップ済み確認
        if ((_context as MainActivity).clickPosition != null) {
            return
        }

        // タップ位置を保存
        _context.clickPosition = true
        val p = getItem(position) as Main
        (sharedPreference ?: return).edit().putString("TASK_ID", p.TaskID).apply()
        sharedPreference.edit().putString("TASK_NAME", p.TaskName).apply()
        val intent: Intent = Intent(_context, FileListActivity::class.java).apply {
            putExtra(ConstantParameters.INTENT_KEY_TASKID, p.TaskID)
        }
        _context.startActivity(intent)
        _context.overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
    }

    private fun forwardWorkSchedule(position: Int) {
        // タップ済み確認
        if ((this._context as MainActivity).clickPosition != null) {
            return
        }

        // タップ位置を保存
        this._context.clickPosition = true
        val target = this.getItem(position) as Main
        (sharedPreference ?: return).edit().putString("TASK_ID", target.TaskID).apply()
        sharedPreference.edit().putString("TASK_NAME", target.TaskName).apply()

        val params = SendParameter()
        val conditions = target.TaskID?.let { WorkScheduleSearchConditions(it) } ?: return
        params.UserID = _context.settings.getString("USER_ID")
        params.OfficeID = _context.settings.getString("OFFICE_ID")
        params.JsonData = conditions.toJson()

        val baseDir = File(_context.cacheDir, "Files")
        val fileName = target.TaskName + "-工程表"
        startDownload(params, baseDir, fileName) {
            val intent: Intent = Intent(this._context, FileViewerActivity::class.java).apply {
                putExtra(ConstantParameters.FILE_NAME, fileName)
                putExtra(ConstantParameters.FILE_EXTENSION, workScheduleExtension)
                putExtra(ConstantParameters.VIEW_TYPE, FileViewTypes.WorkSchedule)
                data = Uri.parse(it)
            }
            this._context.startActivity(intent)
            this._context.overridePendingTransition(R.anim.activity_open_enter, R.anim.activity_open_exit)
        }
    }

    private inner class ValueFilter : Filter() {
        override fun performFiltering(constraint: CharSequence?): FilterResults {
            val results = FilterResults()
            _data = originalData
            _data_filter = originalData
            if (constraint != null && constraint.length > 0) {
                val filterList = ArrayList<Main>()
                for (i in 0.._data.size - 1) {
                    if (_data_filter.get(i).TaskName!!.toLowerCase().contains(constraint.toString().toLowerCase()) ||
                            _data_filter.get(i).TaskNameKana!!.toLowerCase().contains(constraint.toString().toLowerCase()) ||
                            //_data_filter.get(i).AreaName!!.toLowerCase().contains(constraint.toString().toLowerCase()) ||
                            //_data_filter.get(i).ContentVersionName!!.toLowerCase().contains(constraint.toString().toLowerCase())||
                            _data_filter.get(i).FctUserName!!.toLowerCase().contains(constraint.toString().toLowerCase())) {
                        var main= Main()
                        main = _data_filter.get(i)
                        filterList.add(main)
                    }
                }
                results.count = filterList.size
                results.values = filterList
            } else {
                results.count = originalData.size
                results.values = originalData
            }
            sharedPreference!!.edit().putInt("LIST_COUNT", results.count).commit()
            return results

        }

        override fun publishResults(constraint: CharSequence, results: FilterResults) {
            _data = results.values as ArrayList<Main>
            notifyDataSetChanged()
        }

    }

    private fun SetImage(position: Int, bitmap: Bitmap) {
        imageMap[position]!!.setImageBitmap(bitmap)
    }

    // 邸アイコン取得
    private fun GetImageAsync(url: String, position: Int, file: File) {
        val asyncBitmapLoader = AsyncBitmapLoader(object : AsyncBitmapLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {}

            // 実行後
            override fun postExecute(result: Bitmap?) {
                if (result == null) {
                    return
                }
                try {
                    // 画像をキャッシュに保存
                    val fileOutputStream: FileOutputStream = FileOutputStream(file)
                    result.compress(Bitmap.CompressFormat.PNG, 100, fileOutputStream)
                    // ファイルリスト更新
                    fileList = dir!!.listFiles()
                    // 取得した画像をセット
                    SetImage(position, result)
                } catch (ex: Exception) {}
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {}
        })

        // 非同期通信開始
        asyncBitmapLoader.execute(url)
    }

    /**
     * ファイルダウンロード処理
     * */
    private fun startDownload(params: SendParameter, baseDir: File, fileName: String, completion: (String) -> Unit) {
        // URL作成
        val url = Constant.URL + Constant.WorkSchedule
        val view = _context as? MainActivity

        val asyncFileLoader = AsyncFileLoader(_context, object : AsyncFileLoader.AsyncCallback {
            // 実行前
            override fun preExecute() {
                view?.showHub()
            }

            // 実行後
            override fun postExecute(result: ByteArray?) {
                if (result == null) {
                    return
                }
                var outputStream: FileOutputStream? = null

                try {
                    // 保存先のディレクトリが無ければ作成する
                    if (!baseDir.exists()) {
                        baseDir.mkdir()
                    }

                    val fileFullName = fileName + workScheduleExtension
                    val absoluteFilePath = baseDir.absolutePath + "/" + fileFullName

                    outputStream = FileOutputStream(absoluteFilePath)
                    outputStream.write(result)
                    // 読み込みが終ったら、出力ストリームに貯めた出力バイトをファイルへ一気に書き込みます
                    outputStream.flush()

                    completion(absoluteFilePath)
                } catch (ex: Exception) {
                    ShowMessages().Show(BaseActivity.context, BaseActivity.context!!.getString(R.string.unexpected_error))
                    BaseActivity.log(BaseActivity.context?.javaClass!!.name, "$ex")
                } finally {
                    view?.hideHub()
                    view?.clickPosition = null
                    outputStream?.close()
                }
            }

            // 実行中
            override fun progressUpdate(progress: Int?) {}

            // キャンセル
            override fun cancel() {
                view?.hideHub()
                view?.clickPosition = null
            }
        }, params.toMap())

        // 非同期通信開始
        // 第1引数：URL
        asyncFileLoader.execute(url)
    }
}
