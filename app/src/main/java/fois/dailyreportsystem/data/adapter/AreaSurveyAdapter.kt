package fois.dailyreportsystem.data.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.AreaData

class AreaSurveyAdapter(private val _context: Context, data: List<AreaData>) : BaseAdapter() {

    private var _inflater: LayoutInflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    private var _data = data

    class AreaRowView {
        var checkImage: ImageView? = null
        var areaName: TextView? = null
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {

        val row: AreaRowView

        val itemData = this.getItem(position) as AreaData

        var view = _inflater.inflate(R.layout.row_area_survey, null)

        if (convertView == null) {
            row = AreaRowView()
            row.areaName = view.findViewById(R.id.area_name) as TextView
            row.checkImage = view.findViewById(R.id.check_image) as ImageView
            view.tag = row
        } else {
            view = convertView
            row = view.tag as AreaRowView

        }

        if (itemData.checked) {
            row.checkImage!!.visibility = View.VISIBLE
        }else{
            row.checkImage!!.visibility = View.GONE
        }

        val areaName = itemData.areaName.replace("\r\n", "")
        row.areaName!!.text = areaName

        return view
    }
}
