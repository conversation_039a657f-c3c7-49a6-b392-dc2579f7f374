package fois.dailyreportsystem.data.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import fois.dailyreportsystem.R
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Bitmap
import fois.dailyreportsystem.data.AreaData
import fois.dailyreportsystem.util.ColorUtil
import fois.dailyreportsystem.util.saveAreas
import fois.dailyreportsystem.util.setOnSafeClickListener
import fois.dailyreportsystem.util.toDimensionTextSize


class AreaAdapter(private val _context: Context, data: MutableList<AreaData>) : BaseAdapter() {

    private var _inflater: LayoutInflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    private var _data = data


    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    fun getData(): MutableList<AreaData>{
        return _data
    }


    data class AreaRowView(
        var checkImage : ImageView,
        var areaName : TextView,
        var checked: Boolean
    )

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        var view = convertView

        val row : AreaRowView
        if(convertView == null) {
            view = _inflater.inflate(R.layout.row_area, null)
            row = AreaRowView(
                    checkImage = view.findViewById(R.id.check_image) as ImageView,
                    areaName = view.findViewById(R.id.area_name) as TextView,
                    checked = false
            )
            row.checkImage.setImageResource(fois.dailyreportsystem.R.drawable.circle_non_check)
            view.tag = row
        } else {
            row = view!!.tag as AreaRowView
        }

        val itemData = this.getItem(position) as AreaData
        row.areaName.text = itemData.areaName
        row.checked = itemData.checked
        row.checkImage.setImageBitmap(drawIcon(row.checked, position))

        view!!.setOnSafeClickListener {
            row.checked = !row.checked
            row.checkImage.setImageBitmap(drawIcon(row.checked, position))
            val areaData = _data[position]
            areaData.checked = row.checked
            _data.set(position, areaData)
            // エリアリストをまるごと保存
            _context.saveAreas(_data)
        }

        return view!!
    }

    /**
     * チェック〇描画
     */
    private fun drawIcon(checked: Boolean, position: Int): Bitmap{
        val scale = _context.toDimensionTextSize(1F)
        val bitmap = Bitmap.createBitmap((scale * 24).toInt(), (scale * 24).toInt(), Bitmap.Config.ARGB_8888)
        val canvas: Canvas
        canvas = Canvas(bitmap)
        canvas.drawColor(Color.WHITE)

        val paint = Paint()
        paint.color = ColorUtil.getCalenderColor(position)
        paint.style = if(checked) { Paint.Style.FILL }else{ Paint.Style.STROKE }
        paint.isAntiAlias = true
        paint.strokeWidth = _context.toDimensionTextSize(scale / 4)

        val whitePaint  = Paint()
        whitePaint.color = Color.WHITE
        whitePaint.style = Paint.Style.STROKE
        whitePaint.strokeWidth = _context.toDimensionTextSize(scale / 1.5F)
        canvas.drawCircle(scale * 12, scale * 12, scale * if(checked) { 12F }else{ 11F }, paint)

        if(checked) {
            val path = android.graphics.Path()
            path.moveTo(scale * 8, scale * 12)
            path.lineTo(scale * 11, scale * 15)
            path.lineTo(scale * 17, scale * 9)
            canvas.drawPath(path, whitePaint)
        }

        return bitmap
    }

}
