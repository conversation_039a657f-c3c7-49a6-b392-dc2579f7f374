package fois.dailyreportsystem.data.adapter

// 作成日：2017/10/05
// 更新日：2018/02/02

import android.content.Context
import android.content.SharedPreferences
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.*
import com.google.gson.Gson
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.ReportWorkerContent
import fois.dailyreportsystem.data.ReportWorkerContentRow
import fois.dailyreportsystem.data.WorkContent
import fois.dailyreportsystem.data.Worker

class ReportWorkerContentAdapter(private val _context: Context, data: ArrayList<ReportWorkerContent>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = ArrayList<ReportWorkerContent>()

    // 設定ファイル読み込み
    private val sharedPreferences: SharedPreferences = _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)
    // 日報一時保存ファイル読み込み
    private val reportTemporary: SharedPreferences = _context.getSharedPreferences("Report", Context.MODE_PRIVATE)
    // ビュー配列
    private var workerMap: HashMap<Int, EditText> = HashMap()
    private var contentMap: HashMap<Int, CheckBox> = HashMap()
    // 作業者、工程配列
    var listWorker: ArrayList<Worker> = ArrayList()
    var listContent: ArrayList<WorkContent> = ArrayList()

    init {
        this._data = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        // 作業者リスト、工程リストの用意
        for (i in 0.._data.count()-1) {
            // 作業者リスト
            if (_data[i].WorkerName != null) {
                val worker: Worker = Worker()
                worker.UserName = _data[i].WorkerName
                listWorker.add(worker)
            }
            // 工程リスト
            if (_data[i].contentIsCheck) {
                val content: WorkContent = WorkContent()
                content.ContentID = _data[i].ContentID
                content.ContentName = _data[i].ContentName
                content.SubContentID = ""
                content.SubContentName = ""
                content.WorkerCompanyID = ""
                listContent.add(content)
            }
            // カンマ区切りで工程IDを結合
            var unionContent: String = ""
            for (j in 0..listContent.size-1) {
                if (j != 0) {
                    unionContent += ","
                }
                unionContent += listContent[j].ContentID
            }
            // 日報一時ファイルに保存
            reportTemporary.edit().putString("UNION_CONTENT", unionContent).commit()
            reportTemporary.edit().putString("LIST_CONTENT", Gson().toJson(listContent).toString()).commit()
        }
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        var convertView = convertView

        val row: ReportWorkerContentRow = ReportWorkerContentRow()
        val p = this.getItem(position) as ReportWorkerContent

        when (p.type) {
            1 -> {
                // サブタイトル
                convertView = this._inflater.inflate(R.layout.row_sub_title, null)
                row.subTitle = convertView!!.findViewById(R.id.sub_title) as TextView
                row.subTitle!!.text = p.SubTitle
            }
            2 -> {
                // 作業者リスト
                convertView = this._inflater.inflate(R.layout.row_report_worker, null)
                row.workerNumber = convertView!!.findViewById(R.id.report_worker_number) as TextView
                row.workerInput = convertView.findViewById(R.id.report_worker) as EditText
                row.workerInputIcon = convertView.findViewById(R.id.report_worker_input_icon) as ImageView
                row.workerInput!!.tag = position
                workerMap.put(position, row.workerInput!!)
                row.workerNumber!!.text = "0" + position
                row.workerInput!!.setText(p.WorkerName)
                row.workerInputIcon!!.setOnClickListener {
                    // 作業者入力フィールドにフォーカスを当て、キーボードを表示
                    row.workerInput!!.requestFocus()
                    val inputMethodManager = _context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                    inputMethodManager.toggleSoftInput(1, InputMethodManager.SHOW_IMPLICIT)
                }
                row.workerInput!!.onFocusChangeListener = View.OnFocusChangeListener { v, hasFocus ->
                    if (!hasFocus) {
                        // フォーカスが外れたら作業者を一時保存
                        SaveWorker(v)
                    }
                }
            }
            3 -> {
                // 工程リスト
                convertView = this._inflater.inflate(R.layout.row_report_content, null)
                row.contentName = convertView!!.findViewById(R.id.report_content_name) as TextView
                row.contentCheckBox = convertView.findViewById(R.id.report_content_check) as CheckBox
                row.contentCheckBoxArea = convertView.findViewById(R.id.report_content_area) as RelativeLayout
                row.contentCheckBox!!.tag = position
                contentMap.put(position, row.contentCheckBox!!)
                row.contentName!!.text = p.ContentName
                row.contentCheckBox!!.isChecked = p.contentIsCheck
                row.contentCheckBoxArea!!.setOnClickListener {
                    // チェックボックス切替
                    row.contentCheckBox!!.isChecked = !row.contentCheckBox!!.isChecked
                }
                row.contentCheckBox!!.setOnCheckedChangeListener { v, isChecked ->
                    // チェックボックスに変化があった場合、工程を一時保存
                    SaveContent(v)
                }
            }
        }
        return convertView!!
    }

    // 作業者一時保存
    fun SaveWorker(v: View) {
        // タグを取得
        val tagPosition: Int = Integer.parseInt(v.tag.toString())
        // 対応した場所の作業者名を取得
        val worker: String = workerMap[tagPosition]!!.text.toString()
        // _dataに保存
        _data[tagPosition].WorkerName = worker
        // 作業者リストに保存
        for (i in 0.._data.count()-1) {
            if (_data[i].WorkerName != null) {
                listWorker[i-1].CompanyID = sharedPreferences.getString("COMPANY_ID", "")
                listWorker[i-1].CompanyName = ""
                listWorker[i-1].CompanyNumber = ""
                listWorker[i-1].UserID = ""
                listWorker[i-1].UserName = _data[i].WorkerName
                listWorker[i-1].UserPhoneNumber = ""
            }
        }
        // 日報一時保存ファイルに保存
        reportTemporary.edit().putString("LIST_WORKER", Gson().toJson(listWorker).toString()).commit()
    }

    // 工程一時保存
    fun SaveContent(v: View) {
        // タグを取得
        val tagPosition: Int = Integer.parseInt(v.tag.toString())
        // 対応した場所のチェックボックスの状態を取得
        val check: Boolean = contentMap[tagPosition]!!.isChecked
        // _dataに保存
        _data[tagPosition].contentIsCheck = check
        // 工程リストをクリア
        listContent.clear()
        // 工程リストに保存
        for (i in 0.._data.count()-1) {
            if (_data[i].contentIsCheck) {
                val content: WorkContent = WorkContent()
                content.ContentID = _data[i].ContentID
                content.ContentName = _data[i].ContentName
                content.SubContentID = ""
                content.SubContentName = ""
                content.WorkerCompanyID = ""
                listContent.add(content)
            }
        }
        // カンマ区切りで工程IDを結合
        var unionContent: String = ""
        for (i in 0..listContent.size-1) {
            if (i != 0) {
                unionContent += ","
            }
            unionContent += listContent[i].ContentID
        }
        // 日報一時ファイルに保存
        reportTemporary.edit().putString("UNION_CONTENT", unionContent).commit()
        reportTemporary.edit().putString("LIST_CONTENT", Gson().toJson(listContent).toString()).commit()
    }
}
