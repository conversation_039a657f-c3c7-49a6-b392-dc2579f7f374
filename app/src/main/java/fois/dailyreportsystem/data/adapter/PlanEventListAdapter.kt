package fois.dailyreportsystem.data.adapter

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import fois.dailyreportsystem.R
import fois.dailyreportsystem.data.AreaData
import fois.dailyreportsystem.data.CalendarSchedule
import fois.dailyreportsystem.util.*
import java.util.*


class PlanEventListAdapter(private val _context: Context, data: MutableList<CalendarSchedule>, eventDate: Date) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = mutableListOf<CalendarSchedule>()
    private var eventDate = eventDate
    private var areasList : MutableList<AreaData>?

    private val dateUtil = DateUtil()

    init {
        this._data = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater

        // 保存差ているエリア情報を取得する
        this.areasList = _context.loadAreas()
        if (this.areasList == null) {
            this.areasList = mutableListOf<AreaData>()
            this.areasList!!.add(AreaData.mySchedule())
        }
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    data class EventScheduleRow(
        var TravelTime: TextView,
        var StartDateTime: TextView,
        var EndDateTime: TextView,
        var TravelTitle: TextView,
        var Title: TextView,
        var UserName: TextView,
        var Location:TextView,
        var LineColor: View
    )

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        val row: EventScheduleRow
        var view = convertView
        val cs = this.getItem(position) as CalendarSchedule
        if (view == null) {
            view = _inflater.inflate(R.layout.row_event_schedule, parent, false)
            row = EventScheduleRow(
                    TravelTime = view.findViewById<TextView>(R.id.travel_time),
                    StartDateTime = view.findViewById<TextView>(R.id.start_date_time),
                    EndDateTime = view.findViewById<TextView>(R.id.end_date_time),
                    TravelTitle = view.findViewById<TextView>(R.id.travel_title),
                    UserName = view.findViewById<TextView>(R.id.user_name),
                    Title = view.findViewById<TextView>(R.id.title),
                    Location = view.findViewById<TextView>(R.id.location),
                    LineColor = view.findViewById<View>(R.id.vertical_line)
            )
            view.tag = row
        }else{
            row = view.tag as EventScheduleRow
        }


        // 日付は以下の形式でサーバーから送信されるので編集して表示する。
        // yyyy-MM-dd HH:mm
        // 移動時間
        if (cs.schedule.travelTime.isNullOrEmpty() || cs.schedule.travelTime == "0") {
            row.TravelTime.visibility = View.GONE
            row.TravelTitle.visibility = View.GONE
        }else{
            val travel = Integer.parseInt(cs.schedule.travelTime!!)
            val date = cs.schedule.startDateTime!!.toDate()!!.addMinutes(-travel)
            row.TravelTime.text = date.toString("HH:mm")
            row.TravelTitle.text = travel.toTravel()
        }
        // 開始と終了
        val startDate = cs.schedule.startDateTime!!.toDate() ?: return view!!
        val endDate = cs.schedule.endDateTime!!.toDate() ?: return view!!
        val eventDate = eventDate
        // 終日
        if (cs.schedule.allDay == "1"){
            row.StartDateTime.text = "終日"
            row.EndDateTime.text = ""
        }else if (startDate.year() != endDate.year() || startDate.month() != endDate.month() ||startDate.day() != endDate.day()){
            // 開始日の場合
            if (startDate.year() == eventDate.year() && startDate.month() == eventDate.month() && startDate.day() == eventDate.day()){
                row.StartDateTime.text = startDate.toString("HH:mm")
                row.EndDateTime.text = ""
            }
            // 終了日の場合
            else if (endDate.year() == eventDate.year() && endDate.month() == eventDate.month() && endDate.day() == eventDate.day()){
                row.StartDateTime.text = "終了"
                row.EndDateTime.text = endDate.toString("HH:mm")
            }
            // 中日
            else{
                row.StartDateTime.text = "終日"
                row.EndDateTime.text = ""
            }
        }else{
            row.StartDateTime.text = startDate.toString("HH:mm")
            row.EndDateTime.text = endDate.toString("HH:mm")
        }

        row.Title.text = cs.schedule.title
        row.UserName.text = cs.schedule.userName
        if (cs.schedule.userID != _context.settings.getString("USER_ID")){
            row.UserName.setTextColor(Color.argb(255,99,99,99))
        }else{
            row.UserName.setTextColor(Color.BLACK)
        }

        if (cs.schedule.surveyStatusID.isNullOrBlank()){
            // 通常スケジュール
            row.StartDateTime.setTextColor(Color.BLACK)
            row.Title.setTextColor(Color.BLACK)
            row.Location.text = cs.schedule.location
        }else{
            // 調査依頼の場合
            row.StartDateTime.setTextColor(ColorUtil.theme())
            row.Title.setTextColor(ColorUtil.theme())
            if (cs.schedule.researchTask != null) {
                row.Location.text = cs.schedule.researchTask!!.PrefName + " " + cs.schedule.researchTask!!.CityName + " " + cs.schedule.researchTask!!.TaskAddress
            }
        }

        val index = areasList!!.indexOfFirst { it.areaID == cs.areaData.areaID }
        if(index != -1 ){
            row.LineColor.setBackgroundColor(ColorUtil.getCalenderColor(index))
        }

        return view!!
    }

}

