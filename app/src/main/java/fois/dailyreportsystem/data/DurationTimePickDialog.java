package fois.dailyreportsystem.data;

import android.app.TimePickerDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.res.Resources;
import android.os.Bundle;
import android.widget.NumberPicker;
import android.widget.TextView;
import android.widget.TimePicker;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Phuong on 25/08/2017.
 */

public class DurationTimePickDialog extends TimePickerDialog {
    final OnTimeSetListener mCallback;
    TimePicker mTimePicker;
    final int interval;
    Context mcon;
    TextView textView;

    /**
     * テーマを利用する場合は、こちらのコンストラクタを利用
     *
     * @param context      コンテキスト
     * @param theme        テーマ
     * @param callBack     onTimeSetListener
     * @param hourOfDay    時
     * @param minute       分
     * @param is24HourView trueの場合は24時間表示、falseの場合はAM/PMで切り替え
     * @param interval     時間間隔
     * @param textView     埋め込むテキストビュー
     */
    public DurationTimePickDialog(Context context, int theme, OnTimeSetListener callBack, int hourOfDay, int minute, boolean is24HourView, int interval, TextView textView) {
        super(context, theme, callBack, hourOfDay, minute / interval, is24HourView);
        this.mCallback = callBack;
        this.interval = interval;
        this.mcon = context;
        this.textView = textView;
    }

    /**
     * デフォルトのテーマの場合は、こちらのコンストラクタを利用
     *
     * @param context      コンテキスト
     * @param callBack     onTimeSetListener
     * @param hourOfDay    時
     * @param minute       分
     * @param is24HourView trueの場合は24時間表示、falseの場合はAM/PMで切り替え
     * @param interval     時間間隔
     * @param textView     埋め込むテキストビュー
     */
    public DurationTimePickDialog(Context context, OnTimeSetListener callBack, int hourOfDay, int minute, boolean is24HourView, int interval, TextView textView) {
        super(context, callBack, hourOfDay, minute / interval, is24HourView);
        this.mCallback = callBack;
        this.interval = interval;
        this.mcon = context;
        this.textView = textView;
    }

    @Override
    public void onClick(DialogInterface dialog, int which) {
        switch (which) {
            case BUTTON_POSITIVE:
            if (mCallback != null && mTimePicker != null) {
                mTimePicker.clearFocus();
                mCallback.onTimeSet(mTimePicker, mTimePicker.getCurrentHour(),
                        mTimePicker.getCurrentMinute() * interval);
                int minute = mTimePicker.getCurrentMinute() * interval;
                String time = String.format("%02d", mTimePicker.getCurrentHour()) + ":" + String.format("%02d", minute);
                if (textView != null) {
                    textView.setText(time);
                }
            }
            break;
            case BUTTON_NEGATIVE:
            break;
        }
    }

    @Override
    protected void onStop() {
        // override and do nothing
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        try {
            Class<?> rClass = Class.forName("com.android.internal.R$id");
            // Field timePicker = rClass.getField("timePicker");
            this.mTimePicker = findViewById(Resources.getSystem().getIdentifier("timePicker", "id", "android"));
            int hourId = Resources.getSystem().getIdentifier("hour", "id", "android");
            int minuteId = Resources.getSystem().getIdentifier("minute", "id", "android");
            //Hour
            NumberPicker mHourSpinner = (NumberPicker) mTimePicker.findViewById(hourId);
            mHourSpinner.setMinValue(0);
            mHourSpinner.setMaxValue(23);
            List<String> displayedHourValues = new ArrayList<String>();
            for (int i = 0; i < 24; i++) {
                displayedHourValues.add(String.format("%02d", i));
            }
            mHourSpinner.setDisplayedValues(displayedHourValues.toArray(new String[0]));
            //Minute
            NumberPicker mMinuteSpinner = (NumberPicker) mTimePicker.findViewById(minuteId);
            mMinuteSpinner.setMinValue(0);
            mMinuteSpinner.setMaxValue((60 / interval) - 1);
            List<String> displayedValues = new ArrayList<String>();
            for (int i = 0; i < 60; i += interval) {
                displayedValues.add(String.format("%02d", i));
            }
            mMinuteSpinner.setDisplayedValues(displayedValues.toArray(new String[0]));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}