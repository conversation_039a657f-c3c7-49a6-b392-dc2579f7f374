package fois.dailyreportsystem.data

// 作成日：2019/09/23

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.core.content.ContextCompat
import fois.dailyreportsystem.R
import fois.dailyreportsystem.util.ColorUtil
import fois.dailyreportsystem.util.startTime
import fois.dailyreportsystem.util.toString
import java.util.*

class OrderStatusListAdapter(private val _context: Context, data: MutableList<OrderStatus>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = data
    var clickDate: Date? = null

    init {
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        var view = convertView
        val orderStatus = this.getItem(position) as OrderStatus

        if (orderStatus.isHeader) {
            view = this._inflater.inflate(R.layout.row_order_status_section, null)
            val section = view!!.findViewById(R.id.section) as TextView
            section.text = orderStatus.date!!.toString("yyyy/MM/dd(E)")
            when {
                clickDate != null && orderStatus.date == clickDate -> section.setTextColor(ContextCompat.getColor(_context, R.color.calendar_select_day))
                orderStatus.date == Date().startTime() -> section.setTextColor(ContextCompat.getColor(_context, R.color.calendar_today))
                else -> section.setTextColor(ContextCompat.getColor(_context, R.color.dark_gray))
            }
        }else {
            view = this._inflater.inflate(R.layout.row_order_list, null)
            if (orderStatus.schedule != null){
                val researchDate = view!!.findViewById(R.id.research_date) as TextView
                val statusBar = view.findViewById(R.id.status_bar) as View
                val taskName = view.findViewById(R.id.task_name) as TextView
                val address = view.findViewById(R.id.address) as TextView
                val orderStatusTextView = view.findViewById(R.id.order_status) as TextView
                val taskData = orderStatus.schedule!!.researchTask
                researchDate.text = orderStatus.date!!.toString("HH:mm～")
                taskName.text = taskData!!.TaskName + " 様邸"
                address.text = taskData.PrefName + " "+ taskData.CityName + " " + taskData.TaskAddress
                val color = ColorUtil.getSurveyStatusColor(orderStatus.schedule!!.surveyStatusID)
                statusBar.setBackgroundColor(ColorUtil.getSurveyStatusColor(orderStatus.schedule!!.surveyStatusID))
                orderStatusTextView.setBackgroundColor(color)
                orderStatusTextView.text = orderStatus.schedule!!.surveyStatusName
            }
        }

        return view
    }

}
