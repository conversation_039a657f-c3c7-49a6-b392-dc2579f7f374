package fois.dailyreportsystem.data

// 作成者：近藤
// 作成日：2017/10/13
// 更新者：近藤
// 更新日：2017/11/14

// 日報追加・変更用パラメータ、メッセージ、トークン送信パラメータ
class JsonData {

    // 共通
    var UserID: String? = null

    // 日報
    var ReportDate: String? = null
    var WorkResult: WorkData? = null
    var WorkSchedule: WorkData? = null

    // メッセージ
    var TaskID: String? = null
    var MessageGroupID: String? = null
    var MessageGroupType: String? = null    // グループタイプ（1：ユーザ（未使用）、2：邸、3：グループ）
    var MessageItems: ArrayList<MessageItems>? = null

    // トークン
    var NewDeviceID: String? = null
    var OldDeviceID: String? = null
    var DeviceType: String? = null
    var Users: ArrayList<Worker>? = null

    // 予定登録
    var ScheduleID: String? = null
    var ResearchTaskID: String? = null
    var AllDay: String? = null
    var StartDateTime: String? = null
    var EndDateTime: String? = null
    var Title: String? = null
    var Remarks: String? = null
    var Location: String? = null
    var TravelTime: String? = null
    var RepetitionID: String? = null
    var EndRepetition: String? = null
    var SurveyStatusID: String? = null
    var UserName: String? = null

    // ReserthTask
    var ResearchTask:String? = null

    // 物件調査依頼
    var PrefID: String? = null
    var CityCode: String? = null
    var OfficeID: String? = null
    var MakerID: String? = null
    var MakerUserID: String? = null
    var TaskName: String? = null
    var TaskAddress: String? = null
    var TaskTel: String? = null
    var PreferredDate1: String? = null
    var PreferredDate2: String? = null
    var PreferredDate3: String? = null
}