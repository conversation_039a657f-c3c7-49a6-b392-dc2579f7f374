package fois.dailyreportsystem.data

import android.os.Parcel
import android.os.Parcelable

/**
 * Created by Phuong on 25/09/2017.
 */
class Worker() : Parcelable {

    var UserID: String? = null
    var UserName: String? = null
    var UserPhoneNumber: String? = null
    var CompanyID: String? = null
    var CompanyName: String? = null
    var CompanyNumber: String? = null

    constructor(parcel: Parcel) : this() {
        UserID = parcel.readString()
        UserName = parcel.readString()
        UserPhoneNumber = parcel.readString()
        CompanyID = parcel.readString()
        CompanyName = parcel.readString()
        CompanyNumber = parcel.readString()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(UserID)
        parcel.writeString(UserName)
        parcel.writeString(UserPhoneNumber)
        parcel.writeString(CompanyID)
        parcel.writeString(CompanyName)
        parcel.writeString(CompanyNumber)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<Worker> {
        override fun createFromParcel(parcel: Parcel): Worker {
            return Worker(parcel)
        }

        override fun newArray(size: Int): Array<Worker?> {
            return arrayOfNulls(size)
        }
    }
}