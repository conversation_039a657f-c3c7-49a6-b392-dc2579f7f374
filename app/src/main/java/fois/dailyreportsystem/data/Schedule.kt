package fois.dailyreportsystem.data

import android.os.Parcelable
import fois.dailyreportsystem.util.removeNull
import kotlinx.android.parcel.Parcelize
import org.json.JSONObject

/**
 * スケジュールクラス.
 * 作業ごとの開始、終了、繰り返し予定を保存する。
 * @property scheduleID スケジュールID.
 * @property researchTaskID  調査タスクID.
 * @property title  スケジュールのタイトル.
 * @property latitude  緯度.
 * @property longitude  経度.
 * @property repetitionName リピート名.
 * @property location  作業場所.
 * @property userID  スケジュール作成者.
 * @property userName  .
 * @property allDay  終日フラグ(1:true,0:false).
 * @property startDateTime  予定開始日時.
 * @property endDateTime  予定終了日時.
 * @property remarks  メモ.
 * @property travelTime  移動時間.
 * @property repetitionID  繰り返しID.
 * @property endRepetition  繰り返し日時.
 * @property surveyStatusName 調査名.
 * @property surveyStatusID  状況.
 * @property surveyStatusID  状況.
 */

@Parcelize
data class Schedule(var scheduleID: String,
                    var researchTaskID: String,
                    var researchTask: ResearchTask,
                    var title: String?,
                    var location: String?,
                    var latitude: String?,
                    var longitude: String?,
                    var repetitionName: String?,
                    var userID: String?,
                    var userName: String?,
                    var allDay: String?,
                    var startDateTime: String?,
                    var endDateTime: String?,
                    var remarks: String?,
                    var travelTime: String?,
                    var repetitionID: String?,
                    var endRepetition: String?,
                    var surveyStatusName: String?,
                    var surveyStatusID: String?
) : Parcelable {

    fun getBaseModifyData(): MutableMap<String, Any?> {
        var modifyData = mutableMapOf<String, Any?>()

        modifyData["ScheduleID"] = this.scheduleID!!
        modifyData["UserID"] = this.userID!!
        modifyData["ScheduleID"] = this.scheduleID!!

        return modifyData
    }

    companion object {

        @JvmStatic
        fun init(): Schedule {
            return Schedule(scheduleID = "",
                    researchTaskID = "",
                    researchTask = ResearchTask.init(),
                    title = "",
                    location = "",
                    latitude = "",
                    longitude = "",
                    repetitionName = "",
                    userID = "",
                    userName = "",
                    allDay = "",
                    startDateTime = "",
                    endDateTime = "",
                    remarks = "",
                    travelTime = "",
                    repetitionID = "",
                    endRepetition = "",
                    surveyStatusName = "",
                    surveyStatusID = ""
            )
        }

        @JvmStatic
        fun setSchedule(row: JSONObject): Schedule {
            return  try { Schedule(
                    allDay = row.getString("AllDay").removeNull() ,
                    endDateTime = row.getString("EndDateTime").removeNull() ,
                    endRepetition = row.getString("EndRepetition").removeNull() ,
                    latitude = row.getString("Latitude").removeNull() ,
                    location = row.getString("Location").removeNull() ,
                    longitude = row.getString("Longitude").removeNull() ,
                    remarks = row.getString("Remarks").removeNull() ,
                    repetitionID = row.getString("RepetitionID").removeNull() ,
                    repetitionName = row.getString("RepetitionName").removeNull() ,
                    researchTaskID = row.getString("ResearchTaskID").removeNull() ,
                    scheduleID = row.getString("ScheduleID").removeNull() ,
                    startDateTime = row.getString("StartDateTime").removeNull() ,
                    surveyStatusID = row.getString("SurveyStatusID").removeNull() ,
                    surveyStatusName = row.getString("SurveyStatusName").removeNull() ,
                    title = row.getString("Title").removeNull() ,
                    travelTime = row.getString("TravelTime").removeNull() ,
                    userID = row.getString("UserID").removeNull() ,
                    userName = row.getString("UserName").removeNull() ,
                    researchTask = try { ResearchTask.setResearchTask(row.getJSONObject("ResearchTask")) } catch (e: Exception) { ResearchTask.init()}
            ) }catch (e: Exception){ init() }
        }
    }
}
