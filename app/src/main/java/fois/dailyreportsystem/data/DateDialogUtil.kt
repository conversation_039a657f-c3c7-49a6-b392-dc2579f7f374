package fois.dailyreportsystem.data

import android.R
import android.app.DatePickerDialog
import android.app.TimePickerDialog
import android.content.Context
import android.content.DialogInterface
import android.widget.TextView
import fois.dailyreportsystem.util.*
import java.util.*


class DateDialogUtil {


    companion object {

        private lateinit var datePickerDialog: DatePickerDialog
        private lateinit var timePickerDialog: DurationTimePickDialog

        var defaultDate = Date()
        var clearButton = true
        var dateFormat = "yyyy/MM/dd(E) HH:mm"

        fun defaultDate(date: Date): Companion {
            defaultDate = date
            return this
        }
        fun isClear(bool: Boolean): Companion {
            clearButton = bool
            return this
        }
        fun format(format: String): Companion {
            dateFormat = format
            return this
        }

        /**
         * showDateDialog 日付ダイアログ表示.
         *
         * */
        fun showDateDialog(context: Context ,dateTextView: TextView) {
            val dateString = dateTextView.text.toString()
            var (year, month, day, hour, minute) = DateData(0,0,0,0,0)
            if (dateString != "") {
                val date = dateString.toDate(dateFormat) ?: return
                year = date.year()
                month = date.month()
                day = date.day()
                hour = date.hour()
                minute = date.minute()
            }else{
                val selectDate = (defaultDate ?: Date())
                year = selectDate.year()
                month = selectDate.month()
                day = selectDate.day()
                hour = selectDate.hour()
                minute = selectDate.minute()
            }
            datePickerDialog =  DatePickerDialog(context, R.style.Theme_Holo_Light_Panel, DatePickerDialog.OnDateSetListener { _, y, m, d ->
                val date = DateUtil.dateToFormat(y, m + 1, d, hour, minute, dateFormat)
                //
                if (timeContains(dateFormat)) {
                    showTimePickerDialog(context, dateTextView, date)
                }
                // 年月日だけの場合は終了
                else{
                    dateTextView.text = date
                }
            },year,month - 1,day)
            if (clearButton) {
                datePickerDialog.setButton(DialogInterface.BUTTON_NEUTRAL, "クリア") { _, _ ->
                    dateTextView.text = ""
                }
            }
            if (!datePickerDialog.isShowing) {
                datePickerDialog.show()
            }

        }

        //TimePickerDialogを表示
        private fun showTimePickerDialog(context: Context ,dateTextView: TextView, date: String) {
            if (date == "") return
            val dateString = date.toDate(dateFormat) ?: return
            val (year, month, day, hour, minute) = DateData(dateString.year(),dateString.month(),dateString.day(),dateString.hour(),dateString.minute())
            timePickerDialog = DurationTimePickDialog(context, android.R.style.Theme_Holo_Light_Panel, TimePickerDialog.OnTimeSetListener { _, h, m ->
                dateTextView.text = DateUtil.dateToFormat(year, month, day, h, m, dateFormat)
            },hour,minute,true,30, null)
            if (clearButton) {
                timePickerDialog.setButton(DialogInterface.BUTTON_NEUTRAL, "クリア") { _, _ ->
                    dateTextView.text = ""
                }
            }
            if (!timePickerDialog.isShowing) {
                timePickerDialog.show()
            }
        }

        fun timeContains(format: String): Boolean {
            return Regex("H").containsMatchIn(format) && Regex("m").containsMatchIn(format)
        }
    }
}