package fois.dailyreportsystem.data

import android.content.Context
import android.content.SharedPreferences
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.FrameLayout
import android.widget.ImageView
import fois.dailyreportsystem.R
import fois.dailyreportsystem.util.ShowMessages

class PhotoGalleryAdapter(private val _context: Context, data: ArrayList<PhotoGallery>) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = java.util.ArrayList<PhotoGallery>()
    private var originalData = java.util.ArrayList<PhotoGallery>()

    private var imageSelectedList: ArrayList<Boolean> = ArrayList()
    private var selectCount: Int = 0

    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? =  _context.getSharedPreferences("Settings", Context.MODE_PRIVATE)

    init {
        this._data = data
        this.originalData = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        for (i in 0 until _data.size) {
            imageSelectedList.add(false)
        }
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        var convertView = convertView

        var row: PhotoGalleryRow = PhotoGalleryRow()
        val p = this.getItem(position) as PhotoGallery

        if (convertView == null) {
            convertView = this._inflater.inflate(R.layout.photo_gallery_item, null)
            row.itemLayout = convertView!!.findViewById(R.id.grid_item_layout) as FrameLayout
            row.thumbnail = convertView.findViewById(R.id.grid_item_image) as ImageView
            row.checkImage = convertView.findViewById(R.id.grid_item_check) as ImageView
            convertView.tag = row
        } else {
            row = convertView.tag as PhotoGalleryRow
        }

        row.thumbnail!!.setImageBitmap(p.bitmap)
        if (imageSelectedList[position]) {
            row.checkImage!!.visibility = View.VISIBLE
        } else {
            row.checkImage!!.visibility = View.GONE
        }

        row.itemLayout!!.setOnClickListener {
            changeSelect(position, row.checkImage!!)
        }

        return convertView
    }

    private fun changeSelect(position: Int, checkImage: ImageView) {
        // 選択状態の変更
        if (imageSelectedList[position]) {
            imageSelectedList[position] = false
            checkImage.visibility = View.GONE
            selectCount--
        } else {
            imageSelectedList[position] = true
            checkImage.visibility = View.VISIBLE
            selectCount++
        }

        // 6件目を選択した場合チェックを外す（一度に選択可能は5枚）
        if (selectCount == 6) {
            imageSelectedList[position] = false
            checkImage.visibility = View.GONE
            selectCount--
            ShowMessages().Show(_context, _context.getString(R.string.photo_selected_over))
        }

        // カンマ区切りで選択中画像の番号を保存
        var selectIndex: String = ""
        for (i in 0 until imageSelectedList.size) {
            if(imageSelectedList[i]) {
                if (selectIndex != "") {
                    selectIndex += ","
                }
                selectIndex += i.toString()
            }
        }
        sharedPreference!!.edit().putString("SELECT_INDEX", selectIndex).apply()
    }
}