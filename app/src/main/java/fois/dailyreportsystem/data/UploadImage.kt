package fois.dailyreportsystem.data

// 更新日：2018/02/05

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import androidx.core.content.FileProvider
import android.view.View
import fois.dailyreportsystem.BuildConfig
import fois.dailyreportsystem.R
import fois.dailyreportsystem.activity.report.PhotoThumbnailListActivity

import fois.dailyreportsystem.activity.report.PhotoShootListActivity
import fois.dailyreportsystem.base.BaseActivity
import java.io.File

class UploadImage(context: Context, pos: Int, Activity: String, contentID: String, contentName: String, regionName: String) : BaseActivity(), View.OnClickListener {

    // 変数宣言
    private var taken: Boolean = false
    private val RESULT_LOAD_IMAGE: Int = 1
    private var position: Int = -1
    private var ActivityName: String? = null
    private var ContentID: String? = null
    private var ContentName: String? = null
    private var RegionName: String? = null
	private var file: File? = null
    private var outputFileUri: Uri? = null

    // 設定ファイル定義
    private val sharedPreference: SharedPreferences? =  context.getSharedPreferences("Settings", Context.MODE_PRIVATE)

    init {
        BaseActivity.context = context
        position = pos
        ActivityName = Activity
        ContentID = contentID
        ContentName = contentName
        RegionName = regionName
    }

    override fun onClick(v: View) {
		if (Build.VERSION.SDK_INT > 23) {
            // Android7からフォルダ構成変更
			file = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).toString() + "/temp_image.jpg")
            outputFileUri = FileProvider.getUriForFile(context!!, BuildConfig.APPLICATION_ID + ".provider", file!!)
		} else {
			file = File(Environment.getExternalStorageDirectory().toString() + "/temp_image.jpg")
            outputFileUri = Uri.fromFile(file)
		}
        startCameraActivity()
    }

    fun startCameraActivity() {
        try {
            val intent: Intent = Intent(android.provider.MediaStore.ACTION_IMAGE_CAPTURE)
            intent.putExtra(MediaStore.EXTRA_OUTPUT, outputFileUri)
            // 設定ファイルに撮影項目の情報を保存
            sharedPreference!!.edit().putString("CONTENT_ID", ContentID).commit()
            sharedPreference.edit().putString("DETAIL_CONTENT", RegionName + "-" + ContentName).commit()
            // カメラ起動元アクティビティにより分岐
            when (ActivityName) {
                context!!.getString(R.string.photo_shoot_list_activity) -> {
                    (context as PhotoShootListActivity).startActivityForResult(intent, position)
                }
                context!!.getString(R.string.photo_thumbnail_list_activity) -> {
                    (context as PhotoThumbnailListActivity).startActivityForResult(intent, position)
                }
            }
        } catch (e: Exception) {
            log(context?.javaClass!!.name, "Exception File:" + e.message)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

    }

    protected fun onPhotoTaken(filePass: String) {
        (BaseActivity.context as PhotoShootListActivity).finish()
    }
}
