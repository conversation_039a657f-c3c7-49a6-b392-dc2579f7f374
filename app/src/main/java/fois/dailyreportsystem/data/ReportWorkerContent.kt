package fois.dailyreportsystem.data

// 作成者：近藤
// 作成日：2017/10/05
// 更新者：近藤
// 更新日：2017/10/05

import android.os.Parcel
import android.os.Parcelable

class ReportWorkerContent(): Parcelable {

    var SubTitle: String? = null            // サブタイトル
    var WorkerName: String? = null          // 作業者名
    var ContentID: String? = null           // 工程ID
    var ContentName: String? = null         // 工程名
    var contentIsCheck: Boolean = false     // 工程選択
    var type: Int = 0                           // レイアウトタイプ

    constructor(parcel: Parcel): this() {
        SubTitle = parcel.readString()
        WorkerName = parcel.readString()
        ContentID = parcel.readString()
        ContentName = parcel.readString()
        contentIsCheck = parcel.readValue(Boolean::class.java.classLoader) as Boolean
        type = parcel.readValue(Int::class.java.classLoader) as Int
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(SubTitle)
        parcel.writeString(WorkerName)
        parcel.writeString(ContentID)
        parcel.writeString(ContentName)
        parcel.writeValue(contentIsCheck)
        parcel.writeValue(type)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ReportWorkerContent> {
        override fun createFromParcel(parcel: Parcel): ReportWorkerContent {
            return ReportWorkerContent(parcel)
        }

        override fun newArray(size: Int): Array<ReportWorkerContent?> {
            return arrayOfNulls(size)
        }
    }
}