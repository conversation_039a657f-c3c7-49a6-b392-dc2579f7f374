package fois.dailyreportsystem.data

// 作成日：2017/08/17
// 更新日：2018/03/28

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import fois.dailyreportsystem.R
import fois.dailyreportsystem.activity.report.AcceptanceRequestActivity
import fois.dailyreportsystem.activity.report.ReportListActivity
import fois.dailyreportsystem.activity.report.ReportResult1Activity
import fois.dailyreportsystem.activity.report.ReportSchedule1Activity
import fois.dailyreportsystem.util.Constant
import fois.dailyreportsystem.util.ConstantParameters
import java.text.SimpleDateFormat
import java.util.*

class ReportListAdapter(private val _context: Context, data: ArrayList<Report>, taskID: String?, taskName: String?) : BaseAdapter() {
    private var _inflater: LayoutInflater
    private var _data = java.util.ArrayList<Report>()
    private var originalData = java.util.ArrayList<Report>()
    private var _taskID: String? = null
    private var _taskName: String? = null
    private var today: String? = null
    private var sdf: SimpleDateFormat = SimpleDateFormat("yyyyMMdd")

    // 日報一時保存ファイル読み込み
    private val reportTemporary: SharedPreferences = _context.getSharedPreferences("Report", Context.MODE_PRIVATE)

    init {
        this._data = data
        this.originalData = data
        this._inflater = this._context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        this._taskID = taskID
        this._taskName = taskName
        today = sdf.format(Calendar.getInstance().time)
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun getItem(position: Int): Any {
        return this._data[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }


    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val registrationInfo: RegistrationInfo = RegistrationInfo()

        var convertView = convertView

        val row: ReportRow
        val p = this.getItem(position) as Report

        if (convertView == null) {
            row = ReportRow()
            convertView = this._inflater.inflate(R.layout.row_report_list, null)
            row.reportDate = convertView!!.findViewById(R.id.report_date) as TextView
            row.reportContent =convertView.findViewById(R.id.report_work_contents) as TextView
            row.workProcessButton = convertView.findViewById(R.id.work_process_button) as ImageView
            row.scheduleButton = convertView.findViewById(R.id.report_schedule_button) as ImageView
            row.resultButton = convertView.findViewById(R.id.report_result_button) as ImageView
            row.reportSpacer = convertView.findViewById(R.id.report_list_spacer) as FrameLayout

            convertView.tag = row
        } else {
            row = convertView.tag as ReportRow
        }

        // 日報日付
        val strReportDate: String = p.ReportDate!!.trim()
        var strReportDateConverted: String = ""
        val year: Int
        val month: Int
        val day: Int

        val Date: String = p.ReportDate!!

        if (!strReportDate.isNullOrEmpty() && strReportDate.length == 8) {
            year = strReportDate.substring(0, 4).toInt()
            month = strReportDate.substring(4, 6).toInt()
            day = strReportDate.substring(6, 8).toInt()
            strReportDateConverted = ConvertReportDate(year, month, day)
        }
        row.reportDate!!.text = strReportDateConverted

        if (p.WorkScheduleStatus == 2 || p.WorkResultStatus == 2) {
            // 予定・実績が登録されている場合、作業内容を表示
            row.reportSpacer!!.visibility = View.VISIBLE
            row.reportContent!!.text = p.ContentNames
            row.reportContent!!.visibility = View.VISIBLE
        } else {
            // 予定・実績が登録されていない場合は日付のみ
            row.reportSpacer!!.visibility = View.GONE
            row.reportContent!!.visibility = View.GONE
        }

        registrationInfo.TaskID = _taskID
        registrationInfo.TaskName = _taskName
        registrationInfo.ReportDate = strReportDateConverted
        registrationInfo.ReportDateDefault = strReportDate

        // 検収依頼
        registrationInfo.WorkComplete = p.WorkComplete
        registrationInfo.ShowCompleteButton = p.ShowCompleteButton

        // 予定
        registrationInfo.WorkScheduleDate = p.WorkScheduleDetail?.Date
        registrationInfo.WeatherIDSchedule = p.WorkScheduleDetail?.WeatherID
        registrationInfo.TemperatureSchedule = p.WorkScheduleDetail?.Temperature
        registrationInfo.WorkerCountSchedule = p.WorkScheduleDetail?.WorkerCount
        registrationInfo.StartTimeSchedule = p.WorkScheduleDetail?.StartTime
        registrationInfo.EndTimeSchedule = p.WorkScheduleDetail?.EndTime
        if (p.WorkScheduleDetail?.WorkStop == 2) {
            registrationInfo.IsWorkStopSchedule = true
        }
        registrationInfo.WorkScheduleID = p.WorkScheduleID
        registrationInfo.WorkScheduleStatus = p.WorkScheduleStatus
        registrationInfo.ScheduleComment = p.WorkScheduleDetail?.Comment
        registrationInfo.ScheduleDayToEnd = p.WorkScheduleDetail?.DaysToEnd
        // 実績
        registrationInfo.WorkResultDate = p.WorkResultDetail?.Date
        registrationInfo.WeatherIDResult = p.WorkResultDetail?.WeatherID
        registrationInfo.TemperatureResult = p.WorkResultDetail?.Temperature
        registrationInfo.WorkerCountResult = p.WorkResultDetail?.WorkerCount
        registrationInfo.StartTimeResult = p.WorkResultDetail?.StartTime
        registrationInfo.EndTimeResult = p.WorkResultDetail?.EndTime
        if (p.WorkResultDetail?.WorkStop == 2) {
            registrationInfo.IsWorkStopResult = true
        }
        registrationInfo.WorkResultID = p.WorkResultID
        registrationInfo.WorkResultStatus = p.WorkResultStatus
        registrationInfo.ResultComment = p.WorkResultDetail?.Comment
        registrationInfo.ResultDayToEnd = p.WorkResultDetail?.DaysToEnd

        // ボタンタップイベント
        row.scheduleButton!!.setOnClickListener { forwardReportSchedule1(registrationInfo, p.WorkScheduleStatus!!.toInt(), Date, row.scheduleButton!!) }
        row.resultButton!!.setOnClickListener { forwardReportResult1(registrationInfo, p.WorkResultStatus!!.toInt(), Date, row.resultButton!!, p.WorkScheduleID!!) }
        row.workProcessButton?.setOnClickListener { forwardWorkProcess(registrationInfo, row.workProcessButton) }

        // ボタンの画像を設定
        // 予定
        if (p.WorkScheduleStatus == 1) {
            row.scheduleButton!!.setImageResource(R.drawable.planbefore)
        } else {
            row.scheduleButton!!.setImageResource(R.drawable.planafter)
        }

        // 実績
        if (p.WorkResultStatus == 1) {
            row.resultButton!!.setImageResource(R.drawable.resultbefore)
        } else {
            row.resultButton!!.setImageResource(R.drawable.resultafter)
        }

        if (Date.toInt() > today!!.toInt()) {
            row.scheduleButton!!.visibility = View.INVISIBLE
            row.resultButton!!.visibility = View.INVISIBLE
            row.workProcessButton!!.visibility = View.INVISIBLE
        } else {
            row.scheduleButton!!.visibility = View.VISIBLE
            row.resultButton!!.visibility = View.VISIBLE
            row.workProcessButton!!.visibility = View.VISIBLE
        }

        // 検収依頼
        row.workProcessButton!!.visibility = View.INVISIBLE
        if (p.ShowCompleteButton == Constant.ShowCompleteButton.SHOW.STATUS) {
            row.workProcessButton!!.visibility = View.VISIBLE
            setDisplayForWorkProcessButton(row, p)
        }

        return convertView
    }

    private fun setDisplayForWorkProcessButton(row: ReportRow, p: Report) {
        row.workProcessButton?.setImageResource(R.drawable.work_process_complete)
        if (p.WorkComplete == Constant.WorkComplete.COMPLETE.STATUS) {
            row.workProcessButton?.setImageResource(R.drawable.work_process_complete_blue)
        }
    }

    private fun setDisplayForWorkProcessButtonOn(button: ImageView, registrationInfo: RegistrationInfo) {
        button.setImageResource(R.drawable.work_process_complete_on)
        if (registrationInfo.WorkComplete == Constant.WorkComplete.COMPLETE.STATUS) {
            button.setImageResource(R.drawable.work_process_complete_blue_on)
        }
    }


    private fun forwardReportSchedule1(registrationInfo: RegistrationInfo, status: Int, Date: String, button: ImageView) {
        if((this._context as ReportListActivity).clickPosition == null) {
            (this._context as ReportListActivity).clickPosition = true
            reportTemporary.edit().putString("DATE", Date).apply()
            if (status == 1) {
                button.setImageResource(R.drawable.planbefore_on)
            } else {
                button.setImageResource(R.drawable.planafter_on)
            }
            val intent: Intent = Intent(this._context, ReportSchedule1Activity::class.java).apply {
                putExtra(ConstantParameters.INTENT_KEY_TASKINFO, registrationInfo)
            }
            this._context.startActivity(intent)
            (this._context as ReportListActivity).overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
            this._context.finish()
        }
    }

    private fun forwardReportResult1(registrationInfo: RegistrationInfo, status: Int, Date: String, button: ImageView, workID: String) {
        if((this._context as ReportListActivity).clickPosition == null) {
            (this._context as ReportListActivity).clickPosition = true
            reportTemporary.edit().putString("DATE", Date).apply()
            if (status == 1) {
                button.setImageResource(R.drawable.resultbefore_on)
                reportTemporary.edit().putString("WORK_ID", workID).apply()
            } else {
                button.setImageResource(R.drawable.resultafter_on)
            }
            val intent: Intent = Intent(this._context, ReportResult1Activity::class.java).apply {
                putExtra(ConstantParameters.INTENT_KEY_TASKINFO, registrationInfo)
            }
            this._context.startActivity(intent)
            (this._context as ReportListActivity).overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
            this._context.finish()
        }
    }

    private fun forwardWorkProcess(registrationInfo: RegistrationInfo, button: ImageView?) {
        if((this._context as ReportListActivity).clickPosition == null) {
            (this._context as ReportListActivity).clickPosition = true
            if (button != null) {
                setDisplayForWorkProcessButtonOn(button, registrationInfo)
            }
            val intent: Intent = Intent(this._context, AcceptanceRequestActivity::class.java).apply {
                putExtra(ConstantParameters.INTENT_KEY_TASKINFO, registrationInfo)
            }
            this._context.startActivity(intent)
            (this._context as ReportListActivity).overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
            this._context.finish()
        }
    }

    fun ConvertReportDate(year: Int, monthOfYear: Int, dayOfMonth: Int): String {
        val calendar = Calendar.getInstance()
        calendar.set(year, monthOfYear-1, dayOfMonth)
        val day: Int = calendar.get(Calendar.DAY_OF_WEEK)
        val srtDay: String
        when (day) {
            Calendar.SUNDAY -> srtDay = "日"
            Calendar.MONDAY -> srtDay = "月"
            Calendar.TUESDAY -> srtDay = "火"
            Calendar.WEDNESDAY -> srtDay = "水"
            Calendar.THURSDAY -> srtDay = "木"
            Calendar.FRIDAY -> srtDay = "金"
            Calendar.SATURDAY -> srtDay = "土"
            else -> {
                srtDay = ""
            }
        }
        // Display Selected date in textbox
        return monthOfYear.toString() + "月" + dayOfMonth + "日" + "(" + srtDay + ")"
    }
}