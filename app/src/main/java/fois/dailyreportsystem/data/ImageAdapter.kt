package fois.dailyreportsystem.data

// 作成日：2017/08/29
// 更新日：2018/10

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.graphics.Point
import androidx.viewpager.widget.PagerAdapter
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.viewpager.widget.ViewPager
import android.view.WindowManager
import android.widget.Toast

class ImageAdapter(private val context: Context, data: ArrayList<Bitmap>): PagerAdapter() {

    private var _data = java.util.ArrayList<Bitmap>()
    private var _context: Context? = null
    init {
        this._data = data
        this._context = context
    }

    override fun getCount(): Int {
        return this._data.size
    }

    override fun isViewFromObject(view: View, `object`: Any): Boolean {
        val imageView: ImageView = view as ImageView
        imageView.setOnClickListener{
            Toast.makeText(this._context, "ergt", Toast.LENGTH_LONG)
        }
        return view === `object` as ImageView
    }

    override fun instantiateItem(container: View, position: Int): Any {
        val imageView = ImageView(context)
        //val padding = context.resources.getDimensionPixelSize(R.dimen.padding_medium)
        //imageView.setPadding(padding, padding, padding, padding)
        imageView.scaleType = ImageView.ScaleType.FIT_CENTER
        //imageView.setImageBitmap(decodeSampledBitmapFromFile(this._data[position], 200, 150))
        //imageView.setImageResource(this._data[position])
        imageView.setImageBitmap(this._data[position])
        (container as ViewPager).addView(imageView, 0)
        return imageView
    }

    override fun destroyItem(container: View, position: Int, `object`: Any) {
        (container as ViewPager).removeView(`object` as ImageView)
    }

    fun decodeSampledBitmapFromFile(filePath: String, reqWidth: Int, reqHeight: Int): Bitmap {
        var orgBitmap: Bitmap
        var rezBitmap: Bitmap
        val options = BitmapFactory.Options()
        options.inJustDecodeBounds = true
        options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight)
        options.inJustDecodeBounds = false
        orgBitmap = BitmapFactory.decodeFile(filePath, options)
        rezBitmap = Bitmap.createBitmap(orgBitmap, 0, 0, orgBitmap.getWidth(), orgBitmap.getHeight())
        return rezBitmap
    }

    fun calculateInSampleSize(options: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
        // 画像の元サイズ
        val imageScaleHeight = options.outHeight.toFloat() / reqHeight
        val imageScaleWidth = options.outWidth.toFloat() / reqWidth
        var inSampleSize = 1
        if (imageScaleWidth > 1 || imageScaleHeight > 1) {
            // 縦横、大きい方に縮小するスケールを合わせる
            inSampleSize = Math.ceil((if (imageScaleWidth > imageScaleHeight) imageScaleWidth else imageScaleHeight).toDouble()).toInt()
        }
        return inSampleSize
    }



}