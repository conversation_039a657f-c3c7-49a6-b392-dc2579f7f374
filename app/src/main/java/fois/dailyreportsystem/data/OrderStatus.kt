package fois.dailyreportsystem.data

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize
import java.util.*

@Parcelize
data class OrderStatus(
    var date: Date?,
    var schedule: Schedule?,
    var isHeader: Boolean
) : Parcelable {

    companion object {

        @JvmStatic
        fun init(): OrderStatus {
            return OrderStatus(
                    date = null,
                    schedule = Schedule.init(),
                    isHeader = false
            )
        }
    }
}