package fois.dailyreportsystem.data

import android.content.Context
import com.google.gson.Gson
import fois.dailyreportsystem.util.either
import fois.dailyreportsystem.util.put
import fois.dailyreportsystem.util.workSchedule

/**
 * 工程表の検索条件
 */
@Suppress("PropertyName")
class WorkScheduleSearchConditions {
    /**
     * 邸ID
     * 1邸に限定して工程表を表示する際に指定する。邸IDを指定した場合は、他の条件は無効となる。
     */
    var TaskID = ""

    /**
     * 邸名
     */
    var TaskName = ""

    /**
     * 工事番号
     */
    var TaskWorkNo = ""

    /**
     * 着工日1
     */
    var ConstructionStart1 = ""

    /**
     * 着工日2
     */
    var ConstructionStart2 = ""

    /**
     * 完工日1
     */
    var ConstructionComp1 = ""

    /**
     * 完工日2
     */
    var ConstructionComp2 = ""

    /**
     * 着工状態 未着工
     * "0":未着工の邸は対象外とする。
     * "1":未着工の邸を対象とする。
     */
    var ConstructionStatus1 = on

    /**
     * 着工状態 着工中
     * "0":着工中の邸は対象外とする。
     * "1":着工中の邸を対象とする。
     */
    var ConstructionStatus2 = on

    /**
     * 着工状態 完工
     * "0":完工の邸は対象外とする。
     * "1":完工の邸を対象とする。
     */
    var ConstructionStatus3 = on

    /**
     * 都道府県ID
     */
    var PrefID = ""

    /**
     * 販社名1
     */
    var MakerName1 = ""

    /**
     * 販社名2
     */
    var MakerName2 = ""

    /**
     * FCT担当者
     */
    var FctUserName = ""

    /**
     * 施工業者
     */
    var ConstructionCompanyName = ""

    /**
     * 未着工フラグ
     */
    var notStarted: Boolean
        get() {
            return (ConstructionStatus1 != off)
        }
        set(value) {
            ConstructionStatus1 = value.either(on, off)
        }

    /**
     * 着工中のフラグ
     */
    var underConstruction: Boolean
        get() {
            return (ConstructionStatus2 != off)
        }
        set(value) {
            ConstructionStatus2 = value.either(on, off)
        }

    /**
     * 完工のフラグ
     */
    var completion: Boolean
        get() {
            return (ConstructionStatus3 != off)
        }
        set(value) {
            ConstructionStatus3 = value.either(on, off)
        }

    constructor(taskId: String) {
        this.TaskID = taskId
    }

    constructor(context: Context) {
        val json = context.workSchedule.getString(key, "") ?: ""
        // 別インスタンスで設定情報の復元
        val source = Gson().fromJson(json, WorkScheduleSearchConditions::class.java)

        // 必要分コピー
        copy(source ?: return)
    }

    /**
     * 更新
     */
    fun update(context: Context) {
        context.workSchedule.put(key, toJson())
    }

    /**
     * Json文字列に変換
     */
    fun toJson(): String {
        return Gson().toJson(this)
    }

    /**
     * 指定インスタンスから必要分のコピー
     */
    private fun copy(source: WorkScheduleSearchConditions) {
        TaskID = source.TaskID
        TaskName = source.TaskName
        TaskWorkNo = source.TaskWorkNo
        ConstructionStart1 = source.ConstructionStart1
        ConstructionStart2 = source.ConstructionStart2
        ConstructionComp1 = source.ConstructionComp1
        ConstructionComp2 = source.ConstructionComp2
        ConstructionStatus1 = source.ConstructionStatus1
        ConstructionStatus2 = source.ConstructionStatus2
        ConstructionStatus3 = source.ConstructionStatus3
        PrefID = source.PrefID
        MakerName1 = source.MakerName1
        MakerName2 = source.MakerName2
        FctUserName = source.FctUserName
        ConstructionCompanyName = source.ConstructionCompanyName
    }

    companion object {
        private const val key = "SearchConditions"
        private const val on = "1"
        private const val off = "0"

        /**
         * 情報のクリア
         */
        fun clear(context: Context) {
            context.workSchedule.put(key, "")
        }
    }
}
