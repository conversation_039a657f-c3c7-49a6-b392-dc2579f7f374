package fois.dailyreportsystem.data

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize
import org.json.JSONArray
import java.security.NoSuchAlgorithmException

/**
 *
 * @param areaID エリアID
 * @param areaName エリア名
 * @param officeID オフィスID
 * @param checked リストチェック済み
 *
 * */
@Parcelize
data class AreaData(
    val areaID: String,
    val areaName: String,
    val officeID: String,
    var checked : Boolean
): Parcelable {
    companion object {
        @JvmStatic
        fun toArrayList(arrayList: JSONArray): ArrayList<AreaData>?{
            val array = ArrayList<AreaData>()
            try {
                (0 until arrayList.length()).forEach { i ->
                    val row = arrayList.getJSONObject(i)
                    val rowData =  AreaData(
                            areaID = row.getString("AreaID"),
                            areaName = row.getString("AreaName"),
                            officeID = row.getString("OfficeID"),
                            checked = false
                    )
                    array.add(rowData)
                }
                return array
            } catch (e: NoSuchAlgorithmException) {
                e.printStackTrace()
                return null
            }
        }

        @JvmStatic
        fun init(): AreaData {
            return AreaData(areaID = "",
                    areaName = "",
                    officeID = "",
                    checked = false
            )
        }

        @JvmStatic
        fun mySchedule(): AreaData {
            return AreaData(
                    areaID = "0",
                    areaName = "自分の予定",
                    officeID = "0",
                    checked = true
            )
        }
    }

}