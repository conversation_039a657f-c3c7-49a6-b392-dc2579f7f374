package fois.dailyreportsystem.data

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize
import org.json.JSONArray
import org.json.JSONObject
import java.security.NoSuchAlgorithmException

@Parcelize
data class Calender(
        var BusinessDayID: String,
        var Date: String,
        var EndTime: String,
        var Remarks: String,
        var StartTime: String
) : Parcelable {

    companion object {
        @JvmStatic
        fun init(): Calender {
            return Calender(
                    BusinessDayID = "",
                    Date = "",
                    EndTime = "",
                    Remarks = "",
                    StartTime = ""
            )
        }
        @JvmStatic
        fun toArrayList(arrayList: JSONArray): ArrayList<Calender>?{
            val array = ArrayList<Calender>()
            try {
                (0 until arrayList.length()).forEach { i ->
                    val row = arrayList.getJSONObject(i)
                    val rowData = try { Calender(
                            BusinessDayID = row.getString("BusinessDayID"),
                            Date = row.getString("Date"),
                            EndTime = row.getString("EndTime"),
                            Remarks = row.getString("Remarks"),
                            StartTime = row.getString("StartTime")
                    ) }catch (e: Exception){ init() }
                    array.add(rowData)
                }
                return array
            } catch (e: NoSuchAlgorithmException) {
                e.printStackTrace()
                return null
            }
        }
    }
}