package fois.dailyreportsystem.data

import org.json.JSONObject

/**
 * 関連ファイルの情報
 */
class AttachFilesInfo {

    /**
     * タスクID
     */
    var taskID = ""

    /**
     * 関連ファイルID
     */
    var attachFileID = ""

    /**
     * 関連ファイルアイコンID
     */
    var attachFileIconID = ""

    /**
     * 関連ファイルアイコンURL
     */
    var attachFileIconUrl = ""

    /**
     * 関連ファイル拡張子
     */
    var fileExtension = ""

    /**
     * 関連ファイル名
     */
    var fileName = ""

    /**
     * 関連ファイルサイズ
     */
    var fileSize = ""

    /**
     * 内部表示するかどうか
     */
    var internalBrowsing = ""

    /**
     * 備考
     */
    var remarks = ""

    /**
     * 内部表示するかどうか
     */
    fun isInternalBrowsing(): Boolean {
        val value = internalBrowsing.toIntOrNull() ?: 0
        return value > 0
    }

    companion object {
        /**
         * JSONObjectからAttachFileを抽出
         */
        fun fromJsonObject(source: JSONObject): AttachFilesInfo {
            val item = AttachFilesInfo()
            item.taskID = source.getString("TaskID")
            item.attachFileID = source.getString("AttachFileID")
            item.attachFileIconID = source.getString("AttachFileIconID")
            item.attachFileIconUrl = source.getString("AttachFileIconUrl")
            item.fileExtension = source.getString("FileExtension")
            item.fileName = source.getString("FileName")
            item.fileSize = source.getString("FileSize")
            item.internalBrowsing = source.getString("InternalBrowsing")
            item.remarks = source.getString("Remarks")
            return item
        }
    }
}