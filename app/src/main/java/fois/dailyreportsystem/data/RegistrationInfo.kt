package fois.dailyreportsystem.data

import android.os.Parcel
import android.os.Parcelable
import fois.dailyreportsystem.data.Content
import fois.dailyreportsystem.data.WorkSchedule

/**
 * Created by Phuong on 19/09/2017.
 */
class RegistrationInfo() :  Parcelable {

    /*予定登録画面・実績登録画面の使用パラメータ*/
    //予定登録１
    var TaskID: String? = null
    var TaskName: String? = null
    var ReportDate: String? = null
    var ReportDateDefault: String? = null
    var ShowCompleteButton: String? = null
    var WorkComplete: String? = null
    var ScheduleComment: String? = null
    var ScheduleDayToEnd: Int? = null
    var ResultComment: String? = null
    var ResultDayToEnd: Int? = null
    var ListWorkContentID: String? = null

    var WorkScheduleDate: String? = null
    var WeatherIDSchedule: Int? = null
    var TemperatureSchedule: String? = null
    var WorkerCountSchedule: Int? = null
    var StartTimeSchedule: String? = null
    var EndTimeSchedule: String? = null
    var IsWorkStopSchedule: Boolean? = false
    var WorkScheduleID: String? = null
    var WorkScheduleStatus: Int? = null

    var WorkResultDate: String? = null
    var WeatherIDResult: Int? = null
    var TemperatureResult: String? = null
    var WorkerCountResult: Int? = null
    var StartTimeResult: String? = null
    var EndTimeResult: String? = null
    var IsWorkStopResult: Boolean? = false
    var WorkResultID: String? = null
    var WorkResultStatus: Int? = null
    //予定登録２・３
    var ListWorker: String? = null
    var ListWorkContent: String? = null
    var ListWorkSubContent: String? = null
    var ListSelectedWorkSubContent: String? = null

    constructor(parcel: Parcel) : this() {

        TaskID = parcel.readString()
        TaskName = parcel.readString()
        ReportDate = parcel.readString()
        ReportDateDefault = parcel.readString()
        ShowCompleteButton = parcel.readString()
        WorkComplete = parcel.readString()

        WorkScheduleDate = parcel.readString()
        WeatherIDSchedule = parcel.readValue(Int::class.java.classLoader) as? Int
        TemperatureSchedule  = parcel.readString()
        WorkerCountSchedule  = parcel.readValue(Int::class.java.classLoader) as? Int
        StartTimeSchedule  = parcel.readString()
        EndTimeSchedule  = parcel.readString()
        IsWorkStopSchedule  = parcel.readValue(Boolean::class.java.classLoader) as? Boolean
        WorkScheduleID  = parcel.readString()
        WorkScheduleStatus = parcel.readValue(Int::class.java.classLoader) as? Int
        ScheduleComment = parcel.readString()
        ScheduleDayToEnd = parcel.readValue(Int::class.java.classLoader) as? Int

        WorkResultDate = parcel.readString()
        WeatherIDResult = parcel.readValue(Int::class.java.classLoader) as? Int
        TemperatureResult = parcel.readString()
        WorkerCountResult = parcel.readValue(Int::class.java.classLoader) as? Int
        StartTimeResult = parcel.readString()
        EndTimeResult = parcel.readString()
        IsWorkStopResult = parcel.readValue(Boolean::class.java.classLoader) as? Boolean
        WorkResultID = parcel.readString()
        WorkResultStatus = parcel.readValue(Int::class.java.classLoader) as? Int
        ResultComment = parcel.readString()
        ResultDayToEnd = parcel.readValue(Int::class.java.classLoader) as? Int

        ListWorker = parcel.readString()
        ListWorkContent = parcel.readString()
        ListWorkContentID = parcel.readString()
        ListWorkSubContent = parcel.readString()
        ListSelectedWorkSubContent = parcel.readString()


    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(TaskID)
        parcel.writeString(TaskName)
        parcel.writeString(ReportDate)
        parcel.writeString(ReportDateDefault)
        parcel.writeString(ShowCompleteButton)
        parcel.writeString(WorkComplete)

        parcel.writeString(WorkScheduleDate)
        parcel.writeValue(WeatherIDSchedule)
        parcel.writeString(TemperatureSchedule)
        parcel.writeValue(WorkerCountSchedule)
        parcel.writeString(StartTimeSchedule)
        parcel.writeString(EndTimeSchedule)
        parcel.writeValue(IsWorkStopSchedule)
        parcel.writeString(WorkScheduleID)
        parcel.writeValue(WorkScheduleStatus)
        parcel.writeString(ScheduleComment)
        parcel.writeValue(ScheduleDayToEnd)

        parcel.writeString(WorkResultDate)
        parcel.writeValue(WeatherIDResult)
        parcel.writeString(TemperatureResult)
        parcel.writeValue(WorkerCountResult)
        parcel.writeString(StartTimeResult)
        parcel.writeString(EndTimeResult)
        parcel.writeValue(IsWorkStopResult)
        parcel.writeString(WorkResultID)
        parcel.writeValue(WorkResultStatus)
        parcel.writeString(ResultComment)
        parcel.writeValue(ResultDayToEnd)

        parcel.writeString(ListWorker)
        parcel.writeString(ListWorkContent)
        parcel.writeString(ListWorkContentID)
        parcel.writeString(ListWorkSubContent)
        parcel.writeString(ListSelectedWorkSubContent)

    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<RegistrationInfo> {
        override fun createFromParcel(parcel: Parcel): RegistrationInfo {
            return RegistrationInfo(parcel)
        }

        override fun newArray(size: Int): Array<RegistrationInfo?> {
            return arrayOfNulls(size)
        }
    }

}