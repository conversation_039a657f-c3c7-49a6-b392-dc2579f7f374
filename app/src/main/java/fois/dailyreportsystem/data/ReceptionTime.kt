package fois.dailyreportsystem.data

import org.json.JSONArray
import java.security.NoSuchAlgorithmException

data class ReceptionTime(
    var EndDate: String,
    var EndTime: String,
    var StartDate: String,
    var StartTime: String
) {
    companion object {
        @JvmStatic
        fun toReceptionTimes(arrayList: JSONArray) : ArrayList<ReceptionTime>? {
            val array: ArrayList<ReceptionTime> = ArrayList()
            try {
                for (i in 0 until arrayList.length()) {
                    val row = arrayList.getJSONObject(i)

                    val rowData = ReceptionTime(EndDate = row.getString("EndDate"),
                                                EndTime = row.getString("EndTime"),
                                                StartDate = row.getString("StartDate"),
                                                StartTime = row.getString("StartTime"))

                    array.add(rowData)
                }
            } catch (e: NoSuchAlgorithmException) {
                e.printStackTrace()
                return null
            }
            return array
        }
    }
}