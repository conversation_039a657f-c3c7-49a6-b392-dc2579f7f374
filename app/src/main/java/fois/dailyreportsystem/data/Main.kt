package fois.dailyreportsystem.data

// 作成日：2017/10/23
// 更新日：2018/04/06

class Main {
    var AreaName: String? = null
    var ContentVersionName: String? = null
    var FctUserName: String? = null
    var PhotoCount: String? = null
    var ShootingPhoto: String? = null
    var TaskID: String? = null
    var TaskIconAddDatetime: String? = null
    var TaskIconUrl: String? = null
    var TaskInfo: String? = null
    var TaskName: String? = null
    var TaskNameKana: String? = null
    var TaskTel: String? = null
    var TotalMessageCount: String? = null
    var UnreadMessageCount: String? = null
    var TaskPlanStartDate: String? = null
    var TaskPlanCompleteDate: String? = null
    var WorkStatus: String? = null
    var ContentNames: ArrayList<String> = ArrayList()
    var SubContentNames: ArrayList<String> = ArrayList()
    var AttachFile: String? = null

    /**
     * 関連ファイルを持っているかどうか
     */
    var hasFile: Boolean
        get() {
            val value = AttachFile?.toIntOrNull() ?: 0
            return value > 0
        }
        private set(value) {}
}