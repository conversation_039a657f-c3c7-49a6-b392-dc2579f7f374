package fois.dailyreportsystem.data

// 作成日：2017/09/25
// 更新日：2017/10/12

import android.os.Parcel
import android.os.Parcelable

class WorkContent() : Parcelable {

    var ContentID: String? = null
    var ContentName: String? = null
    var SubContentID: String? = null
    var SubContentName: String? = null
    var UsedAmountLarge: String? = null
    var UsedAmountSmall: String? = null
    var UsedFlg: String? = null
    var WorkerCompanyID: String? = null

    constructor(parcel: Parcel) : this() {
        ContentID = parcel.readString()
        ContentName = parcel.readString()
        SubContentID = parcel.readString()
        SubContentName = parcel.readString()
        UsedAmountLarge = parcel.readString()
        UsedAmountSmall = parcel.readString()
        UsedFlg = parcel.readString()
        WorkerCompanyID = parcel.readString()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(ContentID)
        parcel.writeString(ContentName)
        parcel.writeString(SubContentID)
        parcel.writeString(SubContentName)
        parcel.writeString(UsedAmountLarge)
        parcel.writeString(UsedAmountSmall)
        parcel.writeString(UsedFlg)
        parcel.writeString(WorkerCompanyID)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<WorkContent> {
        override fun createFromParcel(parcel: Parcel): WorkContent {
            return WorkContent(parcel)
        }

        override fun newArray(size: Int): Array<WorkContent?> {
            return arrayOfNulls(size)
        }
    }
}