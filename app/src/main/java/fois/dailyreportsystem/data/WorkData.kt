package fois.dailyreportsystem.data

class WorkData {
    var Comment: String? = null
    var Date: String? = null
    var DaysToEnd: String? = null
    var EndTime: String? = null
    var StartTime: String? = null
    var Temperature: String? = null
    var WeatherID: String? = null
    var WorkContents: ArrayList<WorkContent> = ArrayList()
    var WorkID: String = ""
    var WorkerCount: String? = null
    var Workers: ArrayList<Worker> = ArrayList()
}