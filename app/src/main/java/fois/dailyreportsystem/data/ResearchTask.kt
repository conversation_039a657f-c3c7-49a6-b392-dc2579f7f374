package fois.dailyreportsystem.data

import android.os.Parcelable
import fois.dailyreportsystem.util.removeNull
import kotlinx.android.parcel.Parcelize
import org.json.JSONObject
import org.parceler.Parcel

@Parcelize
data class ResearchTask(
        var AreaID: String,
        var AreaName: String,
        var CityCode: String,
        var CityName: String,
        var Direction: String,
        var MakerID: String,
        var MakerName: String,
        var MakerShortName: String,
        var MakerUserID: String,
        var MakerUserName: String,
        var OfficeID: String,
        var PointOutID: String,
        var PrefID: String,
        var PrefName: String,
        var PreferredDate1: String,
        var PreferredDate2: String,
        var PreferredDate3: String,
        var ReportDate: String,
        var Remarks: String,
        var ResearchTaskID: String,
        var TaskAddress: String,
        var TaskComplete: String,
        var TaskName: String,
        var TaskTel: String,
        var PhotoCount: String

) : Parcelable {




    fun getBaseAddData(): MutableMap<String, String> {

        val data = mutableMapOf<String, String>()
       data["PrefID"] = this.PrefID
       data["CityCode"] = this.CityCode
       data["MakerID"] = this.MakerID
       data["MakerUserID"] = this.MakerUserID
       data["OfficeID"] = this.OfficeID
       data["TaskName"] = this.TaskName
       data["TaskAddress"] = this.TaskAddress
       data["TaskComplete"] = this.TaskComplete
       data["PreferredDate1"] = this.PreferredDate1
       data["PreferredDate2"] = this.PreferredDate2
       data["PreferredDate3"] = this.PreferredDate3
       data["Remarks"] = this.Remarks
        return data
    }

    fun getBaseModifyData(): MutableMap<String, String> {

        val modifyData = mutableMapOf<String, String>()
        modifyData["ResearchTaskID"] = this.ResearchTaskID
        modifyData["PrefID"] = this.PrefID
        modifyData["CityCode"] = this.CityCode
        modifyData["MakerID"] = this.MakerID
        modifyData["MakerUserID"] = this.MakerUserID
        modifyData["OfficeID"] = this.OfficeID
        modifyData["TaskName"] = this.TaskName
        modifyData["TaskAddress"] = this.TaskAddress
        modifyData["TaskComplete"] = this.TaskComplete
        modifyData["PreferredDate1"] = this.PreferredDate1
        modifyData["PreferredDate2"] = this.PreferredDate2
        modifyData["PreferredDate3"] = this.PreferredDate3
        modifyData["Remarks"] = this.Remarks

        return modifyData
    }

    companion object {

        @JvmStatic
        fun init(): ResearchTask {
            return ResearchTask(AreaID = "",
                    AreaName = "",
                    CityCode = "",
                    CityName = "",
                    Direction = "",
                    MakerID ="",
                    MakerName = "",
                    MakerShortName = "",
                    MakerUserID = "",
                    MakerUserName = "",
                    OfficeID = "",
                    PointOutID = "",
                    PrefID = "",
                    PrefName = "",
                    PreferredDate1 = "",
                    PreferredDate2 = "",
                    PreferredDate3 = "",
                    ReportDate = "",
                    Remarks = "",
                    ResearchTaskID = "",
                    TaskAddress = "",
                    TaskComplete = "",
                    TaskName = "",
                    TaskTel = "",
                    PhotoCount = ""
            )
        }

        @JvmStatic
        fun setResearchTask(jsonResearchTask: JSONObject): ResearchTask {
            return  try {
                ResearchTask(AreaID = jsonResearchTask.getString("AreaID").removeNull(),
                        AreaName = jsonResearchTask.getString("AreaName").removeNull(),
                        CityCode = jsonResearchTask.getString("CityCode").removeNull(),
                        CityName = jsonResearchTask.getString("CityName").removeNull(),
                        Direction = jsonResearchTask.getString("Direction").removeNull(),
                        MakerID = jsonResearchTask.getString("MakerID").removeNull(),
                        MakerName = jsonResearchTask.getString("MakerName").removeNull(),
                        MakerShortName = jsonResearchTask.getString("MakerShortName").removeNull(),
                        MakerUserID = jsonResearchTask.getString("MakerUserID").removeNull(),
                        MakerUserName = jsonResearchTask.getString("MakerUserName").removeNull(),
                        OfficeID = jsonResearchTask.getString("OfficeID").removeNull(),
                        PointOutID = jsonResearchTask.getString("PointOutID").removeNull(),
                        PrefID = jsonResearchTask.getString("PrefID").removeNull(),
                        PrefName = jsonResearchTask.getString("PrefName").removeNull(),
                        PreferredDate1 = jsonResearchTask.getString("PreferredDate1").removeNull(),
                        PreferredDate2 = jsonResearchTask.getString("PreferredDate2").removeNull(),
                        PreferredDate3 = jsonResearchTask.getString("PreferredDate3").removeNull(),
                        ReportDate = jsonResearchTask.getString("ReportDate").removeNull(),
                        Remarks = jsonResearchTask.getString("Remarks").removeNull(),
                        ResearchTaskID = jsonResearchTask.getString("ResearchTaskID").removeNull(),
                        TaskAddress = jsonResearchTask.getString("TaskAddress").removeNull(),
                        TaskComplete = jsonResearchTask.getString("TaskComplete").removeNull(),
                        TaskName = jsonResearchTask.getString("TaskName").removeNull(),
                        TaskTel = jsonResearchTask.getString("TaskTel").removeNull(),
                        PhotoCount = jsonResearchTask.getString("PhotoCount").removeNull()
                )
            } catch (e: Exception){ init() }
        }
    }
}
