package fois.dailyreportsystem.util

object ConstantParameters {

    //Intentの使用パラメータ
    val INTENT_KEY_TASKID = "TaskID"
    val INTENT_KEY_TASKNAME = "TaskName"
    val INTENT_KEY_EDITMODE = "EditMode"
    val INTENT_KEY_TASKINFO = "TaskInfo"
    val INTENT_KEY_TASKINFO_TEMP = "TaskInfoTemp"
    val INTENT_KEY_IMAGEPASS = "ImagePass"
    val INTENT_KEY_RESEARCHTASKID = "ResearchTaskID"
    val INTENT_KEY_API = "Api"
    val INTENT_KEY_PARAMETER = "Parameter"
    val FILE_NAME = "FileName"
    val FILE_EXTENSION = "FileExtension"
    val VIEW_TYPE = "ViewType"

    //固定値
    val NEW_MODE: Int = 1
    val EDIT_MODE: Int = 2

    val HEADER_ROW: Int = 1
    val SUB_ROW: Int = 2
}