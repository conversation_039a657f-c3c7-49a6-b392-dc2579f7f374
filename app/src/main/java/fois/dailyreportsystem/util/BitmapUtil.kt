package fois.dailyreportsystem.util

import android.graphics.*

class BitmapUtil {
    companion object {

        fun decodeSampledBitmapFromResource(filePath: String, reqWidth: Int, reqHeight: Int ): Bitmap? {
            // inJustDecodeBounds=true で画像のサイズをチェック
            val options = BitmapFactory.Options()
            options.inJustDecodeBounds = true
            BitmapFactory.decodeFile(filePath, options)

            // inSampleSize を計算
            options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight)

            // inSampleSize をセットしてデコード
            options.inJustDecodeBounds = false
            return BitmapFactory.decodeFile(filePath, options)
        }

        fun calculateInSampleSize(options: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
            // Raw height and width of image
            val (height: Int, width: Int) = options.run { outHeight to outWidth }
            var inSampleSize = 1

            if (height > reqHeight || width > reqWidth) {

                val halfHeight: Int = height / 2
                val halfWidth: Int = width / 2

                // Calculate the largest inSampleSize value that is a power of 2 and keeps both
                // height and width larger than the requested height and width.
                while (halfHeight / inSampleSize >= reqHeight && halfWidth / inSampleSize >= reqWidth) {
                    inSampleSize *= 2
                }
            }

            return inSampleSize
        }
    }
}
