package fois.dailyreportsystem.util

// 作成日：2017/11/13
// 更新日：2018/02/06

import android.app.IntentService
import android.content.Intent

class NotificationDialogService : IntentService {
    constructor(name: String) : super(name) {}

    constructor() : super("NotificationDialogService") {}

    override fun onHandleIntent(data: Intent?) {
        val intent = Intent()
        intent.action = "NotificationDialog"
        sendBroadcast(intent)
    }
}
