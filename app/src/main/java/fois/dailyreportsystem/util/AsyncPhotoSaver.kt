package fois.dailyreportsystem.util

import android.content.Context
import android.graphics.Bitmap
import android.os.AsyncTask
import java.io.File
import java.io.FileOutputStream

// キャッシュに画像保存を行うクラス
class AsyncPhotoSaver(context: Context, photoName: String, bitmap: Bitmap) : AsyncTask<String, Int, String>() {

    val _context: Context = context
    val fileName: String = photoName
    val image: Bitmap = bitmap

    interface AsyncCallback {
        fun preExecute()
        fun postExecute()
        fun progressUpdate(progress: Int?)
        fun cancel()
    }

    override fun doInBackground(vararg _data: String): String? {
        try {
            val file: File = File(_context.cacheDir, fileName)
            val fileOutputStream: FileOutputStream = FileOutputStream(file)
            image.compress(Bitmap.CompressFormat.PNG, 100, fileOutputStream)
        } catch (e: Exception) {
            e.stackTrace
        }

        return null
    }
}