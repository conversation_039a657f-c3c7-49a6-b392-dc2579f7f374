package fois.dailyreportsystem.util

import java.io.File

object FileUtil {
    /**
     * ダウンロードディレクトリにて、ファイル名が重複しているかをチェックする。
     * 重複している場合は、ファイル名の末尾に連番を追加する。
     * 重複していない場合は、そのままのファイルパスを返する。
     *
     * @param baseDir ダウンロードディレクトリの基本ディレクトリパス
     * @param fileName オリジナルのファイル名
     * @param fileExtension ファイルの拡張子
     * @return 重複していないファイルパス
     */
    fun getUniqueFilePath(baseDir: File, fileName: String, fileExtension: String): String {
        var fileCount = 0
        var absoluteFilePath = "${baseDir.absolutePath}/$fileName$fileExtension"

        while (File(absoluteFilePath).exists()) {
            fileCount++
            absoluteFilePath = "${baseDir.absolutePath}/$fileName $fileCount$fileExtension"
        }

        return absoluteFilePath
    }
}