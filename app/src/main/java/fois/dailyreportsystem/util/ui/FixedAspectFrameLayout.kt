package fois.dailyreportsystem.util.ui

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import fois.dailyreportsystem.R

/**
 * アスペクト比を考慮したレイアウトクラス
 */
class FixedAspectFrameLayout : FrameLayout{
    private var aspectRate: Float = 0.toFloat()
    constructor(context: Context) :
            this(context, null)

    constructor(context: Context, attrs: AttributeSet?) :
            this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr){

        if (attrs == null) {
            return
        }

        //TypedArray型のインスタンスを取得する
        val a = context.obtainStyledAttributes(attrs,
                R.styleable.FixedAspectFrameLayout,0,0)
        try {
            //TypedArray内部にあるaspectRateを取得する
            aspectRate = a.getFloat(R.styleable.FixedAspectFrameLayout_aspectRate, 0f)
        }
        finally {
            a.recycle()
        }

        if (aspectRate < 0) aspectRate = 0f
    }
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val widthMode = MeasureSpec.getMode(widthMeasureSpec)
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        //横のサイズが固定モード=変更されて縦は変更されなかった場合
        if (widthMode == MeasureSpec.EXACTLY && heightMode != MeasureSpec.EXACTLY) {
            //横のサイズをaspectRateで割る
            val h = (MeasureSpec.getSize(widthMeasureSpec) / aspectRate).toInt()
            super.onMeasure(widthMeasureSpec, MeasureSpec.makeMeasureSpec(h, MeasureSpec.EXACTLY))
        //縦のサイズが固定モード=変更されて横は変更されなかった場合
        }
        else if (widthMode != MeasureSpec.EXACTLY && heightMode == MeasureSpec.EXACTLY) {
            //縦のサイズをaspectRateでかける
            val w = (MeasureSpec.getSize(heightMeasureSpec) * aspectRate).toInt()
            super.onMeasure(MeasureSpec.makeMeasureSpec(w, MeasureSpec.EXACTLY), heightMeasureSpec)
        }
        else {
            //同時に変更されたときはその値で大きさを変える
            super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        }
    }
}