package fois.dailyreportsystem.util.ui

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.LinearLayout
import android.opengl.ETC1.getHeight
import android.R.attr.top
import android.app.Activity
import android.graphics.Rect
import com.annimon.stream.operator.IntArray
import android.icu.lang.UCharacter.GraphemeClusterBreak.T
import android.widget.RelativeLayout


class DetectableSoftKeyRelativeLayout(context: Context, attrs: AttributeSet) : RelativeLayout(context, attrs) {

    interface OnSoftKeyShownListener {
        fun onSoftKeyShown(isShown: Boolean)
    }

    private var listener: OnSoftKeyShownListener? = null

    fun setListener(listener: OnSoftKeyShownListener) {
        this.listener = listener
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        // (a)Viewの高さ
        val viewHeight = View.MeasureSpec.getSize(heightMeasureSpec)
        // (b)ステータスバーの高さ
        val activity = context as Activity
        val rect = Rect()
        activity.window.decorView.getWindowVisibleDisplayFrame(rect)
        val statusBarHeight = rect.top
        // (c)ディスプレイサイズ
        val screenHeight = activity.windowManager.defaultDisplay
                .height
        // (a)-(b)-(c)>100ピクセルとなったらソフトキーボードが表示されてると判断
        //（ソフトキーボードはどんなものでも最低100ピクセルあると仮定）
        val diff = screenHeight - statusBarHeight - viewHeight
        if (listener != null) {
            listener!!.onSoftKeyShown(diff > 100)
        }
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
    }
}