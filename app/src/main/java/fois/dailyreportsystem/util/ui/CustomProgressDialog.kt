package fois.dailyreportsystem.util.ui

import android.app.Dialog
import android.content.Context
import fois.dailyreportsystem.R
import kotlinx.android.synthetic.main.progress_dialog_layout.*


class CustomProgressDialog(context: Context) : Dialog(context, R.style.Theme_CustomProgressDialog) {

    init {
        setContentView(R.layout.progress_dialog_layout)
    }

    fun setMessage(message: CharSequence) {
        messageTextView!!.text = message
    }
}
