package fois.dailyreportsystem.util.calendarview.utils;

import android.view.View;

import java.util.Calendar;

/**
 * This helper class represent a SELECTED day when calendar is in a picker date mode.
 * It is used to remember a SELECTED calendar cell.
 * <p>
 * Created by <PERSON><PERSON><PERSON> on 23.05.2017.
 */

public class SelectedDay {
    private View mView;
    private Calendar mCalendar;

    public SelectedDay(Calendar calendar) {
        mCalendar = calendar;
    }

    /**
     * @param view     View representing SELECTED calendar cell
     * @param calendar Calendar instance representing SELECTED cell date
     */
    public SelectedDay(View view, Calendar calendar) {
        mView = view;
        mCalendar = calendar;
    }

    /**
     * @return View representing SELECTED calendar cell
     */
    public View getView() {
        return mView;
    }

    public void setView(View view) {
        mView = view;
    }

    /**
     * @return Calendar instance representing SELECTED cell date
     */
    public Calendar getCalendar() {
        return mCalendar;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof SelectedDay) {
            return getCalendar().equals(((SelectedDay) obj).getCalendar());
        }

        if(obj instanceof Calendar){
            return getCalendar().equals(obj);
        }

        return super.equals(obj);
    }
}

