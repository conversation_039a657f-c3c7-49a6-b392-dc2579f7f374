package  fois.dailyreportsystem.util.calendarview.adapters;

import android.content.Context;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.annimon.stream.Stream;

import fois.dailyreportsystem.R;
import fois.dailyreportsystem.util.calendarview.CalendarView;
import fois.dailyreportsystem.util.calendarview.utils.CalendarProperties;
import fois.dailyreportsystem.util.calendarview.utils.DateUtils;
import fois.dailyreportsystem.util.calendarview.utils.DayColorsUtils;
import fois.dailyreportsystem.util.calendarview.utils.EventDayUtils;
import fois.dailyreportsystem.util.calendarview.utils.ImageUtils;
import fois.dailyreportsystem.util.calendarview.utils.SelectedDay;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import androidx.annotation.NonNull;

/**
 * This class is responsible for loading a one day cell.
 * <p>
 * Created by Mateusz Kornakiewicz on 24.05.2017.
 */

class CalendarDayAdapter extends ArrayAdapter<Date> {
    private CalendarPageAdapter mCalendarPageAdapter;
    private LayoutInflater mLayoutInflater;
    private int mPageMonth;
    private Calendar mToday = DateUtils.getCalendar();

    private CalendarProperties mCalendarProperties;

    CalendarDayAdapter(CalendarPageAdapter calendarPageAdapter, Context context, CalendarProperties calendarProperties,
                       ArrayList<Date> dates, int pageMonth) {
        super(context, calendarProperties.getItemLayoutResource(), dates);
        mCalendarPageAdapter = calendarPageAdapter;
        mCalendarProperties = calendarProperties;
        mPageMonth = pageMonth < 0 ? 11 : pageMonth;
        mLayoutInflater = LayoutInflater.from(context);
    }

    @NonNull
    @Override
    public View getView(int position, View view, @NonNull ViewGroup parent) {
        if (view == null) {
            view = mLayoutInflater.inflate(mCalendarProperties.getItemLayoutResource(), parent, false);
        }

        TextView dayLabel = (TextView) view.findViewById(R.id.dayLabel);
        TextView dayIcon = (TextView) view.findViewById(R.id.dayIcon);

        Calendar day = new GregorianCalendar();
        day.setTime(getItem(position));

        // Loading an image of the event
        if (dayIcon != null) {
            loadIcon(dayIcon, day);
        }

        setLabelColors(dayLabel, day);

        dayLabel.setText(String.valueOf(day.get(Calendar.DAY_OF_MONTH)));
        return view;
    }

    private void setLabelColors(TextView dayLabel, Calendar day) {
        // ラベルの背景を変更
        // ここから

        // Setting view for all SelectedDays
        if (isSelectedDay(day)) {
            Stream.of(mCalendarPageAdapter.getSelectedDays())
                    .filter(selectedDay -> selectedDay.getCalendar().equals(day))
                    .findFirst().ifPresent(selectedDay -> selectedDay.setView(dayLabel));

            DayColorsUtils.setSelectedDayColors(dayLabel, mCalendarProperties);
            return;
        }
        if (isToday(day)){
            DayColorsUtils.setDayColors(dayLabel, mCalendarProperties.getTodayLabelColor(),
                    Typeface.NORMAL, R.drawable.calendar_bk_today);
            return;
        }
        if (isSunday(day) && isCurrentMonthDay(day)){
            DayColorsUtils.setDayColors(dayLabel, mCalendarProperties.getSundayLabelColor(),
                    Typeface.NORMAL, R.drawable.background_transparent);
            return;
        }
        if (isSaturday(day) && isCurrentMonthDay(day)){
            DayColorsUtils.setDayColors(dayLabel, mCalendarProperties.getSaturdayLabelColor(),
                    Typeface.NORMAL, R.drawable.background_transparent);
            return;
        }

        // ここまで

        // Setting not current month day color
        if (!isCurrentMonthDay(day)) {
            DayColorsUtils.setDayColors(dayLabel, mCalendarProperties.getAnotherMonthsDaysLabelsColor(),
                    Typeface.NORMAL, R.drawable.background_transparent);
            return;
        }

        // Setting disabled days color
        if (!isActiveDay(day)) {
            DayColorsUtils.setDayColors(dayLabel, mCalendarProperties.getDisabledDaysLabelsColor(),
                    Typeface.NORMAL, R.drawable.background_transparent);
            return;
        }

        // Setting custom label color for event day
        if (isEventDayWithLabelColor(day)) {
            DayColorsUtils.setCurrentMonthDayColors(day, mToday, dayLabel, mCalendarProperties);
            return;
        }

        // Setting current month day color
        DayColorsUtils.setCurrentMonthDayColors(day, mToday, dayLabel, mCalendarProperties);
    }

    private boolean isSunday(Calendar day) {
        return day.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY;
    }

    private boolean isSaturday(Calendar day) {
        return day.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY;
    }

    private boolean isToday(Calendar day) {
        return day.equals(mToday);
    }

    private boolean isSelectedDay(Calendar day) {
        return mCalendarProperties.getCalendarType() != CalendarView.CLASSIC && day.get(Calendar.MONTH) == mPageMonth
                && mCalendarPageAdapter.getSelectedDays().contains(new SelectedDay(day));
    }

    private boolean isEventDayWithLabelColor(Calendar day) {
        return EventDayUtils.isEventDayWithLabelColor(day, mCalendarProperties);
    }

    private boolean isCurrentMonthDay(Calendar day) {
        return day.get(Calendar.MONTH) == mPageMonth &&
                !((mCalendarProperties.getMinimumDate() != null && day.before(mCalendarProperties.getMinimumDate()))
                        || (mCalendarProperties.getMaximumDate() != null && day.after(mCalendarProperties.getMaximumDate())));
    }

    private boolean isActiveDay(Calendar day) {
        return !mCalendarProperties.getDisabledDays().contains(day);
    }

    private void loadIcon(TextView dayIcon, Calendar day) {

        // イベントがなければ、dayIconを非表示にする。
        if (mCalendarProperties.getEventDays() == null || !mCalendarProperties.getEventsEnabled()) {
            dayIcon.setVisibility(View.VISIBLE);
            return;
        }

        //
        Stream.of(mCalendarProperties.getEventDays()).filter(eventDate ->
                eventDate.getCalendar().equals(day)).findFirst().executeIfPresent(eventDay -> {

            dayIcon.setText(eventDay.getImageDrawable());
//            ImageUtils.loadImage(dayIcon, eventDay.getImageDrawable());
//
//            // If a day doesn't belong to current month then image is transparent
//            if (!isCurrentMonthDay(day) || !isActiveDay(day)) {
//                dayIcon.setAlpha(0.12f);
//            }

        });
    }
}
