package fois.dailyreportsystem.util.calendarview.utils;

import android.graphics.Typeface;
import android.widget.TextView;

import fois.dailyreportsystem.R;

import java.util.Calendar;

/**
 * This class is used to set a style of calendar cells.
 * <p>
 * Created by <PERSON><PERSON><PERSON> on 24.05.2017.
 */

public class DayColorsUtils {

    /**
     * This is general method which sets a color of the text, font type and a background of a TextView object.
     * It is used to set day cell (numbers) style.
     *
     * @param textView   TextView containing a day number
     * @param textColor  A resource of a color of the day number
     * @param typeface   A type of text style, can be set as NORMAL or BOLD
     * @param background A resource of a background drawable
     */
    public static void setDayColors(TextView textView, int textColor, int typeface, int background) {
        if (textView == null) {
            return;
        }

        textView.setTypeface(null, typeface);
        textView.setTextColor(textColor);
        textView.setBackgroundResource(background);
    }

    /**
     * This method sets a color of the text, font type and a background of a TextView object.
     * It is used to set day cell (numbers) style in the case of SELECTED day (when calendar is in
     * the picker mode). It also colors a background of the selection.
     *
     * @param dayLabel           TextView containing a day number
     * @param calendarProperties A resource of a selection background color
     */
    public static void setSelectedDayColors(TextView dayLabel, CalendarProperties calendarProperties) {
        setDayColors(dayLabel, calendarProperties.getSelectionLabelColor(), Typeface.NORMAL,
                R.drawable.background_color_circle_selector);

        dayLabel.getBackground().setColorFilter(calendarProperties.getSelectionColor(),
                android.graphics.PorterDuff.Mode.MULTIPLY);
    }

    /**
     * This method is used to set a color of texts, font types and backgrounds of TextView objects
     * in a current visible month. Visible day labels from previous and forward months are set using
     * setDayColors() method. It also checks if a day number is a day number of today and set it
     * a different color and bold face type.
     *
     * @param day                A calendar instance representing day date
     * @param today              A calendar instance representing today date
     * @param dayLabel           TextView containing a day numberx
     * @param calendarProperties A resource of a color used to mark today day
     */
    public static void setCurrentMonthDayColors(Calendar day, Calendar today, TextView dayLabel,
                                                CalendarProperties calendarProperties) {
        if (today.equals(day)) {
            setDayColors(dayLabel, calendarProperties.getTodayLabelColor(), Typeface.NORMAL,
                    R.drawable.calendar_bk_today);
        } else if (EventDayUtils.isEventDayWithLabelColor(day, calendarProperties)) {
            EventDayUtils.getEventDayWithLabelColor(day, calendarProperties).executeIfPresent(eventDay ->
                    DayColorsUtils.setDayColors(dayLabel, eventDay.getLabelColor(),
                            Typeface.NORMAL, R.drawable.background_transparent));

        } else if (calendarProperties.getHighlightedDays().contains(day)) {
            setDayColors(dayLabel, calendarProperties.getHighlightedDaysLabelsColor(),
                    Typeface.NORMAL, R.drawable.background_transparent);
        } else if (day.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            setDayColors(dayLabel, calendarProperties.getSundayLabelColor(), Typeface.NORMAL,
                    R.drawable.background_transparent);
        } else if (day.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) {
            setDayColors(dayLabel, calendarProperties.getSaturdayLabelColor(), Typeface.NORMAL,
                    R.drawable.background_transparent);
        } else {
            setDayColors(dayLabel, calendarProperties.getDaysLabelsColor(), Typeface.NORMAL,
                    R.drawable.background_transparent);
        }
    }
}
