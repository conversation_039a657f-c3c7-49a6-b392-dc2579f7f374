package fois.dailyreportsystem.util.calendarview;

import android.graphics.drawable.Drawable;
import androidx.annotation.DrawableRes;
import androidx.annotation.RestrictTo;

import fois.dailyreportsystem.data.PlanScheduleDayData;
import fois.dailyreportsystem.data.CalendarSchedule;
import fois.dailyreportsystem.util.calendarview.utils.DateUtils;

import java.util.Calendar;
import java.util.List;

/**
 * This class represents an event of a day. An instance of this class is returned when user click
 * a day cell. This class can be overridden to make calendar more functional. A list of instances of
 * this class can be passed to CalendarView object using setEvents() method.
 * <p>
 * Created by <PERSON><PERSON><PERSON> on 23.05.2017.
 */

public class EventDay {
    private Calendar mDay;
    private String mDrawable;
    private int mLabelColor;
    private boolean mIsDisabled;

    private List<CalendarSchedule> mScheduleData;

    /**
     * @param day Calendar object which represents a date of the event
     */
    public EventDay(Calendar day) {
        mDay = day;
    }

    /**
     * @param day      Calendar object which represents a date of the event
     * @param drawable Drawable resource which will be displayed in a day cell
     */
    public EventDay(Calendar day, String drawable, List<CalendarSchedule> scheduleData) {
        DateUtils.setMidnight(day);
        mDay = day;
        mDrawable = drawable;
        mScheduleData = scheduleData;
    }


    /**
     * @param day        Calendar object which represents a date of the event
     * @param drawable   Drawable resource which will be displayed in a day cell
     * @param labelColor Color which will be displayed as label text color a day cell
     * @param scheduleData day of schedule data
     */
    public EventDay(Calendar day, String drawable , int labelColor, List<CalendarSchedule> scheduleData) {
        DateUtils.setMidnight(day);
        mDay = day;
        mDrawable = drawable;
        mLabelColor = labelColor;
        mScheduleData = scheduleData;
    }

    /**
     * @return An image resource which will be displayed in the day row
     */
    @RestrictTo(RestrictTo.Scope.LIBRARY)
    public String getImageDrawable() {
        return mDrawable;
    }

    /**
     * @return Color which will be displayed as row label text color
     */
    @RestrictTo(RestrictTo.Scope.LIBRARY)
    public int getLabelColor() {
        return mLabelColor;
    }


    /**
     * @return Calendar object which represents a date of current event
     */
    public Calendar getCalendar() {
        return mDay;
    }


    /**
     * @return Boolean value if day is not disabled
     */
    public boolean isEnabled() {
        return !mIsDisabled;
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    public void setEnabled(boolean enabled) {
        mIsDisabled = enabled;
    }

    /**
     * @return day of EventList
     */
    public List<CalendarSchedule> getScheduleData() {
        return mScheduleData;
    }


}
