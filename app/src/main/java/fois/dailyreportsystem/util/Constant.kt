package fois.dailyreportsystem.util

// 作成日：2017/08/14
// 更新日：2018/04/12

import org.apache.commons.codec.binary.Base64

import java.security.NoSuchAlgorithmException

// 定数を定める
object Constant {

    // ログ出力制御 true：ログ出力可、false：ログ出力不可（リリース用）
    val DEBUG: Boolean = false

    // 本番用URL
    val URL: String = "https://fct-api.e-benrida.net/117/"

    // テスト
    //val URL: String = "https://fct-api-st.e-benrida.net/112/"

    var REQUEST_FORM = "application/x-www-form-urlencoded"

    // Gallery保存先名
    const val DIRECTORY_NAME = "日報カンシン"
    // 拡張子
    const val IMAGE_FILE_EXTENSION = ".jpg"

    // 言語設定
    val LANG: String = "JA"

    // 暗号化用
    val ENCODE_KEY: String = "123456"
    val ENCODE_IV: String = "abcdef"

    // 写真サイズ（フルHD）
    val PHOTO_LONG: Int = 1920
    val PHOTO_SHORT: Int = 1080

    const val FORMAT_DATETIME_SLASH = "yyyy/MM/dd HH:mm"
    const val FORMAT_DATETIME_WEEK_SLASH = "yyyy/MM/dd(E) HH:mm"
    const val FORMAT_DATE_SLASH = "yyyy/MM/dd"
    const val FORMAT_DATETIME_HYPHEN = "yyyy-MM-dd HH:mm"
    const val FORMAT_DATE_HYPHEN = "yyyy-MM-dd"

    // 権限
    val PERMISSION_REQUEST_CAMERA: Int = 1      // カメラ
    val PERMISSION_REQUEST_STORAGE: Int = 2     // ストレージ
    val PERMISSION_REQUEST_BOTH: Int = 3        // 両方
    val PERMISSION_REQUEST_GALLERY: Int = 4     // ストレージ（ギャラリー用）

    var RESPONSE_ERROR = "{\"Status\":{\"StatusCode\":-1,\"StatusMsg\":\"サーバへの接続に失敗しました\"}}"

    // トップの画面切替のID
    const val FRAGMENT_SCREEN_MAIN = 1 //メイン画面
    const val FRAGMENT_SCREEN_PLAN = 2 // 予定一覧
    const val FRAGMENT_SCREEN_REQUEST = 3 // 調査依頼
    const val FRAGMENT_SCREEN_REQUEST_LIST = 4 // 依頼状況
    const val FRAGMENT_SCREEN_SURVEY = 5 // 調査物件一覧
    const val FRAGMENT_SCREEN_WORKSCHEDULE = 6 // 工程表検索
    const val FRAGMENT_SCREEN_NONE = 99 //


    const val WORKER = "Worker"   // 職長
    const val MAKER = "Maker"    // 販売店
    const val FCT = "FCT"    // フジケミ様

    enum class OrderStatusCode(val status: String) {
        COMPLETE("1"),  // 報告書出力済
        RESEARCHED("2"),  // 調査済
        ACCEPTED("3"),  // 受付済
        APPROVAL_WAITING("4"),  // 承認待ち
        REQUESTING("5"),  // 依頼中
        ACCEPTING_REQUEST("6"),  // 依頼受中
        PROVISIONAL("7"),  // 仮受付
        READJUSTMENT("8"),  // 再調整
    }

    // API URL
    val TasksURL: String = "api.svc/Tasks"                            // 邸一覧
    val UnreadCountURL: String = "api.svc/MessageUnreadCount"       // 未読件数
    val ReportsURL: String = "api.svc/Reports"                        // 日報一覧
    val AuthenticateURL: String = "api.svc/Authenticate"            // 認証（初期設定、ログインその他）
    val ContentsURL: String = "api.svc/Contents"                    // 工程一覧（予定、実績登録）
    val SubContentsURL: String = "api.svc/SubContents"            // 内容一覧
    val ScheduleURL: String = "api.svc/Schedule"                    // 登録した予定
    val ResultURL: String = "api.svc/Result"                        // 登録した実績
    val AddScheduleURL: String = "api.svc/AddSchedule"            // 予定登録
    val AddResultURL: String = "api.svc/AddResult"                    // 実績登録
    val ProcessURL: String = "api.svc/ShootingRegions"            // 工程一覧
    val PhotographyURL: String = "api.svc/ShootingContents"        // 撮影項目一覧
    val PhotosURL: String = "api.svc/Photos"                        // サムネール一覧、写真詳細用
    val AddPhotoURL: String = "gh/AddPhoto.ashx"                    // 写真登録
    val DeletePhotoURL: String = "api.svc/DeletePhoto"            // 写真削除
    val MessagesGroupsURL: String = "api.svc/MessagesGroups"        // メッセージグループ
    val MessagesURL: String = "api.svc/Messages"                    // メッセージ詳細（メッセージグループからの遷移）
    val TaskMessagesURL: String = "api.svc/TaskMessages"            // メッセージ詳細（邸一覧からの遷移）
    val AddMessageURL: String = "api.svc/AddMessage"                // メッセージ送信
    val UpdateDeviceIDURL: String = "api.svc/UpdateDeviceID"        // トークン送信
    val User: String = "api.svc/User"                                 // ユーザ
    val UpdateUserPhoneMail: String = "api.svc/UpdateUserPhoneMail"  // ユーザ携帯メールアドレス変更

    val UserScheduledURL: String = "api.svc/UserSchedules"        // ユーザースケジュール
    val AreaScheduledURL: String = "api.svc/AreaSchedules"        // エリアスケジュール
    val Areas: String = "api.svc/Areas"        // エリア一覧
    val UserAreas: String = "api.svc/UserAreas"        // エリアユーザー一覧
    var Clalender: String = "api.svc/Calender"                     // カレンダー
    var CalenderSchedule: String = "api.svc/CalenderSchedule" // カレンダー追加
    var CalendarAddScheduleURL: String = "api.svc/CalenderAddSchedule" // カレンダー追加
    var CalenderModifyScheduleURL: String = "api.svc/CalenderModifySchedule" // カレンダー更新
    var CalenderDeleteScheduleURL: String = "api.svc/CalenderDeleteSchedule" // カレンダー削除
    var OrderResearchTasks: String = "api.svc/OrderResearchTasks"  // 調査物件一覧（並び済み）
    var ResearchTask: String = "api.svc/ResearchTask" // 物件詳細
    var ResearchTaskMakers: String = "api.svc/ResearchTaskMakers" //販売店一覧
    var ResearchTaskMakerUsers: String = "api.svc/ResearchTaskMakerUsers" //販売店一覧
    var ResearchTaskModify: String = "api.svc/ResearchTaskModify"
    var ResearchTaskPointOutItems: String = "api.svc/ResearchTaskPointOutItems" // 診断部位
    var ResearchPhotoAdd = "gh/ResearchPhotoAdd.ashx"
    var ResearchPhotos = "api.svc/ResearchPhotos"
    var ResearchPhotoModify = "api.svc/ResearchPhotoModify"
    var ResearchTaskReschedule = "api.svc/ResearchTaskReschedule"
    var ResearchPhotoModifySelected = "api.svc/ResearchPhotoModifySelected"
    var ResearchTaskUserIsIncluded = "api.svc/ResearchTaskUserIsIncluded"
    var ResearchPhoto = "api.svc//ResearchPhoto"
    var GetResearchPhoto = "gh/ResearchPhoto.ashx"
    var ResearchPhotoDelete = "api.svc/ResearchPhotoDelete"
    var AreaAvailability = "api.svc/AreaAvailability"
    var ResearchTaskReceptionTime = "api.svc/ResearchTaskReceptionTime"
    var Regions = "api.svc/Regions"
    var Prefs = "api.svc/Prefs"
    var Citys = "api.svc/Citys"
    var ResearchTaskAdd = "api.svc/ResearchTaskAdd"
    var ResearchTaskUsers = "api.svc/ResearchTaskUsers"
    var ResearchTaskUserAdd = "api.svc/ResearchTaskUserAdd"
    var ResearchTaskCount = "api.svc/ResearchTaskCount"
    var ResearchTasks = "api.svc/ResearchTasks"
    var LinkMovieSite = "api.svc/LinkMovieSite"

    var ResearchTaskUserChoice = "api.svc/ResearchTaskUserChoice"
    var ResearchTaskUserDelete = "api.svc/ResearchTaskUserDelete"

    val AttachFilesURL: String = "api.svc/AttachFiles"                        // ファイル一覧
    val AttachFile: String = "gh/AttachFile.ashx"                        // ファイル取得

    val WorkSchedule = "gh/WorkSchedule.ashx"                           // 工程表
    val WorkProcess = "api.svc/WorkProcess"                             // 検収依頼
    var CompleteWorkProcess = "api.svc/CompleteWorkProcess"             // 検収依頼情報登録

    //  TravelTime key = セレクト形式メニューの位置、value = ドメイン値
    val travelTime = mutableMapOf(0 to "0", 1 to "5", 2 to "15", 3 to "30", 4 to "60", 5 to "90", 6 to "120")

    // 写真撮影　調査物件 右回り、左回り
    enum class ResearchTaskDirection(val DIRECTION: String) {
        LEFT("1"),
        RIGHT("2"),
    }

    // 撮影写真　予備用　出力用
    enum class ResearchTaskPhotoType(val SELECTED: String) {
        SPARE("0"),
        REPORT("1"),
        ALL("2"),
    }

    // 検収依頼の完了ボタン　表示　非表示
    enum class ShowCompleteButton(val STATUS: String) {
        SHOW("1"),
        NOTSHOW("0")
    }

    // 検収依頼　完了　不完全
    enum class WorkComplete(val STATUS: String) {
        COMPLETE("1"),
        NOTCOMPLETE("0")
    }

    fun GetAES(plaintext: String, key: String, iv: String): String? {
        val keyb = getByteEncodeMd5(key)
        val ivb = getByteEncodeMd5(iv)
        val plainTextb = plaintext.toByteArray()
        val aEs128 = AES128()
        try {
            val bEncript = aEs128.encrypt(plainTextb, keyb!!, ivb!!)
            val str = String(Base64.encodeBase64(bEncript))
            return str
        } catch (e: Exception) {
            return null
        }

    }

    fun getByteEncodeMd5(s: String): ByteArray? {
        try {
            // Create MD5 Hash
            val digest = java.security.MessageDigest.getInstance("MD5")
            digest.update(s.toByteArray())
            val messageDigest = digest.digest()
            return messageDigest

        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
            return null
        }

    }
}
