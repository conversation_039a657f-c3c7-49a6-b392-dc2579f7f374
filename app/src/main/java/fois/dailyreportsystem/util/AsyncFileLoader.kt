package fois.dailyreportsystem.util

// 作成日：2020/06/23
// 更新日：2020/06/23 Ito

import android.content.Context
import android.os.AsyncTask
import org.json.JSONException
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.net.HttpURLConnection
import java.net.MalformedURLException
import java.net.URL


// 通信処理を行うクラス
class AsyncFileLoader(context: Context, _asyncCallback: AsyncCallback, params: Map<String, String>) : AsyncTask<String, Int, ByteArray>() {

    interface AsyncCallback {
        fun preExecute()
        fun postExecute(result: ByteArray?)
        fun progressUpdate(progress: Int?)
        fun cancel()
    }

    private var mContext: Context? = null
    private var mAsyncCallback: AsyncCallback? = null
    private var params: Map<String, String>

    init {
        mContext = context
        mAsyncCallback = _asyncCallback
        this.params = params
    }

    override fun onPreExecute() {
        super.onPreExecute()
        mAsyncCallback!!.preExecute()
    }

    override fun onProgressUpdate(vararg _progress: Int?) {
        super.onProgressUpdate(*_progress)
        mAsyncCallback!!.progressUpdate(_progress[0])
    }

    override fun onPostExecute(_result: ByteArray?) {
        super.onPostExecute(_result)
        mAsyncCallback!!.postExecute(_result)
    }

    override fun onCancelled() {
        super.onCancelled()
        mAsyncCallback!!.cancel()
    }

    override fun doInBackground(vararg _data: String): ByteArray? {
        val urlConnection: HttpURLConnection?
        val boundary: String = "FileLoader"
        val code: String = "UTF-8"
        try {
            urlConnection = URL(_data[0]).openConnection() as HttpURLConnection
            urlConnection.connectTimeout = 20000
            urlConnection.readTimeout = 20000
            urlConnection.setRequestProperty("Connection", "Keep-Alive")
            urlConnection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary)
            urlConnection.requestMethod = "POST"
            urlConnection.instanceFollowRedirects = false
            urlConnection.setChunkedStreamingMode(0)
            urlConnection.doInput = true
            urlConnection.doOutput = true
            urlConnection.connect()

            val dataOutputStream: ByteArrayOutputStream = ByteArrayOutputStream()

            for ((key, value) in params) {
                dataOutputStream.write(("--$boundary\r\n").toByteArray(charset(code)))
                dataOutputStream.write(("Content-Disposition: form-data;").toByteArray(charset(code)))
                dataOutputStream.write(("name=\"$key\"\r\n\r\n").toByteArray(charset(code)))
                dataOutputStream.write((value + "\r\n").toByteArray(charset(code)))
            }

            dataOutputStream.write(("--$boundary--\r\n").toByteArray(charset(code)))

            val connectionOutputStream = urlConnection.outputStream
            connectionOutputStream.write(dataOutputStream.toByteArray())
            connectionOutputStream.flush()

            val inputStream = urlConnection.inputStream
            val byteArrayOutputStream = ByteArrayOutputStream()
            var nRead = -1
            val defaultSize = 16384
            val data = ByteArray(defaultSize)

            try {
                do {
                    nRead = inputStream.read(data, 0, defaultSize)
                    if (nRead == -1) {
                        break
                    }

                    byteArrayOutputStream.write(data, 0, nRead)
                } while (true)

                return byteArrayOutputStream.toByteArray()

            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                inputStream.close()
                urlConnection.disconnect()
            }

            Thread.sleep(200)

        } catch (e: MalformedURLException) {
            e.printStackTrace()
        } catch (e: IOException) {
            e.printStackTrace()
        } catch (e: JSONException) {
            e.printStackTrace()
        } catch (e: InterruptedException) {
            e.printStackTrace()
        }

        return null
    }
}