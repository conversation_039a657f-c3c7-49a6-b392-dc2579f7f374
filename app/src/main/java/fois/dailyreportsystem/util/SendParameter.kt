package fois.dailyreportsystem.util

import com.google.gson.Gson

// 作成者：近藤
// 作成日：2107/09/01
// 更新者：近藤
// 更新日：2017/11/14

// 通信用パラメータ設定用
@Suppress("PropertyName")
class SendParameter {
    // 設定関連
    var CompanyNumber: String? = null       // 会社コード
    var PhoneNumber: String? = null         // 電話番号
    var Password: String? = null            // パスワード

    var UserPhoneMail: String? = null      // 携帯メールアドレス

    // 共通
    var TaskID: String? = null              // 邸ID
    var UserID: String? = null              // ユーザID

    // 日報関連取得用
    var ContentID: String? = null           // 工程ID（予定・実績用）
    var WorkID: String? = null              // 作業ID

    // 日報登録、メッセージ送信用、トークン送信用
    var JsonData: String? = null

    // 写真関連取得用
    var RegionID: String? = null            // 工程ID（撮影用）
    var PhotoID: String? = null             // 写真ID

    // メッセージ関連取得用
    var MessageGroupID: String? = null    // メッセージグループID

    // 予定一覧
    var TargetUserID: String? = null
    var Year: String? = null
    var Month: String? = null
    var Day: Int? = null

    // エリア一覧
    var OfficeID: String? = null

    // エリアスケジュール
    var AreaID: String? = null

    // 予定編集で利用
    var ScheduleID: String? = null

    // タスクID
    var ResearchTaskID: String? = null

    // 調査物件写真詳細変更
    var Selected: String? = null
    var Remarks: String? = null

    // 空き状況
    var CityCode: String? = null

    // 市町村
    var PrefID: String? = null

    // 再調整数カウントに利用
    var SurveyStatusID: String? = null

    // メーカー
    var MakerID: String? = null

    // YYYYMMDD
    var Date: String? = null

    /**
     * ファイルID
     */
    var AttachFileID: String? = null

    /**
     * Json文字列に変換
     */
    fun toJson(): String {
        return Gson().toJson(this)
    }

    /**
     * パラメータマップに変換(リフレクションを使用するとメソッド数が大幅に増えるためベタ実装)
     */
    fun toMap(): Map<String, String> {
        return listOf(
                Pair("CompanyNumber", CompanyNumber ?: ""),
                Pair("PhoneNumber", PhoneNumber ?: ""),
                Pair("Password", Password ?: ""),
                Pair("UserPhoneMail", UserPhoneMail ?: ""),
                Pair("TaskID", TaskID ?: ""),
                Pair("UserID", UserID ?: ""),
                Pair("ContentID", ContentID ?: ""),
                Pair("WorkID", WorkID ?: ""),
                Pair("JsonData", JsonData ?: ""),
                Pair("RegionID", RegionID ?: ""),
                Pair("PhotoID", PhotoID ?: ""),
                Pair("MessageGroupID", MessageGroupID ?: ""),
                Pair("TargetUserID", TargetUserID ?: ""),
                Pair("Year", Year ?: ""),
                Pair("Month", Month ?: ""),
                Pair("Day", (Day == null).either("", Day.toString())),
                Pair("OfficeID", OfficeID ?: ""),
                Pair("AreaID", AreaID ?: ""),
                Pair("ScheduleID", ScheduleID ?: ""),
                Pair("ResearchTaskID", ResearchTaskID ?: ""),
                Pair("Selected", Selected ?: ""),
                Pair("Remarks", Remarks ?: ""),
                Pair("CityCode", CityCode ?: ""),
                Pair("PrefID", PrefID ?: ""),
                Pair("SurveyStatusID", SurveyStatusID ?: ""),
                Pair("MakerID", MakerID ?: ""),
                Pair("AttachFileID", AttachFileID ?: "")
        )
                .filter { it.second.isNotEmpty() }
                .associate { it.first to it.second }
    }

    /**
     * パラメータマップに変換(リフレクションを使用するとメソッド数が大幅に増えるためベタ実装)
     */
    fun toMap(keepEmpty: String): Map<String, String> {
        return listOf(
            Pair("CompanyNumber", CompanyNumber ?: ""),
            Pair("PhoneNumber", PhoneNumber ?: ""),
            Pair("Password", Password ?: ""),
            Pair("UserPhoneMail", UserPhoneMail ?: ""),
            Pair("TaskID", TaskID ?: ""),
            Pair("UserID", UserID ?: ""),
            Pair("ContentID", ContentID ?: ""),
            Pair("WorkID", WorkID ?: ""),
            Pair("JsonData", JsonData ?: ""),
            Pair("RegionID", RegionID ?: ""),
            Pair("PhotoID", PhotoID ?: ""),
            Pair("MessageGroupID", MessageGroupID ?: ""),
            Pair("TargetUserID", TargetUserID ?: ""),
            Pair("Year", Year ?: ""),
            Pair("Month", Month ?: ""),
            Pair("Day", (Day == null).either("", Day.toString())),
            Pair("OfficeID", OfficeID ?: ""),
            Pair("AreaID", AreaID ?: ""),
            Pair("ScheduleID", ScheduleID ?: ""),
            Pair("ResearchTaskID", ResearchTaskID ?: ""),
            Pair("Selected", Selected ?: ""),
            Pair("Remarks", Remarks ?: ""),
            Pair("CityCode", CityCode ?: ""),
            Pair("PrefID", PrefID ?: ""),
            Pair("SurveyStatusID", SurveyStatusID ?: ""),
            Pair("MakerID", MakerID ?: ""),
            Pair("AttachFileID", AttachFileID ?: "")
        )
            .filter { it.first == keepEmpty || it.second.isNotEmpty() }
            .associate { it.first to it.second }
    }

    companion object {
        /**
         * Json文字列から復元
         */
        fun fromJson(json: String): SendParameter {
            return Gson().fromJson(json, SendParameter::class.java)
        }
    }
}