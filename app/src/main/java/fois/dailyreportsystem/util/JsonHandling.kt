package fois.dailyreportsystem.util

import android.R.attr.defaultValue
import fois.dailyreportsystem.data.*
import fois.dailyreportsystem.data.ResearchTask.Companion.setResearchTask
import org.json.JSONArray
import org.json.JSONObject
import kotlin.collections.ArrayList
import java.security.NoSuchAlgorithmException


/**
 * Created by Phuong on 21/09/2017.
 */
object JsonHandling {
    fun toWorkerArrayList(workers: String): ArrayList<Worker>? {
        var return_array: ArrayList<Worker> = ArrayList<Worker>()
        try {
            if (!workers.isNullOrEmpty()) {
                val worker_array = JSONArray(workers)
                for (i in 0..worker_array.length() - 1) {
                    val worker_row = worker_array.getJSONObject(i)
                    var worker: Worker = Worker()
                    worker.CompanyID = worker_row.getString("CompanyID")
                    worker.CompanyName = worker_row.getString("CompanyName")
                    worker.CompanyNumber = worker_row.getString("CompanyNumber")
                    worker.UserID = worker_row.getString("UserID")
                    worker.UserName = worker_row.getString("UserName")
                    worker.UserPhoneNumber = worker_row.getString("UserPhoneNumber")
                    return_array.add(worker)
                }
            }
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
            return null
        }
        return return_array
    }

    fun toWorkContentArrayList(workcontent: String): ArrayList<WorkContent>? {
        var return_array: ArrayList<WorkContent> = ArrayList<WorkContent>()
        try {
            if (!workcontent.isNullOrEmpty()) {
                val workContent_array = JSONArray(workcontent)
                for (i in 0..workContent_array.length() - 1) {
                    val workContent_row = workContent_array.getJSONObject(i)
                    var workContent: WorkContent = WorkContent()
                    workContent.ContentID = workContent_row.getString("ContentID")
                    workContent.ContentName = workContent_row.getString("ContentName")
                    workContent.SubContentID = workContent_row.getString("SubContentID")
                    workContent.SubContentName = workContent_row.getString("SubContentName")
                    workContent.WorkerCompanyID = workContent_row.getString("WorkerCompanyID")
                    return_array.add(workContent)
                }
            }
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
            return null
        }
        return return_array
    }

    fun toSubContentArrayList(subcontents: String): ArrayList<Content>? {
        var return_array: ArrayList<Content> = ArrayList<Content>()
        try {
            if (!subcontents.isNullOrEmpty()) {
                val subcontent_array = JSONArray(subcontents)
                for (i in 0..subcontent_array.length() - 1) {
                    val subcontent_row = subcontent_array.getJSONObject(i)
                    var subContent: Content = Content()
                    subContent.ContentID = subcontent_row.getString("ContentID")
                    subContent.SubContentID = subcontent_row.getString("SubContentID")
                    subContent.SubContentName = subcontent_row.getString("SubContentName")
                    subContent.UsedFlg = subcontent_row.getInt("UsedFlg")
                    subContent.ContentType = ConstantParameters.SUB_ROW
                    return_array.add(subContent)
                }
            }
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
            return null
        }
        return return_array
    }

    fun toCheckSubContentArrayList(subcontents: String): ArrayList<Content>? {
        var return_array: ArrayList<Content> = ArrayList<Content>()
        try {
            if (!subcontents.isNullOrEmpty()) {
                val subcontent_array = JSONArray(subcontents)
                for (i in 0..subcontent_array.length() - 1) {
                    val subcontent_row = subcontent_array.getJSONObject(i)
                    var subContent: Content = Content()
                    subContent.ContentID = subcontent_row.getString("ContentID")
                    subContent.SubContentID = subcontent_row.getString("SubContentID")
                    subContent.SubContentName = subcontent_row.getString("SubContentName")
                    subContent.UsedFlg = subcontent_row.getInt("UsedFlg")
                    subContent.isCheck = subcontent_row.getBoolean("isCheck")
                    subContent.ContentType = ConstantParameters.SUB_ROW
                    return_array.add(subContent)
                }
            }
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
            return null
        }
        return return_array
    }

    fun toScheduleArrayList(arrayList: JSONArray): ArrayList<Schedule>?{
        var array: ArrayList<Schedule> = ArrayList<Schedule>()
        try {
            for (i in 0 until arrayList.length()) {
                val row = arrayList.getJSONObject(i)

                val researchTask = setResearchTask(row.getJSONObject("ResearchTask"))

                val rowData = Schedule(
                        allDay = row.getString("AllDay"),
                        endDateTime = row.getString("EndDateTime"),
                        endRepetition = row.getString("EndRepetition"),
                        latitude = row.getString("Latitude"),
                        location = row.getString("Location"),
                        longitude = row.getString("Longitude"),
                        remarks = row.getString("Remarks"),
                        repetitionID = row.getString("RepetitionID"),
                        repetitionName = row.getString("RepetitionName"),
                        researchTaskID = row.getString("ResearchTaskID"),
                        researchTask = researchTask,
                        scheduleID = row.getString("ScheduleID"),
                        startDateTime = row.getString("StartDateTime"),
                        surveyStatusID = row.getString("SurveyStatusID"),
                        surveyStatusName = row.getString("SurveyStatusName"),
                        title = row.getString("Title"),
                        travelTime = row.getString("TravelTime"),
                        userID = row.getString("UserID"),
                        userName = row.getString("UserName"))

                array.add(rowData)
            }
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
            return null
        }
        return array
    }

    fun toIntOrDefaultValue(jsonObject: JSONObject, key: String, defaultValue: Int?): Int? {
        return try {
            jsonObject.getInt(key)
        } catch (e: Exception) {
            defaultValue
        }
    }
}