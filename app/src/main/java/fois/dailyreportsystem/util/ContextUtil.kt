package fois.dailyreportsystem.util

import android.app.Activity
import android.content.ContentValues
import android.content.Context
import android.content.SharedPreferences
import android.content.res.Resources
import android.graphics.Bitmap
import android.media.ExifInterface
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.util.TypedValue
import android.view.View
import android.view.inputmethod.InputMethodManager
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import fois.dailyreportsystem.data.AreaData
import fois.dailyreportsystem.data.City
import fois.dailyreportsystem.data.ResearchTask
import org.json.JSONObject
import java.io.File
import java.io.FileOutputStream

fun Context.saveAccount(user_data: JSONObject){
    // 各種データを設定ファイルに保存
    val sp = this.settings
    // 会社ID
    sp.put("COMPANY_ID", user_data.getString("CompanyID"))
    // 会社名
    sp.put("COMPANY_NAME", user_data.getString("CompanyName"))
    sp.put("COMPANY_NAME_SELECT", user_data.getString("CompanyName"))
    // 会社コード
    sp.put("COMPANY_NUMBER", user_data.getString("CompanyNumber"))
    sp.put("COMPANY_NUMBER_SELECT", user_data.getString("CompanyNumber"))
    // ユーザID
    sp.put("USER_ID", user_data.getString("UserID"))
    // ユーザ名
    sp.put("USER_NAME", user_data.getString("UserName"))
    // ログインユーザーのオフィスを設定
    sp.put("OFFICE_ID", user_data.getString("OfficeID"))
    // ログインユーザー権限を設定
    sp.put("COMPANY_ROLE", user_data.getString("CompanyRole"))
    // 調査員フラグ
    val flgResearch = user_data.getString("FlgResearch") ?: "0"
    sp.put("FLAG_RESEARCH", if(flgResearch.isNotBlank()){flgResearch}else{"0"})
    // 正社員フラグ
    val flgStaff =  user_data.getString("FlgStaff") ?: "0"
    sp.put("FLAG_STAFF", if(flgStaff.isNotBlank()){flgStaff}else{"0"})

    // 電話番号・パスワード保存
    sp.put("PHONE_NUMBER", sp.getString("PHONE_NUMBER_INPUT"))
    // TODO:画面ごとにAES暗号化済みのパスワードで取り扱っている・いない混在しているので、済み用Settingを新設(いずれ一本化したい)
    sp.put("PASSWORD", sp.getString("PASSWORD_INPUT_AES"))

    // 現在の時間を保存
    sp.put("LAST_HANDLE", System.currentTimeMillis())

    // 入力データを空にする
    sp.put("PHONE_NUMBER_INPUT", "")
    sp.put("PASSWORD_INPUT", "")
    sp.put("PASSWORD_INPUT_AES", "")

    // スケジュール空き状況はデフォルト選択なしにする。
    sp.remove("SELECT_CITY")
    sp.remove("SELECT_AREAS")

    // 調査物件一覧のフィルタ設定をなしにする。
    sp.remove("SURVEY_LIST_FILTER")

    // タブの位置情報を消す
    sp.remove("RE_SETUP_FRAGMENT")

    // 全てのリストの位置をリセット
    this.position.clear()
}

fun Context.isSaveAccount(): Boolean{
    val sp = this.settings
    return (sp.getString("COMPANY_ID").isNotBlank()
            && sp.getString("USER_ID").isNotBlank()
            && sp.getString("OFFICE_ID").isNotBlank()
            && sp.getString("COMPANY_ROLE").isNotBlank()
            && sp.getString("FLAG_RESEARCH").isNotBlank()
            && sp.getString("FLAG_STAFF").isNotBlank()
            )
}


fun Context.saveCity(data :City){
    val sp : SharedPreferences = getSharedPreferences(SETTINGS, Context.MODE_PRIVATE)
    sp.edit().putString("SELECT_CITY", Gson().toJson(data)).apply()
}
fun Context.loadCity(): City?{
    try {
        val sp : SharedPreferences = getSharedPreferences(SETTINGS, Context.MODE_PRIVATE)
        val city = sp.getString("SELECT_CITY", null) ?: return null
        val type = object : TypeToken<City>() {}.type
        return Gson().fromJson(city, type)
    }catch (e: Exception){
        return null
    }
}
fun Context.saveAreas(data: MutableList<AreaData>){
    val sp : SharedPreferences = getSharedPreferences(SETTINGS, Context.MODE_PRIVATE)
    sp.edit().putString("SELECT_AREAS", Gson().toJson(data)).apply()
}
fun Context.loadAreas(): MutableList<AreaData>?{
    try {
        val sp: SharedPreferences = getSharedPreferences(SETTINGS, Context.MODE_PRIVATE)
        val data = sp.getString("SELECT_AREAS", null) ?: return null
        val type = object : TypeToken<MutableList<AreaData>>() {}.type
        return Gson().fromJson(data, type)
    }catch (e: Exception){
        return null
    }
}

fun Context.saveResearchTask(data: ResearchTask){
    settings.put("EDIT_RESEARCH_TASK", Gson().toJson(data))
}
fun Context.loadResearchTask(): ResearchTask?{
    try {
        val data = settings.getString("EDIT_RESEARCH_TASK", null) ?: return null
        val type = object : TypeToken<ResearchTask>() {}.type
        return Gson().fromJson(data, type)
    }catch (e: Exception){
        return null
    }
}
fun Context.removeResearchTask(){
    settings.remove("EDIT_RESEARCH_TASK")
}

fun Context.savePointOut(category: String, item: String) {
    settings.put("POINT_OUT_CATEGORY_ID", category)
    settings.put("POINT_OUT_ITEM_ID", item)
}

fun Context.loadPointOut(): Pair<String, String> {
    val category = settings.getString("POINT_OUT_CATEGORY_ID")
    val item = settings.getString("POINT_OUT_ITEM_ID")
    return category to item
}

fun Context.removePointOut() {
    settings.remove("POINT_OUT_CATEGORY_ID")
    settings.remove("POINT_OUT_ITEM_ID")
}

fun Context.isSameAccount(user_data: JSONObject): Boolean {
    val companyId = user_data.getString("CompanyID")
    val userId = user_data.getString("UserID")
    return isSameAccount(companyId, userId)
}

fun Context.isSameAccount(companyId: String, userId: String): Boolean {
    val prevCompanyId = settings.getString("COMPANY_ID")
    val prevUserId = settings.getString("USER_ID")
    return ((prevCompanyId == companyId) && (prevUserId == userId))
}

val Context.SETTINGS: String get() = "Settings"
val Context.REPORT: String get() = "Report"
val Context.POSITION: String get() = "Position"
val Context.WORKSCHEDULE: String get() = "WorkSchedule"
val Context.TIME_INTERVAL: Long get() = 3 * 60 * 60 * 1000 // テスト用（ X 秒）
//val Context.TIME_INTERVAL: Long get() = 5 * 1000 // テスト用（ X 秒）

val Context.settings: SharedPreferences get() = getSharedPreferences(SETTINGS, Context.MODE_PRIVATE)// 設定
val Context.report: SharedPreferences get() = getSharedPreferences(REPORT, Context.MODE_PRIVATE)//
val Context.position: SharedPreferences get() = getSharedPreferences(POSITION, Context.MODE_PRIVATE)// リスト遷移位置
val Context.workSchedule: SharedPreferences get() = getSharedPreferences(WORKSCHEDULE, Context.MODE_PRIVATE)// 検索条件

fun SharedPreferences.getString(key: String): String = getString(key,"") ?:""
fun SharedPreferences.getBoolean(key: String): Boolean = getBoolean(key, false)
fun SharedPreferences.getInt(key: String): Int = getInt(key, 0)
fun SharedPreferences.clear() = edit().clear().apply()
fun SharedPreferences.remove(key: String) = edit().remove(key).apply()

fun SharedPreferences.put(key: String, value: Any?){
    if (value != null) {
        when (value) {
            is String -> edit().putString(key, value).apply()
            is Boolean -> edit().putBoolean(key, value).apply()
            is Long -> edit().putLong(key, value).apply()
            is Int -> edit().putInt(key, value).apply()
            is Float -> edit().putFloat(key, value).apply()
        }
    }
}

fun Context.hideKeyboard(view: View) {
    val inputMethodManager = getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
    inputMethodManager.hideSoftInputFromWindow(view.windowToken, 0)
    view.requestFocus()
}

fun Context.toDimensionTextSize(size: Float): Float {
    val r: Resources = this.resources
    return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_SP, size, r.displayMetrics)
}

// ログイン確認
fun Context.loginCheck(): Boolean{
    // 現在時刻の取得
    val currentTime: Long = System.currentTimeMillis()
    // 前回ログイン・操作時刻の取得
    val lastHandle: Long = this.settings.getLong("LAST_HANDLE", 0)


    if(!this.isSaveAccount()){
        // ログインフラグを変更
        this.settings.put("LOGIN_FLAG", false)

        return false
    }
    // タイムアウトチェック
    return if (currentTime - lastHandle < TIME_INTERVAL) {
        // 3時間未満の場合、現在の時間を保存
        this.settings.put("LAST_HANDLE", System.currentTimeMillis())
        this.settings.put("LOGIN_FLAG", true)
        true
    } else {
        // ログインフラグを変更
        this.settings.put("LOGIN_FLAG", false)
        // 3時間以上経過している場合false
        false
    }
}

fun Context.getUriToPath(uri: Uri): File? {
    val projection = arrayOf(MediaStore.MediaColumns.DATA)
    val cursor = this.contentResolver.query(uri, projection, null, null, null) ?: return null
    var path: String? = null
    if (cursor.moveToFirst()) {
        path = cursor.getString(0)
    }
    cursor.close()
    return File(path)
}


fun Context.getImageRotation( imagePath : String ): String {
    try {
        var orientation = ExifInterface(imagePath).getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_UNDEFINED)
        return this.rotation(orientation)
    } catch (e: Exception) {
        e.printStackTrace()
        return "0"
    }
}

fun Context.getImageRotation(uri: Uri, imagePath: String): String {
    try {
        var orientation = 0
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            this.contentResolver.openInputStream(uri).use { stream ->
                stream?.let {
                    ExifInterface(it).run {
                        orientation = this.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_UNDEFINED)
                    }
                }
            }
        }else{
            orientation = ExifInterface(imagePath).getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_UNDEFINED)
        }
        return this.rotation(orientation)
    } catch (e: Exception) {
        e.printStackTrace()
        return "0"
    }
}


fun Context.rotation( orientation: Int): String {
    when (orientation) {
        ExifInterface.ORIENTATION_UNDEFINED,
        ExifInterface.ORIENTATION_NORMAL -> { }
        ExifInterface.ORIENTATION_FLIP_HORIZONTAL -> return "2"
        ExifInterface.ORIENTATION_ROTATE_180 -> return "3"
        ExifInterface.ORIENTATION_FLIP_VERTICAL -> return "4"
        ExifInterface.ORIENTATION_TRANSVERSE -> return "5"
        ExifInterface.ORIENTATION_ROTATE_90 -> return "6"
        ExifInterface.ORIENTATION_TRANSPOSE -> return "7"
        ExifInterface.ORIENTATION_ROTATE_270 -> return "8"
    }
    return "0"
}
fun Context.getFile(fileName: String): File{
    return File(this.filesDir, fileName)
}
fun Context.removeFile(fileName: String): Boolean{
    return File(this.filesDir, fileName).delete()
}
// ギャラリーに画像ファイルを保存する
fun Context.setGallery(
        bitmap : Bitmap,
        fileName: String,
        completion: (Uri) -> Unit
) {
    // ギャラリーへの反映時に必要な情報を格納している値です
    // AndroidQ以降とそれより前で設定可能なKeyが異なるため、
    // ここでは共通部分のみ生成してif文内でそれぞれに必要な値を追加しています
    val values = createContentValues(fileName)
    val uri: Uri

    if (Build.VERSION_CODES.Q <= Build.VERSION.SDK_INT) {
        values.apply {
            // 画像の保存先を指定します（AndroidQより前と指定方法が異なります）
            put(
                    MediaStore.Images.Media.RELATIVE_PATH,
                    "${Environment.DIRECTORY_PICTURES}/${Constant.DIRECTORY_NAME}"
            )
            put(MediaStore.MediaColumns.IS_PENDING, 1)
        }

        this.contentResolver.run {
            uri = insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values) ?: return

            // AndroidQ以降では画像の書き出し前にギャラリーへの登録(正確に言うとMediaStoreへの登録)を
            // 済ませてから該当Uriに画像を書き出します（処理の順番がAndroidQより前と異なります）
            openOutputStream(uri).use { bitmap.compress(Bitmap.CompressFormat.JPEG, 100, it) }
            values.put(MediaStore.MediaColumns.IS_PENDING, 0)
            update(uri, values, null, null)
        }
    } else {

        // スクリーンショット画像書き出し用のディレクトリ・ファイルを準備
        val directory = File(
                // ファイルの書き出し先はいくつか候補がありますが、
                // アプリを削除してもファイルが消えない、外部アプリからアクセス可能という理由から、
                // getExternalStoragePublicDirectoryを選択しています
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),
                Constant.DIRECTORY_NAME
        )
        if (!directory.exists()) directory.mkdirs()
        val file = File(directory, "$fileName${Constant.IMAGE_FILE_EXTENSION}")

        // Bitmapをファイルに書き出します
        FileOutputStream(file).use { bitmap.compress(Bitmap.CompressFormat.JPEG, 100, it) }

        // 書き出した画像をギャラリーに反映させています
        this.contentResolver.run {
            values.put(MediaStore.Images.Media.DATA, file.absolutePath)
            uri = insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values) ?: return

        }
    }

    completion(uri)

}
/**
 * [Build.VERSION_CODES.Q]以上とそれより前で設定できる値が違うため、ここでは共通部分のみ設定
 */
private fun createContentValues(name: String) = ContentValues().apply {
    put(MediaStore.Images.Media.DISPLAY_NAME, "$name${Constant.IMAGE_FILE_EXTENSION}")
    put(MediaStore.Images.Media.TITLE, name)
    put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
    put(MediaStore.Images.Media.DATE_ADDED, System.currentTimeMillis() / 1_000)
}

fun Context.setFileImage(
    bitmap : Bitmap,
    fileName: String,
    completion: (Uri) -> Unit
) {
    // ギャラリーへの反映時に必要な情報を格納している値です
    // AndroidQ以降とそれより前で設定可能なKeyが異なるため、
    // ここでは共通部分のみ生成してif文内でそれぞれに必要な値を追加しています
    val values = createContentValues(fileName)
    val uri: Uri

    if (Build.VERSION_CODES.Q <= Build.VERSION.SDK_INT) {
        values.apply {
            // 画像の保存先を指定します（AndroidQより前と指定方法が異なります）
            put(
                MediaStore.Images.Media.RELATIVE_PATH,
                "${Environment.DIRECTORY_PICTURES}/${Constant.DIRECTORY_NAME}"
            )
            put(MediaStore.MediaColumns.IS_PENDING, 1)
        }

        this.contentResolver.run {
            uri = insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values) ?: return

            // AndroidQ以降では画像の書き出し前にギャラリーへの登録(正確に言うとMediaStoreへの登録)を
            // 済ませてから該当Uriに画像を書き出します（処理の順番がAndroidQより前と異なります）
            openOutputStream(uri).use { bitmap.compress(Bitmap.CompressFormat.JPEG, 85, it) }
            values.put(MediaStore.MediaColumns.IS_PENDING, 0)
//            update(uri, values, null, null)
        }
    } else {

        // スクリーンショット画像書き出し用のディレクトリ・ファイルを準備
        val directory = File(
            // ファイルの書き出し先はいくつか候補がありますが、
            // アプリを削除してもファイルが消えない、外部アプリからアクセス可能という理由から、
            // getExternalStoragePublicDirectoryを選択しています
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),
            Constant.DIRECTORY_NAME
        )
        if (!directory.exists()) directory.mkdirs()
        val file = File(directory, "$fileName${Constant.IMAGE_FILE_EXTENSION}")

        // Bitmapをファイルに書き出します
        FileOutputStream(file).use { bitmap.compress(Bitmap.CompressFormat.JPEG, 85, it) }

        // 書き出した画像をギャラリーに反映させています
        this.contentResolver.run {
//            values.put(MediaStore.Images.Media.DATA, file.absolutePath)
            uri = insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values) ?: return

        }
    }

    completion(uri)

}
