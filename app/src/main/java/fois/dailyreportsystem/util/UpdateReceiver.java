package fois.dailyreportsystem.util;

// 作成日：2017/09/27
// 更新日：2018/01/22

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;

public class UpdateReceiver extends BroadcastReceiver {

    public static Handler handler;

    @Override
    public void onReceive(Context context, Intent intent) {
        Bundle bundle = intent.getExtras();
        String messageGroupID = bundle.getString("MessageGroupID");
        String taskID = bundle.getString("TaskID");
        String unreadCount = bundle.getString("UnreadCount");

        if (handler != null) {
            Message msg = new Message();
            Bundle data = new Bundle();
            data.putString("MessageGroupID", messageGroupID);
            data.putString("TaskID", taskID);
            data.putString("UnreadCount", unreadCount);
            msg.setData(data);
            handler.sendMessage(msg);
        }
    }

    public void registerHandler(Handler locationUpdateHandler) {
        handler = locationUpdateHandler;
    }

}
