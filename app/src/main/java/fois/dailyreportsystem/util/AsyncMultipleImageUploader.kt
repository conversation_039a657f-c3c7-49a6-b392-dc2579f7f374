package fois.dailyreportsystem.util

// 作成日：2022/07/07

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.media.ExifInterface
import android.net.Uri
import android.os.AsyncTask
import android.os.Build
import org.json.JSONException
import org.json.JSONObject
import java.io.*
import java.net.HttpURLConnection
import java.net.MalformedURLException
import java.net.URL
import java.util.*
import androidx.core.content.FileProvider
import fois.dailyreportsystem.BuildConfig


// 通信処理を行うクラス
class AsyncMultipleImageUploader(context: Context, _asyncCallback: AsyncCallback, params : Dictionary<String, String>) : AsyncTask<String, Int, JSONObject>() {

    var mParam :Dictionary<String, String>


    interface AsyncCallback {
        fun preExecute()
        fun postExecute(result: JSONObject?)
        fun progressUpdate(progress: Int?)
        fun cancel()
    }

    private var mContext: Context? = null
    private var mAsyncCallback: AsyncCallback? = null


    init {
        mContext = context
        mAsyncCallback = _asyncCallback
        mParam = params
    }

    override fun onPreExecute() {
        super.onPreExecute()
        mAsyncCallback!!.preExecute()
    }

    override fun onProgressUpdate(vararg _progress: Int?) {
        super.onProgressUpdate(*_progress)
        mAsyncCallback!!.progressUpdate(_progress[0])
    }

    override fun onPostExecute(_result: JSONObject?) {
        super.onPostExecute(_result)
        mAsyncCallback!!.postExecute(_result)
    }

    override fun onCancelled() {
        super.onCancelled()
        mAsyncCallback!!.cancel()
    }

    override fun doInBackground(vararg _data: String): JSONObject? {
        val urlConnection: HttpURLConnection?
        val BOUNDARY: String = "DailyReportAddPhoto"
        val CHAR: String = "UTF-8"
        try {
            urlConnection = URL(_data[0]).openConnection() as HttpURLConnection
            urlConnection.connectTimeout = 20000
            urlConnection.readTimeout = 20000
            urlConnection.setRequestProperty("Connection", "Keep-Alive")
            urlConnection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY)
            urlConnection.requestMethod = "POST"
            urlConnection.instanceFollowRedirects = false
            urlConnection.setChunkedStreamingMode(0)
            urlConnection.doInput = true
            urlConnection.doOutput = true
            urlConnection.connect()


            /* Android7以降は読み込み先が異なる */
            val file = File(_data[1])
            val outputFileUri = if (Build.VERSION.SDK_INT >23) {
                FileProvider.getUriForFile(mContext!!, BuildConfig.APPLICATION_ID + ".provider", file).also {
                    mContext!!.grantUriPermission(mContext!!.packageName, it, Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }
            } else {
                Uri.fromFile(file)
            }
            mContext!!.contentResolver.openInputStream(outputFileUri).use { input ->
                val cacheFile = File(mContext!!.cacheDir, file.name)
                cacheFile.outputStream().use {  output ->
                    input!!.copyTo(output)
                }

                var bitmap: Bitmap = BitmapFactory.decodeFile(cacheFile.path)

                val matrix: Matrix = Matrix()
                var rotate: Boolean = false
                when (_data[2]) {
                    ExifInterface.ORIENTATION_ROTATE_180.toString() -> {
                        matrix.setRotate(180f, bitmap.width.toFloat(), bitmap.height.toFloat())
                        rotate = true
                    }
                    ExifInterface.ORIENTATION_ROTATE_90.toString() -> {
                        matrix.setRotate(90f, bitmap.width.toFloat(), bitmap.height.toFloat())
                        rotate = true
                    }
                    ExifInterface.ORIENTATION_ROTATE_270.toString() -> {
                        matrix.setRotate(-90f, bitmap.width.toFloat(), bitmap.height.toFloat())
                        rotate = true
                    }
                }
                if (rotate) {
                    bitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
                }
                val imageOutputStream: ByteArrayOutputStream = ByteArrayOutputStream()
                bitmap.compress(Bitmap.CompressFormat.JPEG, 85, imageOutputStream)

                val byteArrayOutputStream: ByteArrayOutputStream = ByteArrayOutputStream()

                val keys = mParam.keys()
                while (keys.hasMoreElements()) {
                    val key = keys.nextElement() as String
                    byteArrayOutputStream.write(("--" + BOUNDARY + "\r\n").toByteArray(charset(CHAR)))
                    byteArrayOutputStream.write(("Content-Disposition: form-data;").toByteArray(charset(CHAR)))
                    byteArrayOutputStream.write(("name=\"$key\"\r\n\r\n").toByteArray(charset(CHAR)))
                    byteArrayOutputStream.write((mParam[key] + "\r\n").toByteArray(charset(CHAR)))
                }

                // 画像
                byteArrayOutputStream.write(("--" + BOUNDARY + "\r\n").toByteArray(charset(CHAR)))
                byteArrayOutputStream.write(("Content-Disposition: form-data;").toByteArray(charset(CHAR)))
                byteArrayOutputStream.write(("name=\"Photo\"; filename=\"testImage\"\r\n").toByteArray(charset(CHAR)))
                byteArrayOutputStream.write(("Content-Type: image/jpeg\r\n\r\n").toByteArray(charset(CHAR)))
                byteArrayOutputStream.write(imageOutputStream.toByteArray())
                byteArrayOutputStream.write(("\r\n").toByteArray(charset(CHAR)))

                byteArrayOutputStream.write(("--" + BOUNDARY + "--\r\n").toByteArray(charset(CHAR)))

                val outputStream = urlConnection.outputStream
                outputStream.write(byteArrayOutputStream.toByteArray())
                outputStream.flush()

                val inputStream = urlConnection.inputStream

                val stringBuffer = StringBuffer()
                var readStr: String?
                val bufferedReader = BufferedReader(InputStreamReader(inputStream, "UTF-8"))
                readStr = bufferedReader.readLine()
                while (readStr != null) {
                    stringBuffer.append(readStr)
                    readStr = bufferedReader.readLine()
                }
                val str = stringBuffer.toString()
                val jsonObject = JSONObject(str)

                try {
                    return jsonObject
                } catch (e: Exception) {
                    e.printStackTrace()
                } finally {
                    outputStream.close()
                    inputStream.close()
                    bufferedReader.close()
                    urlConnection.disconnect()
                }

                Thread.sleep(200)

            }

        } catch (e: MalformedURLException) {
            e.printStackTrace()
        } catch (e: IOException) {
            e.printStackTrace()
        } catch (e: JSONException) {
            e.printStackTrace()
        } catch (e: InterruptedException) {
            e.printStackTrace()
        }

        return null
    }
}