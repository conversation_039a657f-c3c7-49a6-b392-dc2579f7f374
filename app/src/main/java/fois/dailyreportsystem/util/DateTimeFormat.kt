package fois.dailyreportsystem.util

import java.security.NoSuchAlgorithmException
import java.text.Format

/**
 * Created by Phuong on 21/09/2017.
 */
object DateTimeFormat {
    fun formatTime(time: String): String? {
        var strtime: String = ""
        try {
            strtime = time.trim()
            if (strtime.length == 4) {
                val strHour: String = strtime.substring(0, 2)
                val strMinute: String = strtime.substring(2, 4)
                strtime = strHour + ":" + strMinute
            }
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
            return null
        }
        return strtime
    }
}