package fois.dailyreportsystem.util

import android.content.Context
import androidx.core.content.ContextCompat
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import fois.dailyreportsystem.R

class FooterItem(private val _context: Context, text: Int, normalImageId: Int, tapImageId: Int) {
    private var icon: ImageView = ImageView(_context)
    private var disp_name: TextView = TextView(_context)
    private var normalImage: Int = normalImageId
    private var tapImage: Int = tapImageId
    val layout = LinearLayout(_context)

    init {
        var layoutParamLinear  =   LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, 100)
        layoutParamLinear.gravity = Gravity.CENTER

        layout.layoutParams = layoutParamLinear
        layout.orientation = LinearLayout.VERTICAL

        icon.layoutParams = LinearLayout.LayoutParams(100,50)
        icon.setImageResource(normalImage)

        disp_name.text = _context.getString(text)
        disp_name.layoutParams = LinearLayout.LayoutParams(100, 50)

        layout.addView(icon)
        layout.addView(disp_name)
    }

    fun getFooterItem() : LinearLayout {
        return layout
    }

    fun changeImage(image: String) {
        if (image == "on") {
            icon.setImageResource(normalImage)
            disp_name.setTextColor(ContextCompat.getColor(_context,  R.color.white))
        } else {
            icon.setImageResource(tapImage)
            disp_name.setTextColor(ContextCompat.getColor(_context, R.color.half_transparent))
        }
    }
}