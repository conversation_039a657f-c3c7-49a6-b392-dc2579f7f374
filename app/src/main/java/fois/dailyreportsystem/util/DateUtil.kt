package fois.dailyreportsystem.util

import me.mattak.moment.Moment
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*

/*
 * 拡張関数
 */
fun String.toDate(pattern: String = "yyyy-MM-dd HH:mm"): Date? {
    val sdFormat = try {
        SimpleDateFormat(pattern, Locale.JAPAN)
    } catch (e: IllegalArgumentException) {
        null
    }
    val date = sdFormat?.let {
        try {
            it.parse(this)
        } catch (e: ParseException) {
            null
        }
    }
    return date
}
fun Date.toString(pattern: String): String  =  SimpleDateFormat(pattern, Locale.JAPAN).format(this)
fun Date.toInt(pattern: String): Int = Integer.parseInt(this.toString(pattern))
fun Date.year(): Int   = this.toInt("yyyy") //年
fun Date.month(): Int  = this.toInt("M") //月
fun Date.day(): Int    = this.toInt("d") //日
fun Date.hour(): Int   = this.toInt("H") //時
fun Date.minute(): Int = this.toInt("m") //分

fun Date.addMinutes(minutes: Int): Date = Date(this.time + (1000 * 60 * minutes)) //+分
fun Date.addMonth(month: Int): Date {
    val calendar = this.toCalendar()
    calendar.add(Calendar.MONTH, month)
    return calendar.time
}
fun Date.addDay(day: Int): Date{
    val calendar = this.toCalendar()
    calendar.add(Calendar.DAY_OF_YEAR, day)
    return calendar.time
}
fun Date.startWeek(): Date{
    val calendar = this.toCalendar()
    calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY)
    return calendar.time.startTime()
}
fun Date.endWeek(): Date{
    val calendar = this.toCalendar()
    calendar.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY)
    return calendar.time.endTime()
}
fun Date.setTime(hour: Int = 0, minute: Int = 0, second: Int = 0): Date{
    val calendar = this.toCalendar()
    calendar.set(Calendar.HOUR, hour)
    calendar.set(Calendar.MINUTE, minute)
    calendar.set(Calendar.SECOND, second)
    return calendar.time
}

fun Date.setHour(hour: Int): Date{
    val calendar = this.toCalendar()
    calendar.set(Calendar.HOUR, hour)
    return calendar.time
}
fun Date.setMinute(minute: Int): Date{
    val calendar = this.toCalendar()
    calendar.set(Calendar.MINUTE, minute)
    return calendar.time
}
fun Date.startTime(): Date {
    val calendar = this.toCalendar()
    calendar.set(Calendar.HOUR_OF_DAY, 0)
    calendar.set(Calendar.MINUTE, 0)
    calendar.set(Calendar.SECOND, 0)
    calendar.set(Calendar.MILLISECOND, 0)
    return calendar.time
}

fun Date.endTime(): Date {
    val calendar = this.toCalendar()
    calendar.set(Calendar.HOUR_OF_DAY, 23)
    calendar.set(Calendar.MINUTE, 59)
    calendar.set(Calendar.SECOND, 59)
    calendar.set(Calendar.MILLISECOND, 59)
    return calendar.time
}
fun Date.roundTime(): Date {
    val calendar = this.toCalendar()
    if (this.minute() > 30) {
        calendar.set(Calendar.MINUTE, 30)
    } else {
        calendar.set(Calendar.MINUTE, 0)
    }
    return calendar.time
}

fun Date.toCalendar(): Calendar {
    val calendar = Calendar.getInstance()
    calendar.time = this
    return calendar
}

fun Int.toTravel(): String {
    var result = ""
    val hour = this / 60
    var displayMinutes = this
    if (hour > 0) {
        result = hour.toString() + "時間"
        displayMinutes = this - (hour * 60)
    }
    if (displayMinutes > 0) {
        result += displayMinutes.toString() + "分"
    }else{
        result += "なし"
    }
    return "移動時間：$result"
}

/**
 * 日付に関するユーティリティ機能を提供します.
 */
class DateUtil {

    companion object {
        /**
         * 現在日付を指定の書式で取得する
         *
         * @param pattern 日付書式パターン
         */
        @JvmStatic
        fun getDateFormat(pattern: String): String {
            var sdf: SimpleDateFormat = SimpleDateFormat(pattern, Locale.JAPAN)
            var cal = Calendar.getInstance()

            return sdf.format(cal.time)
        }
        /**
         * 日付をフォーマット指定で変換
         * @param year 年
         * @param month 月
         * @param day 日
         * @param format フォーマット
         */
        fun dateToFormat(year: Int, month: Int, day: Int, hour: Int, minute: Int, format: String): String {
            val calendar = Calendar.getInstance()
            calendar.set(year, month - 1, day, hour, minute, 0) //　月部分は 0~11で指定する必要がある
            val timeZone = TimeZone.getDefault()
            val locale = Locale.JAPAN
            var moment = Moment(calendar.time, timeZone, locale)

            return moment.format(format)
        }

        /**
         * 日付を月、日、週のフォーマットで変換
         * @param year 年
         * @param monthOfYear 月
         * @param dayOfMonth 日
         *
         * Returns: 文字列
         */
        private fun convertReportDate(year: Int, monthOfYear: Int, dayOfMonth: Int): String {
            val calendar = Calendar.getInstance()
            calendar.set(year, monthOfYear - 1, dayOfMonth)
            val srtDay: String = when (calendar.get(Calendar.DAY_OF_WEEK)) {
                Calendar.SUNDAY -> "日"
                Calendar.MONDAY -> "月"
                Calendar.TUESDAY -> "火"
                Calendar.WEDNESDAY -> "水"
                Calendar.THURSDAY -> "木"
                Calendar.FRIDAY -> "金"
                Calendar.SATURDAY -> "土"
                else -> {
                    ""
                }
            }
            // Display Selected date in textbox
            return "$monthOfYear" + "月" + dayOfMonth + "日" + "(" + srtDay + ")"
        }

        /**
         * yyyyMMdd を月、日、週を含む表示形式に変換する。
         * @param dateStr YYYYMMDD
         *
         * Returns: 文字列
         */
        fun convertDateToDisplay(dateStr: String?): String {
            val year = dateStr?.substring(0, 4)?.toInt()
            val month = dateStr?.substring(4, 6)?.toInt()
            val day = dateStr?.substring(6, 8)?.toInt()
            if (year != null && month != null && day != null) {
                return convertReportDate(year, month, day)
            }

            return ""
        }
    }

    fun roundDateTime(date: Date = Date()): String {
        var dateNow = date.toString("yyyy/MM/dd HH:mm")
        var dateSplit = dateNow.split(" ")
        var dateMinutes = dateSplit[1].split(":")
        var hour = dateMinutes[0]
        var minitue = dateMinutes[1].toInt()

        var time = if (minitue > 30) {
            hour.toString() + ":" + "30"
        } else {
            hour.toString() + ":" + "00"
        }

        return dateSplit[0] + " " + time
    }

    /**
     * 現在時刻を変換し、30分切り捨て時刻で返却する
     */
    fun roundDownDateTime(): String {
        var dateNow = getDateFormat("HH:mm")
        var dateSplit = dateNow.split(":")
        var hour = dateSplit[0]
        var minitue = dateSplit[1].toInt()

        if (minitue > 30) {
            return hour.toString() + ":" + "30"
        } else {
            return hour.toString() + ":" + "00"
        }
    }

    /**
     * 時間の加算処理を行います
     *
     * @param beforeTime 変更前の時間
     * @param addHour 加算する時間
     */
    fun getAddHour(beforeTime: String, addHour: Int): String {
        var beforeTimeSplit = beforeTime.split(":")
        var beforeHour = beforeTimeSplit[0].toInt()

        if ((beforeHour + addHour) >= 24) {
            var calcTime = (beforeHour + addHour) - 24;

            return "0" + calcTime.toString() + ":" + beforeTimeSplit[1]
        } else {
            return (beforeHour + addHour).toString() + ":" + beforeTimeSplit[1]
        }
    }

    /**
     * StringをDate型に変換
     * @param dateStr 変換したい日付の文字列 (例) 2018/01/07
     * @param format フォーマット
     */
    fun stringToDate(dateStr: String, format: String): Date? {
        val sdf = SimpleDateFormat(format, Locale.JAPAN)
        var date: Date? = null
        try {
            date = sdf.parse(dateStr)
        } catch (e: ParseException) {
            e.printStackTrace()
        }

        return date
    }

    /**
     * StringをDate型に変換
     * @param dateStr 変換したい日付の文字列 (例) 2018/01/07
     * @param format フォーマット
     */
    fun stringToDateString(dateStr: String, format: String): String {
        val sdf = SimpleDateFormat(format, Locale.JAPAN)
        var date: Date? = null
        try {
            date = sdf.parse(dateStr)
        } catch (e: ParseException) {
            e.printStackTrace()
        }

        return date.toString()
    }

    /**
     * yyyy-MM-dd HH:MM 日付をフォーマット指定で変換
     * @param year 年
     * @param month 月
     * @param day 日
     * @param format フォーマット
     */
    fun stringDateToFormat(date: String, format: String): String {

        if (date == "") {
            return ""
        }
        val datetime = date.split(" ")
        val dateSplit = datetime[0].split("-")
        val timeSplit = datetime[1].split(":")
        val year = dateSplit[0].toInt()
        val month = dateSplit[1].toInt()
        val day = dateSplit[2].toInt()
        val hour = timeSplit[0].toInt()
        val minutes = timeSplit[1].toInt()

        return dateToFormat(year, month, day, hour, minutes, format)
    }
}