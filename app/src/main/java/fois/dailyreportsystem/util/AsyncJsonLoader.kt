package fois.dailyreportsystem.util

// 作成者：近藤
// 作成日：2017/09/01
// 更新日：2017/10/03

import android.os.AsyncTask
import org.json.JSONException
import org.json.JSONObject
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.MalformedURLException
import java.net.URL

// 通信処理を行うクラス
class AsyncJsonLoader(_asyncCallback: AsyncCallback) : AsyncTask<String, Int, JSONObject>() {

    interface AsyncCallback {
        fun preExecute()
        fun postExecute(result: JSONObject?)
        fun progressUpdate(progress: Int?)
        fun cancel()
    }

    private var mAsyncCallback: AsyncCallback? = null

    init {
        mAsyncCallback = _asyncCallback
    }

    override fun onPreExecute() {
        super.onPreExecute()
        mAsyncCallback!!.preExecute()
    }

    override fun onProgressUpdate(vararg _progress: Int?) {
        super.onProgressUpdate(*_progress)
        mAsyncCallback!!.progressUpdate(_progress[0])
    }

    override fun onPostExecute(_result: JSONObject?) {
        super.onPostExecute(_result)
        mAsyncCallback!!.postExecute(_result)
    }

    override fun onCancelled() {
        super.onCancelled()
        mAsyncCallback!!.cancel()
    }

    // _data[0]：接続先URL、_data[1]：POST送信データ
    override fun doInBackground(vararg _data: String): JSONObject? {
        val urlConnection: HttpURLConnection?
        try {
            urlConnection = URL(_data[0]).openConnection() as HttpURLConnection
            urlConnection.connectTimeout = 20000
            urlConnection.readTimeout = 20000
            urlConnection.addRequestProperty("Content-Type", "application/json; charset=UTF-8")
            urlConnection.requestMethod = "POST"
            urlConnection.instanceFollowRedirects = false
            urlConnection.setChunkedStreamingMode(0)
            urlConnection.doInput = true
            urlConnection.doOutput = true
            urlConnection.connect()

            val outputStream = urlConnection.outputStream
            outputStream.write(_data[1].toByteArray(charset("UTF-8")))
            outputStream.flush()

            val inputStream = urlConnection.inputStream

            val stringBuffer = StringBuffer()
            var readStr: String?
            val bufferedReader = BufferedReader(InputStreamReader(inputStream, "UTF-8"))
            readStr = bufferedReader.readLine()
            while (readStr != null) {
                stringBuffer.append(readStr)
                readStr = bufferedReader.readLine()
            }
            val str = stringBuffer.toString()
            val jsonObject = JSONObject(str)

            try {
                return jsonObject
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                outputStream.close()
                inputStream.close()
                bufferedReader.close()
                urlConnection.disconnect()
            }

            Thread.sleep(200)

        } catch (e: MalformedURLException) {
            e.printStackTrace()
        } catch (e: IOException) {
            e.printStackTrace()
        } catch (e: JSONException) {
            e.printStackTrace()
        } catch (e: InterruptedException) {
            e.printStackTrace()
        }

        return null
    }
}