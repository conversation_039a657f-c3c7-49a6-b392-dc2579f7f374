package fois.dailyreportsystem.util

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import androidx.core.content.FileProvider
import fois.dailyreportsystem.BuildConfig
import fois.dailyreportsystem.R
import fois.dailyreportsystem.activity.report.PhotoDetailActivity
import fois.dailyreportsystem.activity.report.PhotoShootListActivity
import fois.dailyreportsystem.activity.report.PhotoThumbnailListActivity
import fois.dailyreportsystem.base.BaseActivity
import java.io.File

// カメラを起動するクラス
class StartCamera : BaseActivity() {

    private val sharedPreferences: SharedPreferences = context!!.getSharedPreferences("Settings", Context.MODE_PRIVATE)

    fun LaunchCamera(activity: String) {
        try {
            var file: File?
            val outputFileUri: Uri?
            if (Build.VERSION.SDK_INT >23) {
                // Android7以降は読み込み先が異なる
                file = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).toString() + "/temp_image.jpg")
                if (file.exists()) {
                    file.delete()
                    file = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).toString() + "/temp_image.jpg")
                }
                outputFileUri = FileProvider.getUriForFile(context!!, BuildConfig.APPLICATION_ID + ".provider", file)
            } else {
                // Android6以前
                file = File(Environment.getExternalStorageDirectory().toString() + "/temp_image.jpg")
                if (file.exists()) {
                    file.delete()
                    file = File(Environment.getExternalStorageDirectory().toString() + "/temp_image.jpg")
                }
                outputFileUri = Uri.fromFile(file)
            }
            val intent: Intent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
            intent.putExtra(MediaStore.EXTRA_OUTPUT, outputFileUri)
            // 遷移元で分岐
            when (activity) {
                context!!.getString(R.string.photo_shoot_list_activity) -> {
                    if((context as PhotoShootListActivity).clickPosition == null) {
                        (context as PhotoShootListActivity).clickPosition = true
                        (context as PhotoShootListActivity).startActivityForResult(intent, 0)
                    }
                }
                context!!.getString(R.string.photo_thumbnail_list_activity) -> {
                    if((context as PhotoThumbnailListActivity).clickPosition == null) {
                        (context as PhotoThumbnailListActivity).clickPosition = true
                        (context as PhotoThumbnailListActivity).startActivityForResult(intent, 0)
                    }
                }
                context!!.getString(R.string.photo_detail_activity) -> {
                    if((context as PhotoDetailActivity).clickPosition == null) {
                        (context as PhotoDetailActivity).clickPosition = true
                        sharedPreferences.edit().putString("EXTRA_OUTPUT", outputFileUri.toString()).apply()
                        (context as PhotoDetailActivity).startActivityForResult(intent, 0)
                    }
                }
            }
        } catch (e: Exception) {
            log(context?.javaClass!!.name, "Exception File:" + e.message)
        }
    }
}
