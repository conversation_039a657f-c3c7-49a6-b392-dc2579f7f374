package fois.dailyreportsystem.util

// 作成日：2017/11/13
// 更新日：2018/02/02

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Handler
import androidx.fragment.app.DialogFragment
import android.view.Gravity
import android.view.Window
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import fois.dailyreportsystem.R
import fois.dailyreportsystem.activity.login.LoginActivity
import fois.dailyreportsystem.activity.message.MessageGroupListActivity
import fois.dailyreportsystem.base.BaseActivity

class NotificationDialogFragment : DialogFragment() {
    private var wm: WindowManager.LayoutParams? = null
    private var notificationDialogArea: Button? = null
    private var notificationDialogTitle: TextView? = null
    private var notificationDialogMessage: TextView? = null
    private var notificationDialogTime: TextView? = null

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = Dialog(activity!!)
        dialog.window!!.requestFeature(Window.FEATURE_NO_TITLE)
        dialog.setContentView(R.layout.notification_dialog)

        notificationDialogArea = dialog.findViewById(R.id.nDialog) as Button
        notificationDialogTitle = dialog.findViewById(R.id.nDialog_title) as TextView
        notificationDialogMessage = dialog.findViewById(R.id.nDialog_message) as TextView
        notificationDialogTime = dialog.findViewById(R.id.nDialog_time) as TextView

        // 文言セット
        try {
            val sharedPreference: SharedPreferences = BaseActivity.context!!.getSharedPreferences("Settings", Context.MODE_PRIVATE)
            notificationDialogTitle!!.text = sharedPreference.getString("NOTIFICATION_TITLE", "")
        } catch (e: Exception) {}

        val handle: Handler = Handler()
        val run: Runnable = Runnable {
            try {
                dialog.dismiss()
            } catch (e: Exception) {}
        }

        wm = dialog.window!!.attributes
        wm!!.gravity = Gravity.TOP
        wm!!.width = resources.displayMetrics.widthPixels
        dialog.window!!.attributes = wm
        dialog.window!!.setFlags(0, WindowManager.LayoutParams.FLAG_DIM_BEHIND)

        dialog.setCanceledOnTouchOutside(true)

        notificationDialogArea!!.setOnSafeClickListener { NClick() }

        handle.postDelayed(run, 3000)

        return dialog
    }

    override fun onStop() {
        super.onStop()
        activity?.finish()
    }

    private fun NClick() {
        try {
            val intent: Intent
            if (context!!.settings.getBoolean("NOTIFICATION_ANOTHER_USER")) {
                intent = Intent(BaseActivity.context, LoginActivity::class.java)
                context!!.settings.put("NOTIFICATION_ANOTHER_USER", false)
            } else {
                intent = Intent(BaseActivity.context, MessageGroupListActivity::class.java)
            }
            startActivity(intent)
            activity!!.finish()
        } catch (e: Exception) {
            val pm: PackageManager = activity!!.packageManager
            val intent: Intent? = pm.getLaunchIntentForPackage("fois.dailyreportsystem")
            startActivity(intent)
            activity!!.finish()
        }
    }
}
