package fois.dailyreportsystem.util

// 作成日：2017/11/13
// 更新日：2018/02/06

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent

class NotificationDialogReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, data: Intent) {
        val intent = Intent(context, NotificationDialogActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(context, 0, intent, 0)
        try {
            pendingIntent.send()
        } catch (e: PendingIntent.CanceledException) {
            e.printStackTrace()
        }

    }
}
