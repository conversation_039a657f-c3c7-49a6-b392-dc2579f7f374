package fois.dailyreportsystem.util

// 作成日：2017/09/22
// 更新日：2017/11/14

import android.content.Context
import android.content.SharedPreferences
import android.util.Log

import com.google.firebase.iid.FirebaseInstanceId
import com.google.firebase.iid.FirebaseInstanceIdService
import fois.dailyreportsystem.base.BaseActivity

class MyFirebaseInstanceIDService : FirebaseInstanceIdService() {

    /**
     * Called if InstanceID token is updated. This may occur if the security of
     * the previous token had been compromised. Note that this is called when the InstanceID token
     * is initially generated so this is where you would retrieve the token.
     */
    // [START refresh_token]
    override fun onTokenRefresh() {
        // Get updated InstanceID token.
        val refreshedToken = FirebaseInstanceId.getInstance().token
        Log.d(TAG, "Refreshed token: " + refreshedToken!!)

        // 設定ファイル定義
        val sharedPreference: SharedPreferences? =  BaseActivity.context!!.getSharedPreferences("Settings", Context.MODE_PRIVATE)
        // トークンの保存
        if (sharedPreference!!.getString("NEW_DEVICE_ID", "") != "") {
            sharedPreference.edit().putString("OLD_DEVICE_ID", sharedPreference.getString("NEW_DEVICE_ID", "")).apply()
        }
        sharedPreference.edit().putString("NEW_DEVICE_ID", refreshedToken).apply()
        sharedPreference.edit().putBoolean("TOKEN_CHANGED", true).apply()

        // If you want to send messages to this application instance or
        // manage this apps subscriptions on the server side, send the
        // Instance ID token to your app server.
        sendRegistrationToServer(refreshedToken)
    }
    // [END refresh_token]

    /**
     * Persist token to third-party servers.

     * Modify this method to associate the user's FCM InstanceID token with any server-side account
     * maintained by your application.

     * @param token The new token.
     */
    private fun sendRegistrationToServer(token: String) {
        // TODO: Implement this method to send token to your app server.
    }

    companion object {

        private val TAG = "MyFirebaseIIDService"
    }
}