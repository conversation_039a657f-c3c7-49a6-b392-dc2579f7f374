package fois.util

// 作成日：2018/02/23
// 更新日：2018/03/08

import android.content.Context
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.util.Constant

import java.util.Dictionary
import java.util.Hashtable

import java.io.UnsupportedEncodingException
import java.net.URLEncoder

// 通信に使用パラメータをセットするクラス
class ConnectionParameter : BaseActivity() {

        private val sharedPreferences = context!!.getSharedPreferences("Settings", Context.MODE_PRIVATE)

        // 共通パラメータ
        fun SetCommonParameter(): Dictionary<String, String> {
            val params_item = Hashtable<String, String>()
            params_item.put("UserCode", sharedPreferences.getString("USER_ID[" + sharedPreferences.getString("WEB_USER_ID", "") + sharedPreferences.getString("SERVICE_ID", "PR") + "]", ""))
            params_item.put("UserPassword", sharedPreferences.getString("UserPassMD5[" + sharedPreferences.getString("WEB_USER_ID", "") + sharedPreferences.getString("SERVICE_ID", "PR") + "]", ""))
            params_item.put("La", Constant.LANG)
            params_item.put("Expires", sharedPreferences.getString("EXPIRES", ""))
            return params_item
        }

        fun SetVariousParameter(dictionary: Dictionary<String, String>): String {
            var arrayParam: ArrayList<String>? = ArrayList(dictionary.size())
            val keys = dictionary.keys()
            while (keys.hasMoreElements()) {
                val key = keys.nextElement() as String
                var value = try {
                    URLEncoder.encode(dictionary.get(key), "UTF-8")
                } catch (e: UnsupportedEncodingException) {
                    dictionary.get(key)
                }
                arrayParam!!.add(key + "=" + value)
            }
            return arrayParam!!.joinToString(separator="&")
        }
}
