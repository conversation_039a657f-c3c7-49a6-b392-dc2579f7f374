package fois.dailyreportsystem.util.weekview

import fois.dailyreportsystem.data.Schedule
import fois.dailyreportsystem.util.*

import fois.dailyreportsystem.util.weekview.WeekViewUtil.*
import java.util.*

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 7/21/2014.
 * Website: http://april-shower.com
 */
class WeekViewEvent {
    var id: String? = null
    var startTime: Calendar? = null
    var endTime: Calendar? = null
    var name: String? = null
    var location: String? = null
    var color: Int = 0
    var isAllDay: Boolean = false
    var schedule: Schedule? = null
    var travelTime: Float = 0f

    constructor(schedule: Schedule) {
        this.schedule = schedule
        this.id = schedule.scheduleID

        val start = schedule.startDateTime!!.toDate() ?: Date()
        this.startTime = start.toCalendar()

        val end = schedule.endDateTime!!.toDate() ?: Date()
        this.endTime = end.toCalendar()

        if (schedule.travelTime != null) {
            this.travelTime  = try{ schedule.travelTime!!.toFloat() }catch (e: Exception){ 0F }
        }

        var statusId = try { Integer.parseInt(schedule.surveyStatusID!!) } catch (e: Exception) { 0 }
        this.color = ColorUtil.getSurveyStatusColor(schedule.surveyStatusID)
        if (statusId != 0) {
            this.name = schedule.title!!
            if (schedule.researchTaskID != "") {
                this.location = schedule.researchTask!!.CityName + schedule.researchTask!!.TaskAddress
            } else {
                this.location = schedule.location!!
            }
        }
    }

    /**
     * Initializes the event for week view.
     * @param id The id of the event.
     * @param name Name of the event.
     * @param location The location of the event.
     * @param startTime The time when the event starts.
     * @param endTime The time when the event ends.
     * @param allDay Is the event an all day event.
     */
    @JvmOverloads
    constructor(id: String, name: String, location: String?, startTime: Calendar, endTime: Calendar, allDay: Boolean = false) {
        this.id = id
        this.name = name
        this.location = location
        this.startTime = startTime
        this.endTime = endTime
        this.isAllDay = allDay
    }

    override fun equals(o: Any?): Boolean {
        if (this === o) return true
        if (o == null || javaClass != o.javaClass) return false

        val that = o as WeekViewEvent?

        return id === that!!.id

    }

    fun splitWeekViewEvents(): List<WeekViewEvent> {
        //This function splits the WeekViewEvent in WeekViewEvents by day
        val events = ArrayList<WeekViewEvent>()
        // The first millisecond of the next day is still the same day. (no need to split events for this).
        var endTime = this.endTime!!.clone() as Calendar
        endTime.add(Calendar.MILLISECOND, -1)
        if (!isSameDay(this.startTime!!, endTime)) {
            endTime = this.startTime!!.clone() as Calendar
            endTime.set(Calendar.HOUR_OF_DAY, 23)
            endTime.set(Calendar.MINUTE, 59)
            val event1 = WeekViewEvent(this.id ?: "", this.name
                    ?: "", this.location, this.startTime!!, endTime, this.isAllDay)
            event1.color = this.color
            events.add(event1)

            // Add other days.
            val otherDay = this.startTime!!.clone() as Calendar
            otherDay.add(Calendar.DATE, 1)
            while (!isSameDay(otherDay, this.endTime!!)) {
                val overDay = otherDay.clone() as Calendar
                overDay.set(Calendar.HOUR_OF_DAY, 0)
                overDay.set(Calendar.MINUTE, 0)
                val endOfOverDay = overDay.clone() as Calendar
                endOfOverDay.set(Calendar.HOUR_OF_DAY, 23)
                endOfOverDay.set(Calendar.MINUTE, 59)
                val eventMore = WeekViewEvent(this.id ?: "", this.name
                        ?: "", null, overDay, endOfOverDay, this.isAllDay)
                eventMore.color = this.color
                events.add(eventMore)

                // Add next day.
                otherDay.add(Calendar.DATE, 1)
            }

            // Add last day.
            val startTime = this.endTime!!.clone() as Calendar
            startTime.set(Calendar.HOUR_OF_DAY, 0)
            startTime.set(Calendar.MINUTE, 0)
            val event2 = WeekViewEvent(this.id ?: "", this.name
                    ?: "", this.location, startTime, this.endTime!!, this.isAllDay)
            event2.color = this.color
            events.add(event2)
        } else {
            events.add(this)
        }

        return events
    }
}