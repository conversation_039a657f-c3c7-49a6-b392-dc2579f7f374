package fois.dailyreportsystem.util

import android.graphics.drawable.Drawable
import android.os.Build
import android.view.View


private var clickTime: Long = 0

// ダブルタップ防止
fun <T : View> T.safeClick(){
    this.isEnabled = false
    this.isClickable = false
    this.postDelayed({
        this.isEnabled = true
        this.isClickable = true
    }, 600L)
}

fun <T : View> T.setOnSafeClickListener(block: (T) -> Unit) {
    this.setOnClickListener { view ->
        if (System.currentTimeMillis() - clickTime < 600) {
            return@setOnClickListener
        }
        @Suppress("UNCHECKED_CAST")
        block(view as T)
        clickTime = System.currentTimeMillis()
    }
}

fun View.setDrawable(drawable: Drawable?) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
        this.background = drawable
    } else {
        this.setBackgroundDrawable(drawable)
    }
}

/**
 * 複数のコントロールで同じクリックイベントを共有させる
 */
fun <T : View> Iterable<T>.setSharedOnSafeClickListener(block: (T) -> Unit) {
    this.forEach {
        it.setOnSafeClickListener(block)
    }
}

/**
 * 複数のコントロールで同じフォーカス変更イベントを共有させる
 */
fun <T : View> Iterable<T>.setSharedOnFocusChangeListener(block: (T, Boolean) -> Unit) {
    this.forEach {
        it.setOnFocusChangeListener { view, hasFocus ->
            block(view as T, hasFocus)
        }
    }
}