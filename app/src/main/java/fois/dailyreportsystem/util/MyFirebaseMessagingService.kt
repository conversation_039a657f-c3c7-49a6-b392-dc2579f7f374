package fois.dailyreportsystem.util

// 作成日：2017/09/22
// 更新日：2018/02/16

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat

import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import fois.dailyreportsystem.R
import fois.dailyreportsystem.activity.MainActivity
import fois.dailyreportsystem.activity.login.LoginActivity
import fois.dailyreportsystem.activity.message.MessageGroupListActivity
import fois.dailyreportsystem.activity.request.OrderDetailActivity
import fois.dailyreportsystem.activity.setting.FirstSettingActivity
import fois.dailyreportsystem.base.BaseActivity
import fois.dailyreportsystem.data.Login
import fois.dailyreportsystem.util.Constant.FRAGMENT_SCREEN_MAIN
import fois.dailyreportsystem.util.Constant.FRAGMENT_SCREEN_REQUEST_LIST



class MyFirebaseMessagingService : FirebaseMessagingService() {


    override fun handleIntent(intent: Intent) {

        if (intent.extras == null) return

        val data: Map<String, String> = mapOf(
                "NotificationCode" to (intent.extras!!.getString("NotificationCode") ?: ""),
                "UserID" to (intent.extras!!.getString("UserID") ?: ""),
                "Message" to (intent.extras!!.getString("Message") ?: ""),
                "SendTime" to (intent.extras!!.getString("SendTime") ?: ""),
                "MessageGroupID" to (intent.extras!!.getString("MessageGroupID") ?: ""),
                "MessageGroupName" to (intent.extras!!.getString("MessageGroupName") ?: ""),
                "UnreadCount" to (intent.extras!!.getString("UnreadCount") ?: ""),
                "UnreadTotal" to (intent.extras!!.getString("UnreadTotal") ?: ""),
                "TaskID" to (intent.extras!!.getString("TaskID") ?: ""),
                "MessageID" to (intent.extras!!.getString("MessageID") ?: ""),
                "ScheduleID" to (intent.extras!!.getString("ScheduleID") ?: "")
        )
        val title = (intent.extras!!.getString("gcm.notification.title") ?: "")
        val body = (intent.extras!!.getString("gcm.notification.body") ?: "")

        sendNotification(title, body, data)
    }


    override fun onMessageReceived(remoteMessage: RemoteMessage) {

        try {
            var data: Map<String, String> = mapOf()
            // データの取得
            if(remoteMessage != null) {
                data = remoteMessage.data
                val title = remoteMessage.notification?.title ?: ""
                val body = remoteMessage.notification?.body ?: ""

                sendNotification(title, body, data)
            }

        } catch (e: Exception) {

        }
    }

    private fun sendNotification(title: String, body: String, data: Map<String, String> ){

        val sp = this.settings


        // ログイン中のユーザか判別
        if (sp.getString("USER_ID") != data["UserID"]) {
            // ログインユーザとは別ユーザへの通知
            showNotificationDialog(title, body, data)
            return
        }

        // アプリがバックグラウンドか判別
        if (! sp.getBoolean("FOREGROUND")) {
            // バックグラウンド
            showNotificationDialog(title, body, data)
            return
        }

        // フォアグラウンド
        when(data["NotificationCode"]){
            "0101" -> {
                sp.put("NOTIFICATION_TITLE", data["MessageGroupName"] ?: "")


                // 更新用intent
                val intent: Intent = Intent()
                intent.putExtra("MessageGroupID", data["MessageGroupID"])
                intent.putExtra("TaskID", data["TaskID"])
                intent.putExtra("UnreadCount", data["UnreadCount"])

                // 現在の画面を判別
                when (sp.getString("ACTIVITY")) {
                    BaseActivity.context!!.getString(R.string.main_activity) -> {
                        // 邸一覧
                        if (data["TaskID"]!!.isNotEmpty()) {
                            // 邸IDが通知に存在する
                            val taskIDList: String = sp.getString("TASK_ID_LIST")
                            val taskIDListArray: List<String> = taskIDList.split(",".toRegex(), 0)
                            if (taskIDListArray.indexOf(data["TaskID"]) != -1) {
                                // 該当する邸が存在する
                                intent.action = "TaskListUpdate"
                                sendBroadcastIntent(intent)
                                return
                            }
                        }
                    }
                    BaseActivity.context!!.getString(R.string.message_group_list_activity) -> {
                        // メッセージグループ一覧画面
                        intent.action = "MessageGroupListUpdate"
                        sendBroadcastIntent(intent)
                        return
                    }
                    BaseActivity.context!!.getString(R.string.message_list_activity) -> {
                        // メッセージ一覧画面
                        if (sp.getString("MESSAGE_GROUP_ID") == data["MessageGroupID"]) {
                            // 通知のメッセージグループを表示中
                            intent.action = "MessageListUpdate"
                            sendBroadcastIntent(intent)
                            return
                        }
                    }
                }
                showNotificationDialog(title, body, data)
            }
            "0201","0202","0203","0204","0205" -> {
                showNotificationDialog(title, body, data)
            }
        }
    }


    // ブロードキャストインテント
    private fun sendBroadcastIntent(intent: Intent){
        try {
            baseContext.sendBroadcast(intent)
        } catch (e: Exception){

        }
    }

    // 通知ダイアログの表示
    private fun showNotificationDialog(title: String, body: String, data: Map<String, String> ){
        var channelId = ""
        var channelName = ""
        var intents :Array<Intent> = arrayOf(Intent(), Intent())

        val sp = this.settings

        when (data["NotificationCode"]) {
            // 新着メッセージ
            "0101" -> {
                channelId = getString(R.string.channelID)
                channelName = getString(R.string.channelName)

                intents = arrayOf(Intent(), Intent())
                val intent = Intent(this, MainActivity::class.java)
                intent.putExtra("RE_SETUP_FRAGMENT", FRAGMENT_SCREEN_MAIN)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                intents[0] = intent
                intents[1] = Intent(applicationContext, MessageGroupListActivity::class.java)
            }
            // 調査依頼状況
            "0201", "0202", "0203", "0204", "0205" -> {
                channelId = getString(R.string.surveyID)
                channelName = getString(R.string.surveyName)

                intents = arrayOf(Intent(), Intent())
                val intent = Intent(this, MainActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                intent.putExtra("RE_SETUP_FRAGMENT", FRAGMENT_SCREEN_REQUEST_LIST)
                intents[0] = intent
                var intent1 = Intent(applicationContext, OrderDetailActivity::class.java)
                intent1.putExtra("SCHEDULE_ID", data["ScheduleID"])
                intents[1] = intent1
            }
        }

        // 初回起動、認証が終わっているか判別
        if (sp.getBoolean("FIRST_LAUNCH", true)) {
            //初回登録へ
            intents = arrayOf(Intent(), Intent())
            val intent = Intent(this, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            intents[0] = intent
            intents[1] = Intent(applicationContext, FirstSettingActivity::class.java)
        }
        // ログイン中か判別
        else if (!sp.getBoolean("LOGIN_FLAG") || sp.getString("USER_ID") != data["UserID"]) {
            // ログイン画面へ
            intents = arrayOf(Intent(), Intent())
            val intent = Intent(this, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            intents[0] = intent
            val intent1 = Intent(applicationContext, LoginActivity::class.java)
            data.forEach { (k, v) ->
                intent1.putExtra(k, v)
            }
            intents[1] = intent1
        }

        val pendingIntent = PendingIntent.getActivities(this, 0 /* Request code */, intents, PendingIntent.FLAG_ONE_SHOT)
        val notificationBuilder = NotificationCompat.Builder(this, channelId)
                .setSmallIcon(R.mipmap.ic_launch) // 必須
                .setColor(ContextCompat.getColor(this, R.color.defaultColor))
                .setContentText(body)
                .setShowWhen(true)
                .setWhen(System.currentTimeMillis())
                .setAutoCancel(true)
                .setDefaults(NotificationCompat.DEFAULT_ALL)
                .setPriority(NotificationCompat.PRIORITY_MAX)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setContentIntent(pendingIntent)

        if (title.isNotBlank()) {
            notificationBuilder.setContentTitle(title)
        }

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // Since android Oreo notification channel is needed.
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (notificationManager.getNotificationChannel(channelId) == null) {
                val mChannel = NotificationChannel(channelId, channelName, NotificationManager.IMPORTANCE_HIGH)
                mChannel.apply {
                    description = "この通知の詳細情報を設定します"
                }
                notificationManager.createNotificationChannel(mChannel)
            }
        }

        notificationManager.notify(0 /* ID of notification */, notificationBuilder.build())
    }
}