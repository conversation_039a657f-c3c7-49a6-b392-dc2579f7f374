package fois.dailyreportsystem.util

// 作成日：2017/10/04
// 更新日：2018/02/02

import android.content.Context
import android.view.Gravity
import android.widget.Toast

// Toastを表示するためのクラス（一括でUIなどの管理をする場合ここで変更）
class ShowMessages {
    fun Show(context: Context?, message: String) {
        if (toast != null) {
            // Toastが表示中ならキャンセルする
            toast!!.cancel()
        }
        // Toastを作成（表示時間：長め、表示位置：中央）
        toast = Toast.makeText(context, message, Toast.LENGTH_LONG)
        toast!!.setGravity(Gravity.CENTER, 0, 0)
        toast!!.show()
    }

    companion object {
        private var toast: Toast? = null
    }
}