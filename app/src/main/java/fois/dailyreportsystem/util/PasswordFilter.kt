package fois.dailyreportsystem.util

// 作成日：2017/09/07
// 更新日：2018/02/06

import android.text.InputFilter
import android.text.Spanned

// パスワード用入力制御フィルター
class PasswordFilter : InputFilter {
    override fun filter(source: CharSequence, start: Int, end: Int, dest: Spanned, dstart: Int, dend: Int): CharSequence {
        // 半角英数字のみ許可、それ以外は空文字にして返す
        //if (source.toString().matches("^[a-zA-Z0-9]+$".toRegex())) {
            return source
        /*} else {
            return ""
        }*/
    }
}