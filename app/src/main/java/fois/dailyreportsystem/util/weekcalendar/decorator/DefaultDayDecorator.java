package fois.dailyreportsystem.util.weekcalendar.decorator;

import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.util.TypedValue;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.core.content.ContextCompat;

import org.joda.time.DateTime;
import org.joda.time.DateTimeConstants;

import java.util.ArrayList;

import fois.dailyreportsystem.R;
import fois.dailyreportsystem.util.weekcalendar.fragment.WeekFragment;

/**
 * Created by gokhan on 7/27/16.
 */
public class DefaultDayDecorator implements DayDecorator {

    private Context context;
    private final int selectedDateColor;
    private final int selectedTextColor;
    private final int todayDateColor;
    private int todayDateTextColor;
    private int textColor;
    private float textSize;
    private int sundayTextColor;
    private int saturdayTextColor;

    public DefaultDayDecorator(Context context,
                               @ColorInt int selectedDateColor,
                               @ColorInt int selectedTextColor,
                               @ColorInt int todayDateColor,
                               @ColorInt int todayDateTextColor,
                               @ColorInt int textColor,
                               float textSize,
                               @ColorInt int sundayTextColor,
                               @ColorInt int saturdayTextColor
    ) {
        this.context = context;
        this.selectedDateColor = selectedDateColor;
        this.selectedTextColor = selectedTextColor;
        this.todayDateColor = todayDateColor;
        this.todayDateTextColor = todayDateTextColor;
        this.textColor = textColor;
        this.textSize = textSize;
        this.sundayTextColor = sundayTextColor;
        this.saturdayTextColor = saturdayTextColor;
    }

    @TargetApi(Build.VERSION_CODES.JELLY_BEAN)
    @Override
    public void decorate(View view, TextView dayTextView, DateTime dateTime, DateTime firstDayOfTheWeek, DateTime selectedDateTime, ArrayList<DateTime> holidays) {
        //DateTime dt = new DateTime();

        Drawable holoCircle = ContextCompat.getDrawable(context, R.drawable.week_calendar_holo_circle);
        Drawable solidCircle = ContextCompat.getDrawable(context, R.drawable.week_calendar_solid_circle);

        holoCircle.setColorFilter(selectedDateColor, PorterDuff.Mode.SRC_ATOP);
        solidCircle.setColorFilter(todayDateColor, PorterDuff.Mode.SRC_ATOP);

        if (firstDayOfTheWeek.getMonthOfYear() < dateTime.getMonthOfYear() || firstDayOfTheWeek.getYear() < dateTime.getYear()) {
            dayTextView.setTextColor(Color.GRAY);
        }

        DateTime calendarStartDate = WeekFragment.CalendarStartDate;
        // 今日
        if (dateTime.toLocalDate().equals(calendarStartDate.toLocalDate())) {
            dayTextView.setBackground(solidCircle);
            dayTextView.setTextColor(this.todayDateTextColor);
        } else {
            if (dateTime.toLocalDate().getDayOfWeek() == DateTimeConstants.SUNDAY) {
                dayTextView.setTextColor(this.sundayTextColor);
            } else if (dateTime.toLocalDate().getDayOfWeek() == DateTimeConstants.SATURDAY) {
                dayTextView.setTextColor(this.saturdayTextColor);
            } else if (holidays != null && holidays.indexOf(dateTime.withMillisOfDay(0)) >= 0){
                dayTextView.setTextColor(this.sundayTextColor);
            } else {
                dayTextView.setTextColor(textColor);
            }
        }

        // 選択
        if (selectedDateTime != null) {
            if (selectedDateTime.toLocalDate().equals(dateTime.toLocalDate())) {
                dayTextView.setBackground(holoCircle);
                dayTextView.setTextColor(this.selectedTextColor);
            } else {
                if(! dateTime.toLocalDate().equals(calendarStartDate.toLocalDate())){
                    dayTextView.setBackground(null);
                }
            }
        }

        float size = textSize;
        if (size == -1) size = dayTextView.getTextSize();
        dayTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, size);

        if (dateTime.toLocalDate().getDayOfMonth() == 1){
            dayTextView.setText(dateTime.toLocalDate().getMonthOfYear() + "月" + System.getProperty("line.separator") + dateTime.toLocalDate().getDayOfMonth());
            dayTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, size / 1.5F);
        }
    }
}
