package fois.dailyreportsystem.util

// 作成日：2017/10/18
// 更新日：2107/11/16

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.AsyncTask
import org.json.JSONException
import java.io.IOException
import java.net.HttpURLConnection
import java.net.MalformedURLException
import java.net.URL

// 通信処理を行うクラス
class AsyncBitmapLoader(_asyncCallback: AsyncCallback) : AsyncTask<String, Int, Bitmap>() {

    interface AsyncCallback {
        fun preExecute()
        fun postExecute(result: Bitmap?)
        fun progressUpdate(progress: Int?)
        fun cancel()
    }

    private var mAsyncCallback: AsyncCallback? = null

    init {
        mAsyncCallback = _asyncCallback
    }

    override fun onPreExecute() {
        super.onPreExecute()
        mAsyncCallback!!.preExecute()
    }

    override fun onProgressUpdate(vararg _progress: Int?) {
        super.onProgressUpdate(*_progress)
        mAsyncCallback!!.progressUpdate(_progress[0])
    }

    override fun onPostExecute(_result: Bitmap?) {
        super.onPostExecute(_result)
        mAsyncCallback!!.postExecute(_result)
    }

    override fun onCancelled() {
        super.onCancelled()
        mAsyncCallback!!.cancel()
    }

    override fun doInBackground(vararg _data: String): Bitmap? {
        val urlConnection: HttpURLConnection?
        val bitmap: Bitmap?
        try {
            urlConnection = URL(_data[0]).openConnection() as HttpURLConnection
            urlConnection.connectTimeout = 20000
            urlConnection.readTimeout = 20000
            urlConnection.requestMethod = "GET"
            urlConnection.connect()

            val inputStream = urlConnection.inputStream

            bitmap = BitmapFactory.decodeStream(inputStream)

            try {
                return bitmap
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                inputStream.close()
                urlConnection.disconnect()
            }

            Thread.sleep(200)

        } catch (e: MalformedURLException) {
            e.printStackTrace()
        } catch (e: IOException) {
            e.printStackTrace()
        } catch (e: JSONException) {
            e.printStackTrace()
        } catch (e: InterruptedException) {
            e.printStackTrace()
        }

        return null
    }
}