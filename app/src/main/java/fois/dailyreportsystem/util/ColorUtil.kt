package fois.dailyreportsystem.util

import android.graphics.Color

class ColorUtil {

    companion object {
        fun theme(): Int {
            return Color.argb(255, 45, 108, 204)
        }
        val SURVEY_STATUS_COLOR = listOf(
            Color.argb(255, 156, 194, 228),
            Color.argb(255, 172, 172, 172),
            Color.argb(255, 0, 0, 0),
            Color.argb(255, 49, 100, 200),
            Color.argb(255, 255, 0, 6),
            Color.argb(255, 201, 106, 45),
            Color.argb(255, 238, 125, 57),
            Color.argb(255, 201, 201, 201),
            Color.argb(255, 255, 193, 46)
        )
        fun getSurveyStatusColor(status: String?): Int{
            if (status == null) return ColorUtil.SURVEY_STATUS_COLOR[0]
            val statusId = try { Integer.parseInt(status) } catch (e: Exception){ 0 }
            return try { ColorUtil.SURVEY_STATUS_COLOR[statusId] } catch (e:Exception){ ColorUtil.SURVEY_STATUS_COLOR[0] }
        }

        val CALENDER_COLOR = listOf(
                Color.argb(255,  50, 152, 220),
                Color.argb(255, 205,  45,  53),
                Color.argb(255,  29, 108, 160),
                Color.argb(255, 228, 127,  33),
                Color.argb(255, 139,  70, 172),
                Color.argb(255, 237, 197,  16),
                Color.argb(255, 152,  90, 182),
                Color.argb(255,  38, 205, 112),
                Color.argb(255, 235,  62, 152),
                Color.argb(255,  10, 128,  57),
                Color.argb(255, 125, 139, 140),
                Color.argb(255,  20, 189, 155)
        )

        fun getCalenderColor(status: Int): Int{
            return try { ColorUtil.CALENDER_COLOR[status] } catch (e:Exception){ Color.GRAY }
        }
    }

}