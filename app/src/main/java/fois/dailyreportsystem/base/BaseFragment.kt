package fois.dailyreportsystem.base

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.SharedPreferences
import androidx.fragment.app.Fragment
import fois.dailyreportsystem.R
import fois.dailyreportsystem.util.ui.CustomProgressDialog

open class BaseFragment : Fragment(){

    lateinit var _context: Context
    lateinit var _activity: Activity

    var progressDialog: CustomProgressDialog? = null
    var progressCancelFlag: Boolean = false
    var countHub = 0

    lateinit var basePreference: SharedPreferences

    override fun onAttach(context: Context) {
        super.onAttach(context)
        this._context = context
        basePreference = context.getSharedPreferences("Settings", Context.MODE_PRIVATE)
        _activity = context as Activity

    }

    // モーダルを表示
    fun showHub(){
        if (this.progressDialog == null) {
            showProgressDialog(context!!.getString(R.string.connecting), DialogInterface.OnDismissListener {
                if (progressCancelFlag) {
                    return@OnDismissListener
                }
            })
        }
    }

    // モーダルを削除
    fun hideHub(){
        if (progressDialog != null) {
            if (progressDialog!!.isShowing) {
                try {
                    progressDialog!!.dismiss()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            progressDialog = null
        }
    }
    // ローディング表示
    fun showProgressDialog(message: String, onDismissListener: DialogInterface.OnDismissListener): CustomProgressDialog {
        if (progressDialog == null){
            progressDialog = CustomProgressDialog(_context)
            progressDialog!!.setCancelable(false)
            progressDialog!!.setOnCancelListener( DialogInterface.OnCancelListener { progressCancelFlag = true })
        }
        if(!message.isEmpty()) {
            progressDialog!!.setMessage(message)
        }
        progressDialog!!.setOnDismissListener(onDismissListener)
        progressDialog!!.show()
        return progressDialog!!
    }
}