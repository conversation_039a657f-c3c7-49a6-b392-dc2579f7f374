package fois.dailyreportsystem.base

// 作成日：2017/08/15
// 更新日：2018/03/09

import android.Manifest
import android.annotation.TargetApi
import android.app.AlertDialog
import android.app.Dialog
import android.content.*
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.Handler
import androidx.appcompat.app.AppCompatActivity
import android.util.Log
import android.view.*
import android.widget.*
import androidx.core.app.ActivityCompat
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout

import fois.dailyreportsystem.R
import fois.dailyreportsystem.activity.login.LoginActivity
import fois.dailyreportsystem.activity.message.MessageGroupListActivity
import fois.dailyreportsystem.data.RegistrationInfo
import fois.dailyreportsystem.util.*
import fois.dailyreportsystem.util.ui.CustomProgressDialog

/**
 * 共通クラス
 */
open class BaseActivity : AppCompatActivity() {
    protected var finishFlag: Boolean = false                            // 終了フラグ
    protected var URL: String? = null                                        // 接続URL
    protected var LANG: String? = null                                    // 言語
    var alertDialog: AlertDialog.Builder? = null                // アラートダイアログ
    var progressDialog: CustomProgressDialog? = null                   // プログレスダイアログ
    var progressCancelFlag: Boolean = false                    // プログレスを止めるフラグ
    protected var destroyFlag: Boolean = false                        // Flag destroy context
    var registrationInfo: RegistrationInfo? = null //登録情報
    var clickPosition: Boolean? = null

    // 通知用
    private var wm: WindowManager.LayoutParams? = null
    private var notificationDialogArea: Button? = null
    private var notificationDialogMessage: TextView? = null
    private var notificationDialogTime: TextView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        /* 初期化 */
        context = this
        finishFlag = false
        URL = Constant.URL
        LANG = Constant.LANG
        alertDialog = AlertDialog.Builder(context)
        registrationInfo = RegistrationInfo()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
    }

    // Add base onResume
    override fun onResume() {
        super.onResume()
        context = this
        destroyFlag = false

        val beforeActivityName: String = this.settings.getString("ACTIVITY" )
        var activityName: String = localClassName
        activityName = activityName.substring(activityName.lastIndexOf(".")+1)
        if(activityName != context!!.getString(R.string.login_activity) && activityName != beforeActivityName) {
            this.settings.put("BEFORE_ACTIVITY", beforeActivityName)
            this.settings.put("ACTIVITY", activityName)
        }

        // アプリはフォアグラウンド
        this.settings.put("FOREGROUND", true)

        clickPosition = null
    }

    override fun onPause() {
        super.onPause()
        // アプリはバックグラウンド
        this.settings.put("FOREGROUND", false)
    }

    override fun onDestroy() {
        super.onDestroy()
        if (!destroyFlag) {
            context = context
        } else {
            context = null
        }
        URL = null
        LANG = null
        alertDialog = null
        progressDialog = null
        // Layout最上親をmain_layoutにする
        cleanupView(findViewById(R.id.main_layout))
        // ガベージコレクション
        System.gc()
    }

    override fun finish() {
        super.finish()
        finishFlag = true
    }

    fun showProgressDialog(message: String, onDismissListener: DialogInterface.OnDismissListener) {
        if (this.progressDialog == null){
            this.progressDialog = CustomProgressDialog(this)
            this.progressDialog?.setCancelable(false)
            this.progressDialog?.setOnCancelListener( DialogInterface.OnCancelListener { progressCancelFlag = true })
            this.progressDialog!!.setOnDismissListener(onDismissListener)
            if(message.isNotEmpty()) {
                this.progressDialog?.setMessage(message)
            }
            this.progressDialog?.show()
        }
    }

    /* alertDialog初期設定 */
    @TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
    protected fun showAlertDialog(title: String?, mes: String?, positiveTitle: String?, positiveListener: DialogInterface.OnClickListener?, negativeTitle: String?, negativeListener: DialogInterface.OnClickListener?, onDismissListener: DialogInterface.OnDismissListener?) {
        alertDialog = AlertDialog.Builder(context)
        if (title != null) {
            alertDialog!!.setTitle(title)
        }
        if (mes != null) {
            alertDialog!!.setMessage(mes)
        }
        if (positiveTitle != null && positiveListener != null) {
            alertDialog!!.setPositiveButton(positiveTitle, positiveListener)
        }
        if (negativeTitle != null && negativeListener != null) {
            alertDialog!!.setNegativeButton(negativeTitle, negativeListener)
        }
        if (onDismissListener != null) {
            alertDialog!!.setOnDismissListener(onDismissListener)
        }
        alertDialog!!.show()
    }

    protected fun showAlertDialog(title: String, mes: String?, positiveTitle: String, positiveListener: DialogInterface.OnClickListener, negativeTitle: String?, negativeListener: DialogInterface.OnClickListener?) {
        this.showAlertDialog(title, mes, positiveTitle, positiveListener, negativeTitle, negativeListener, null)
    }

    protected fun showAlertDialog(title: String, mes: String, positiveTitle: String, positiveListener: DialogInterface.OnClickListener) {
        this.showAlertDialog(title, mes, positiveTitle, positiveListener, null, null, null)
    }

    fun showOKDialog(title: String) {
        this.showAlertDialog(title, null, "OK", android.content.DialogInterface.OnClickListener { paramDialogInterface, paramInt -> }, null, null)
    }

    fun showOKDialog(title: String, message: String) {
        this.showAlertDialog(title, message, "OK", android.content.DialogInterface.OnClickListener { paramsDialogInterface, paramInt -> }, null, null)
    }

    fun NotificationDialog(message: String, receiver: BroadcastReceiver) {
        val nDialog: NDialog = NDialog(context!!)
        val handle: Handler = Handler()
        val run: Runnable = Runnable {
            try {
                nDialog.dismiss()
            } catch (e: Exception) {}
        }
        wm = nDialog.window!!.attributes
        // 表示を画面上方にする
        wm!!.gravity = Gravity.TOP
        // 横幅を大きくする
        wm!!.width = resources.displayMetrics.widthPixels
        nDialog.window!!.attributes = wm
        // 通知ダイアログ表示
        nDialog.show()
        notificationDialogArea!!.setOnSafeClickListener { NotificationTap(receiver, nDialog) }
        // 3秒で通知を閉じる
        handle.postDelayed(run, 3000)
    }

    fun NotificationTap(receiver: BroadcastReceiver, nDialog: NDialog) {
        val intent = Intent(applicationContext, MessageGroupListActivity::class.java)
        startActivity(intent)
        if (localClassName.substring(localClassName.indexOf(".")+1) != "MainActivity") {
            finish()
        }
        unregisterReceiver(receiver)
        nDialog.cancel()
    }

    inner class NDialog(context: Context) : Dialog(context, R.style.NDialog) {
        init {
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            this.setContentView(R.layout.notification_dialog)
            notificationDialogArea = findViewById(R.id.nDialog) as Button
            notificationDialogMessage = findViewById(R.id.nDialog_message) as TextView
            notificationDialogTime = findViewById(R.id.nDialog_time) as TextView

        }
    }

    // モーダルを表示
    fun showHub(message: String = (context ?: this).getString(R.string.connecting)){
        if (this.progressDialog != null) {
            this.progressDialog?.show()
        }else{
            showProgressDialog(message, DialogInterface.OnDismissListener {
                if (progressCancelFlag) {
                    return@OnDismissListener
                }
            })
        }
    }

    // モーダルを削除
    fun hideHub(){
        if (this.progressDialog != null) {
            if ((progressDialog ?: return).isShowing) {
                try {
                    //progressDialog?.hide()
                    progressDialog?.dismiss()
                    progressDialog = null
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    // ログイン画面へ
    fun moveLogin() {
        val intent = Intent(applicationContext, LoginActivity::class.java)
        startActivity(intent)
        overridePendingTransition(R.anim.slide_in_up, R.anim.fade_out)
    }

    companion object {

        var context: Context? = null
            set                                // インターフェース

        fun log(className: String, message: String) {
            if (Constant.DEBUG) {
                Log.d(className, message)
            }
        }

        /* メモリ解放 */
        fun cleanupView(view: View?, parentView : View? = null) {
            if (view != null) {
                if (view is ImageButton) {

                    val ib = view as ImageButton?
                    ib!!.setImageDrawable(null)
                } else if (view is ImageView) {
                    /* SwipeRefreshLayout内のonDetachedFromWindow対策
                       SwipeRefreshLayout内のImageViewのdrawableがnullになり
                       SwipeRefreshLayoutのonDetachedFromWindowでimageView.getBackgroundを参照しておりヌルぽを防ぐ。
                    */
                    if (parentView != null && parentView is SwipeRefreshLayout) {
                        return
                    }

                    val iv = view as ImageView?
                    iv!!.setImageDrawable(null)
                }
                //view.background = null
                if (view is ViewGroup) {
                    val vg = view as ViewGroup?
                    val size = vg!!.childCount
                    for (i in 0 until size) {
                        cleanupView(vg.getChildAt(i), view)
                    }
                }
            }
        }
    }

    fun checkPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf(
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.CAMERA
                ),
                Constant.PERMISSION_REQUEST_STORAGE
            )
            return
        }
        // 権限の取得、付与（Android6以上）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (ActivityCompat.checkSelfPermission(
                    this,
                    Manifest.permission.CAMERA
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                if (ActivityCompat.checkSelfPermission(
                        this,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE
                    ) != PackageManager.PERMISSION_GRANTED
                ) {
                    // カメラとストレージの権限が無い場合、両方の許可を求める
                    ActivityCompat.requestPermissions(
                        this,
                        arrayOf(
                            Manifest.permission.CAMERA,
                            Manifest.permission.WRITE_EXTERNAL_STORAGE
                        ),
                        Constant.PERMISSION_REQUEST_BOTH
                    )
                } else {
                    // カメラの権限の許可を求める
                    ActivityCompat.requestPermissions(
                        this,
                        arrayOf(Manifest.permission.CAMERA),
                        Constant.PERMISSION_REQUEST_CAMERA
                    )
                }
            } else if (ActivityCompat.checkSelfPermission(
                    this,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                // ストレージの権限の許可を求める
                ActivityCompat.requestPermissions(
                    this,
                    arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
                    Constant.PERMISSION_REQUEST_STORAGE
                )
            }
        }
    }

    // 権限許可・拒否の選択した結果の受け取り
    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        when (requestCode) {
            Constant.PERMISSION_REQUEST_CAMERA -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    log("権限", "カメラ許可")
                } else {
                    // 権限が許可されなかった場合、警告を出す
                    permissionDenied()
                }
            }
            Constant.PERMISSION_REQUEST_STORAGE -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    log("権限", "ストレージ許可")
                } else {
                    // 権限が許可されなかった場合、警告を出す
                    permissionDenied()
                }
            }
            Constant.PERMISSION_REQUEST_BOTH -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    log("権限", "カメラ許可")
                }
                if (grantResults[1] == PackageManager.PERMISSION_GRANTED) {
                    log("権限", "ストレージ許可")
                }
                if (grantResults.indexOf(-1) != -1) {
                    // 権限が許可されなかった場合、警告を出す
                    permissionDenied()
                }
            }
            Constant.PERMISSION_REQUEST_GALLERY -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    log("権限", "ストレージ許可")
                } else {
                    // 権限が許可されなかった場合、警告を出す
                    permissionDenied()
                }
            }
        }
    }

    // 権限不許可
    private fun permissionDenied() =
        ShowMessages().Show(context, context!!.getString(R.string.permission_error))
}
