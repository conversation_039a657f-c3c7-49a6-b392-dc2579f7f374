package com.artifex.mupdfdemo;

import android.graphics.RectF;

public class Annotation extends RectF {
	enum Type {
		TEXT, LINK, FREETEXT, LINE, SQUAR<PERSON>, CIRCLE, <PERSON><PERSON><PERSON>G<PERSON>, POLYLINE, <PERSON><PERSON><PERSON><PERSON>HT,
		UNDERLIN<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>O<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>MENT,
		SOUND, MOVIE, WIDGET, SCREEN, PRINTERMARK, TRAPNET, WATERMARK, A3D, UNKNOWN
	}

	public final Type type;

	public Annotation(float x0, float y0, float x1, float y1, int _type) {
		super(x0, y0, x1, y1);
		type = _type == -1 ? Type.UNKNOWN : Type.values()[_type];
	}
}
