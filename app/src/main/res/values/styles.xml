<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="Animation.ActivitySlide" parent="android:Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/activity_left_in</item>
        <item name="android:activityOpenExitAnimation">@anim/activity_left_out</item>
        <item name="android:activityCloseEnterAnimation">@anim/activity_right_in</item>
        <item name="android:activityCloseExitAnimation">@anim/activity_right_out</item>
    </style>

    <style name="Animation.ActivityFade" parent="android:Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/activity_open_enter</item>
        <item name="android:activityOpenExitAnimation">@anim/activity_open_exit</item>
        <item name="android:activityCloseEnterAnimation">@anim/activity_close_enter</item>
        <item name="android:activityCloseExitAnimation">@anim/activity_close_exit</item>
    </style>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="AppTheme.DialogTheme" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowMinWidthMajor">100%</item>
        <item name="android:windowMinWidthMinor">100%</item>
    </style>

    <style name="MyDialog" parent="AppTheme.DialogTheme">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.MyDialog</item>
    </style>

    <style name="Animation.MyDialog" parent="android:Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/options_panel_enter</item>
        <item name="android:windowExitAnimation">@anim/options_panel_exit</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.ActionBar" />

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="NDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowAnimationStyle">@style/Animation.NDialog</item>
        <item name="android:backgroundDimAmount">0.0</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="Animation.NDialog" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/dialog_anim_enter</item>
        <item name="android:windowExitAnimation">@anim/dialog_anim_exit</item>
    </style>

    <style name="trans" parent="android:Theme.Holo.Light">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimAmount">0.0</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@style/Animation.NDialog</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- Checkbox -->
    <style name="CustomCheckbox" parent="android:Widget.CompoundButton.CheckBox">
        <item name="android:button">@drawable/custom_checkbox</item>
    </style>

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="section_sub_title">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">30dp</item>
        <item name="android:background">@null</item>
        <item name="android:gravity">bottom</item>
        <item name="android:layout_marginLeft">15dp</item>
        <item name="android:layout_marginBottom">5dp</item>
        <item name="android:textColor">#6E6D73</item>
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily" tools:ignore="NewApi">sans-serif-light</item>
    </style>

    <style name="section_sub_title_text">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:background">@null</item>
        <item name="android:gravity">bottom</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingBottom">5dp</item>
        <item name="android:textColor">#6E6D73</item>
        <item name="android:textSize">12sp</item>
    </style>

    <style name="vertical_line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1px</item>
        <item name="android:background">@color/gray</item>
    </style>

    <style name="vertical_line_dark">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1px</item>
        <item name="android:background">@color/dark_gray</item>
    </style>

    <style name="horizon_line">
        <item name="android:layout_width">10px</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:background">@color/red</item>
    </style>

    <style name="horizon_dark_line">
        <item name="android:layout_width">1px</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:background">@color/dark_gray</item>
    </style>

</resources>
