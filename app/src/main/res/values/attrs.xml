<resources>
    <!-- AspectFrame -->
    <declare-styleable name="FixedAspectFrameLayout">
        <attr name="aspectRate" format="float"/>
    </declare-styleable>

    <declare-styleable name="LightSaberView">
        <attr name="exampleString" format="string" />
        <attr name="exampleDimension" format="dimension" />
        <attr name="exampleColor" format="color" />
        <attr name="exampleDrawable" format="color|reference" />
    </declare-styleable>

    <declare-styleable name="CalendarView">
        <attr name="headerColor" format="color" />
        <attr name="headerLabelColor" format="color" />
        <attr name="headerVisibility" format="enum" >
            <enum name="visible" value="0"/>
            <enum name="invisible" value="4"/>
            <enum name="gone" value="8"/>
        </attr>
        <attr name="previousButtonSrc" format="integer" />
        <attr name="forwardButtonSrc" format="integer" />
        <attr name="todayLabelColor" format="color" />
        <attr name="selectionColor" format="color" />
        <attr name="selectionLabelColor" format="color" />
        <attr name="disabledDaysLabelsColor" format="color" />
        <attr name="highlightedDaysLabelsColor" format="color" />
        <attr name="pagesColor" format="color" />
        <attr name="abbreviationsBarColor" format="color" />
        <attr name="abbreviationsLabelsColor" format="color" />
        <attr name="daysLabelsColor" format="color" />
        <attr name="anotherMonthsDaysLabelsColor" format="color" />
        <attr name="datePicker" format="boolean" />
        <attr name="eventsEnabled" format="boolean" />
        <attr name="swipeEnabled" format="boolean" />
        <attr name="maximumDaysRange" format="integer" />
        <attr name="type" />
    </declare-styleable>

    <attr name="type" format="enum">
        <enum name="classic" value="0"/>
        <enum name="one_day_picker" value="1"/>
        <enum name="many_days_picker" value="2"/>
        <enum name="range_picker" value="3"/>
    </attr>

    <declare-styleable name="WeekView">
        <attr name="firstDayOfWeek" format="enum">
            <enum name="sunday" value="1"/>
            <enum name="monday" value="2"/>
            <enum name="tuesday" value="3"/>
            <enum name="wednesday" value="4"/>
            <enum name="thursday" value="5"/>
            <enum name="friday" value="6"/>
            <enum name="saturday" value="7"/>
        </attr>
        <attr name="hourHeight" format="dimension"/>
        <attr name="minHourHeight" format="dimension"/>
        <attr name="maxHourHeight" format="dimension"/>
        <attr name="textSize" format="dimension"/>
        <attr name="headerTextSize" format="dimension"/>
        <attr name="eventTextSize" format="dimension"/>
        <attr name="headerColumnPadding" format="dimension"/>
        <attr name="hourColumnPadding" format="dimension"/>
        <attr name="headerRowPadding" format="dimension"/>
        <attr name="columnGap" format="dimension"/>
        <attr name="headerColumnTextColor" format="color"/>
        <attr name="noOfVisibleDays" format="integer"/>
        <attr name="showFirstDayOfWeekFirst" format="boolean"/>
        <attr name="headerRowBackgroundColor" format="color"/>
        <attr name="dayBackgroundColor" format="color"/>
        <attr name="hourSeparatorColor" format="color"/>
        <attr name="todayBackgroundColor" format="color"/>
        <attr name="todayHeaderTextColor" format="color"/>
        <attr name="hourSeparatorHeight" format="dimension"/>
        <attr name="eventTextColor" format="color"/>
        <attr name="eventPadding" format="dimension"/>
        <attr name="headerColumnBackground" format="color"/>
        <attr name="dayNameToLength" format="enum">
            <enum name="length_short" value="1"/>
            <enum name="length_long" value="2"/>
        </attr>
        <attr name="overlappingEventGap" format="dimension"/>
        <attr name="eventMarginVertical" format="dimension"/>
        <attr name="xScrollingSpeed" format="float"/>
        <attr name="eventCornerRadius" format="dimension"/>
        <attr name="showDistinctPastFutureColor" format="boolean"/>
        <attr name="showDistinctWeekendColor" format="boolean"/>
        <attr name="futureBackgroundColor" format="color"/>
        <attr name="pastBackgroundColor" format="color"/>
        <attr name="futureWeekendBackgroundColor" format="color"/>
        <attr name="pastWeekendBackgroundColor" format="color"/>
        <attr name="showNowLine" format="boolean"/>
        <attr name="nowLineColor" format="color"/>
        <attr name="nowLineThickness" format="dimension"/>
        <attr name="horizontalFlingEnabled" format="boolean"/>
        <attr name="verticalFlingEnabled" format="boolean"/>
        <attr name="allDayEventHeight" format="dimension"/>
        <attr name="scrollDuration" format="integer"/>
        <attr name="headerShow" format="boolean"/>
    </declare-styleable>

    <!-- WeekCalendar -->
    <declare-styleable name="WeekCalendar">
        <attr name="numOfPages" format="integer"/>
        <attr name="daysTextSize" format="dimension"/>
        <attr name="daysTextColor" format="color"/>
        <attr name="daysBackgroundColor" format="color"/>
        <attr name="weekTextSize" format="dimension"/>
        <attr name="weekTextColor" format="color"/>
        <attr name="weekBackgroundColor" format="color"/>
        <attr name="selectedBgColor" format="color"/>
        <attr name="selectedTextColor" format="color"/>
        <attr name="todaysDateBgColor" format="color"/>
        <attr name="sundayTextColor" format="color"/>
        <attr name="saturdayTextColor" format="color"/>
        <attr name="dayNameLength">
            <enum name="singleLetter" value="0"/>
            <enum name="threeLetters" value="1"/>
        </attr>
        <attr name="hideNames"  format="boolean"/>

        <attr name="todaysDateTextColor" format="color"/>
    </declare-styleable>
</resources>
