<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/dialog_bg"
        android:minWidth="60pt"
        android:orientation="vertical"
        android:paddingBottom="15dp"
        android:paddingLeft="3dp"
        android:paddingRight="3dp"
        android:paddingTop="15dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:orientation="horizontal">

            <ProgressBar
                android:id="@+id/progressBar2"
                style="@android:style/Widget.DeviceDefault.ProgressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:max="10000" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/messageTextView"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginTop="4dp"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textSize="12dp" />
        </LinearLayout>

    </LinearLayout>

</FrameLayout>