<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="@drawable/com_header"
        android:theme="@style/AppTheme.AppBarOverlay"
        app:contentInsetStart="0dp"
        app:layout_constraintBottom_toTopOf="@id/pref_city_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/close_arrow"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_centerVertical="true"
            android:layout_gravity="left"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:src="@drawable/com_headerarrow_down"
            tools:layout_editor_absoluteY="5dp"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_centerVertical="true"
            android:layout_gravity="left"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:src="@drawable/white_arrow_l"
            tools:ignore="ContentDescription"
            tools:layout_editor_absoluteY="5dp" />

        <TextView
            android:id="@+id/back_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left"
            android:ellipsize="end"
            android:maxEms="10"
            android:maxLines="1"
            android:textColor="@color/orange_link"
            android:textSize="20sp"
            android:textStyle="bold"
            android:visibility="gone"
            tools:ignore="RtlHardcoded" />

        <TextView
            android:id="@+id/toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:ellipsize="end"
            android:maxEms="10"
            android:maxLines="1"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

    </androidx.appcompat.widget.Toolbar>

    <ListView
        android:id="@+id/pref_city_name"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:divider="@color/gray"
        android:dividerHeight="1px"

        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/index_name"
        app:layout_constraintHorizontal_bias="0.0"

        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintStart_toStartOf="@id/toolbar"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <LinearLayout
        android:id="@+id/index_name"
        android:layout_width="60dp"
        android:layout_height="0dp"
        android:background="@android:color/transparent"

        android:gravity="center_vertical"
        android:orientation="vertical"

        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        app:layout_constraintBottom_toBottomOf="parent"

        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintRight_toLeftOf="@id/pref_city_name"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

</androidx.constraintlayout.widget.ConstraintLayout>