<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:scrollbarStyle="insideOverlay"
    tools:context=".activity.survey.ShootingGalleryActivity">

    <RelativeLayout
        android:id="@+id/toolbar_frame"
        android:layout_width="match_parent"
        android:layout_height="45dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentTop="true"
            android:layout_marginTop="0dp"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay"
            app:contentInsetStart="0dp">

            <ImageView
                android:id="@+id/back_arrow"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_centerVertical="true"
                android:layout_gravity="left|center"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:src="@drawable/white_arrow_l" />

            <TextView
                android:id="@+id/survey_property_toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/shooting_gallery_title"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/filter_select"
                android:layout_width="wrap_content"
                android:layout_height="45dp"
                android:layout_alignParentRight="true"
                android:layout_gravity="end"
                android:gravity="center|right"
                android:minWidth="60dp"
                android:paddingRight="15dp"
                android:text="@string/shooting_all"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

        </androidx.appcompat.widget.Toolbar>


    </RelativeLayout>
    <!--List view-->
    <LinearLayout
        android:id="@+id/contents"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar_frame"
        >

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/gallery_swipe_refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ListView
                android:id="@+id/shooting_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:divider="@color/gray"
                android:dividerHeight="1px"
                android:listSelector="@drawable/ripple_transparent"
                android:drawSelectorOnTop="true" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </LinearLayout>

</RelativeLayout>
