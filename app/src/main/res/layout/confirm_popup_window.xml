<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/redius_frame_white"

    android:orientation="vertical">

    <TextView
        android:id="@+id/title_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="5dp"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold"
        app:fontFamily="sans-serif" />

    <TextView
        android:id="@+id/detail_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="14sp"
        app:fontFamily="sans-serif-light" />

    <LinearLayout
        style="@style/vertical_line"
        android:background="@color/black" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight=".5"
            android:background="@drawable/ripple_white"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="@color/light_blue"
            android:textSize="14sp"
            app:fontFamily="sans-serif-black" />

        <LinearLayout
            android:layout_width="1px"
            android:background="@color/black"
            style="@style/horizon_line" />

        <TextView
            android:id="@+id/positive"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight=".5"
            android:background="@drawable/ripple_white"
            android:gravity="center"
            android:text="@string/dialog_positive"
            android:textColor="@color/light_blue"
            android:textSize="14sp"
            app:fontFamily="sans-serif-light" />


    </LinearLayout>
</LinearLayout>