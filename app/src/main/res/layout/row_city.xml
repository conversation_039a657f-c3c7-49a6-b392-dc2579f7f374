<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="50dp"
    android:descendantFocusability="blocksDescendants"
    android:minHeight="50dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/city_name"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight=".55"
        android:gravity="bottom"
        android:paddingStart="20dp"
        android:paddingLeft="15dp"
        android:text="tesxt"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:fontFamily="sans-serif-light" />

    <TextView
        android:id="@+id/city_name_kana"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight=".45"
        android:gravity="top"
        android:paddingStart="20dp"
        android:paddingLeft="15dp"
        android:text="text"
        android:textColor="@android:color/darker_gray"
        android:textSize="11sp"
        app:fontFamily="sans-serif-light" />
</LinearLayout>