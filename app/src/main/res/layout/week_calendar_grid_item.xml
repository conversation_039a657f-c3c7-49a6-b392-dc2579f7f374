<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="53dp"
    android:orientation="vertical">

    <TextView

        android:id="@+id/daytext"
        android:layout_width="@dimen/day_text_wh"
        android:layout_height="@dimen/day_text_wh"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="3dp"
        android:gravity="center"
        android:text="20"
        android:textColor="@android:color/white"
        android:textSize="17sp" />

    <TextView
        android:id="@+id/check_mark"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_below="@id/daytext"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="2dp"
        android:gravity="center_horizontal|center_vertical"
        android:text="●"
        android:textColor="@color/button_gray"
        android:textSize="5sp"
        android:visibility="gone" />

</RelativeLayout>