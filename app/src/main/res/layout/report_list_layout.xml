<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main_layout"
    android:layout_width="wrap_content"
    android:layout_height="fill_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:scrollbarStyle="insideOverlay">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="fill_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:orientation="horizontal" >

        <androidx.appcompat.widget.Toolbar
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:maxLines="1"
                android:maxEms="10"
                android:ellipsize="end"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"/>

        </androidx.appcompat.widget.Toolbar>

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:paddingBottom="10dp"
            android:paddingTop="10dp"
            android:src="@drawable/com_headerarrow"
            android:layout_centerVertical="true"
            android:layout_alignParentLeft="true" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/LayoutLine"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:background="@color/gray"
        android:orientation="vertical"
        android:layout_below="@+id/LayoutHeader" />

    <TextView
        android:id="@+id/report"
        android:layout_width="fill_parent"
        android:layout_height="46dp"
        android:layout_below="@+id/LayoutLine"
        android:background="@color/title_gray"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:paddingLeft="15dp"
        android:text="@string/report_list"
        android:textColor="@color/black"
        android:textSize="18sp"
        android:textStyle="bold" />

    <FrameLayout
        android:id="@+id/LayoutLine2"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:background="@color/gray"
        android:layout_below="@+id/report" />

    <ListView
        android:id="@+id/report_list_listview"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_below="@+id/LayoutLine2"
        android:divider="@color/gray"
        android:dividerHeight="1px"/>

</RelativeLayout>
