<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="40dp"
    android:descendantFocusability="blocksDescendants"
    android:minHeight="40dp"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/region_name"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/contents_background"
        android:gravity="bottom|center_vertical"
        android:paddingStart="20dp"
        android:paddingLeft="15dp"
        android:paddingBottom="3dp"
        android:textColor="@color/dark_gray"
        android:textSize="14sp"
        app:fontFamily="sans-serif-light" />


</LinearLayout>