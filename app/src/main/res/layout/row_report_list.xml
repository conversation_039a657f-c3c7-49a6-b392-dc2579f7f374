<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="60dp"
    android:minHeight="60dp"
    android:background="@color/white"
    android:descendantFocusability="blocksDescendants">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="60dp"
        android:minHeight="60dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/report_date"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:gravity="center_vertical"
            android:textSize="15sp"
            android:textColor="@color/black"
            android:layout_marginLeft="8dp" />

        <FrameLayout
            android:id="@+id/report_list_spacer"
            android:layout_width="match_parent"
            android:layout_height="18dp"
            android:minHeight="18dp"
            android:visibility="gone"/>

    </LinearLayout>

    <TextView
        android:id="@+id/report_work_contents"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true"
        android:layout_alignParentBottom="true"
        android:layout_toStartOf="@+id/report_schedule_button"
        android:layout_marginStart="10dp"
        android:paddingBottom="10dp"
        android:gravity="bottom"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="12sp"
        android:textColor="@color/black"
        android:visibility="gone"
        android:layout_alignParentLeft="true"
        android:layout_toLeftOf="@+id/report_schedule_button"
        android:layout_marginLeft="10dp" />

    <ImageView
        android:id="@+id/report_result_button"
        android:layout_width="80dp"
        android:layout_height="40dp"
        android:layout_centerVertical="true"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="10dp"
        android:src="@drawable/resultbefore"
        android:layout_alignParentRight="true"
        android:layout_marginRight="5dp" />

    <ImageView
        android:id="@+id/report_schedule_button"
        android:layout_width="80dp"
        android:layout_height="40dp"
        android:layout_centerVertical="true"
        android:layout_toStartOf="@+id/report_result_button"
        android:layout_marginEnd="5dp"
        android:src="@drawable/planbefore"
        android:layout_toLeftOf="@+id/report_result_button"
        android:layout_marginRight="5dp" />

    <ImageView
        android:id="@+id/work_process_button"
        android:layout_width="80dp"
        android:layout_height="40dp"
        android:layout_centerVertical="true"
        android:layout_marginEnd="5dp"
        android:layout_marginRight="5dp"
        android:layout_toStartOf="@+id/report_schedule_button"
        android:layout_toLeftOf="@+id/report_schedule_button"
        android:src="@drawable/work_process_complete" />

</RelativeLayout>