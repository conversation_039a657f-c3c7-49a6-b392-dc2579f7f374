<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@null"
    android:orientation="vertical">

    <fois.dailyreportsystem.util.weekcalendar.WeekCalendar
        android:id="@+id/weekCalendar"
        android:layout_width="match_parent"
        android:layout_height="78dp"
        android:background="@color/white"
        app:daysBackgroundColor="@color/white"
        app:daysTextColor="@color/black"
        app:weekBackgroundColor="@color/white"
        app:weekTextColor="@color/black"
        app:weekTextSize="14dp"
        app:todaysDateBgColor="@color/calendar_today"
        app:selectedBgColor="@color/calendar_select_day"
        app:selectedTextColor="@color/white"
        app:sundayTextColor="@color/red"
        app:saturdayTextColor="@color/blue"
        app:daysTextSize="20dp"
        app:numOfPages="150"
        app:dayNameLength="threeLetters"/>

    <LinearLayout
        android:id="@+id/line1"
        style="@style/vertical_line"
        android:layout_marginBottom="5dp"
        android:orientation="horizontal" />

    <ListView
        android:id="@+id/order_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/contents_background"
        android:dividerHeight="1px" />

</LinearLayout>
