<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:background="@drawable/ripple_white"
    android:orientation="vertical"
    android:padding="5dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"

        android:layout_marginStart="5dp"
        android:layout_weight=".55"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/area_name"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:background="@drawable/square_frame_gray"
            android:gravity="center|center_vertical"
            android:paddingStart="5dp"
            android:paddingLeft="10dp"
            android:paddingEnd="5dp"
            android:paddingRight="10dp"
            android:textColor="@color/white"
            android:textSize="13sp"
            app:fontFamily="sans-serif-light" />

        <TextView
            android:id="@+id/address"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:paddingLeft="3dp"
            android:textColor="@color/dark_gray"
            android:textSize="14sp"
            app:fontFamily="sans-serif-light" />


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"

        android:layout_marginStart="5dp"

        android:layout_weight=".45"
        android:orientation="horizontal">


        <TextView
            android:id="@+id/company_name"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight=".4"
            android:gravity="center_vertical"
            android:textColor="@color/black"
            android:textSize="12sp"
            app:fontFamily="sans-serif-light" />

        <TextView
            android:id="@+id/task_name"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight=".6"
            android:gravity="center_vertical"
            android:text="aaaaaaa"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:fontFamily="sans-serif-medium" />

    </LinearLayout>
</LinearLayout>