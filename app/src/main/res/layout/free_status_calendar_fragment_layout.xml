<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/free_status_calendar_fragment"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@null"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:scrollbarStyle="insideOverlay">

    <fois.dailyreportsystem.util.weekview.FreeWeekView
        android:id="@+id/weekView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/white"

        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"

        app:textSize="12sp"
        app:headerTextSize="18sp"
        app:eventTextColor="@android:color/white"
        app:hourHeight="45dp"
        app:headerColumnPadding="20dp"
        app:hourColumnPadding="8dp"
        app:headerColumnTextColor="#6D6D6D"
        app:headerRowPadding="15dp"
        app:todayHeaderTextColor="@color/todayColor"
        app:showNowLine="true"
        app:nowLineColor="@color/todayColor"
        app:columnGap="0dp"
        app:noOfVisibleDays="3"
        app:dayNameToLength="length_short"
        app:headerRowBackgroundColor="#ffffffff"
        app:dayBackgroundColor="#ffffffff"
        app:todayBackgroundColor="#ffffffff"
        app:headerColumnBackground="#ffffffff"
        app:showDistinctPastFutureColor="true"
        app:showDistinctWeekendColor="true"
        app:pastBackgroundColor="#ffffffff"
        app:pastWeekendBackgroundColor="#E6E5F8"
        app:futureBackgroundColor="#ffffffff"
        app:futureWeekendBackgroundColor="#E6E5F8"/>

    <Button
        android:id="@+id/month"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="50dp"
        android:layout_height="43dp"
        android:background="@color/white"
        android:scrollbarThumbHorizontal="@null"
        android:scrollbarTrackVertical="@null"
        android:textColor="@color/todayColor"
        android:textColorHighlight="@null"
        android:textColorHint="@null"
        android:textCursorDrawable="@null"
        android:textSize="17sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
