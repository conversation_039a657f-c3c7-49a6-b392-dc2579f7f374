<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="10dp"
    android:paddingTop="10dp"
    android:background="@color/contents_gray"
    android:orientation="vertical">

    <TextView
        android:id="@+id/message_sender"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:paddingStart="10dp"
        android:paddingEnd="40dp"
        android:textSize="12sp"
        android:textColor="@color/dark_gray"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:paddingStart="10dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/message_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="15dp"
                android:background="@drawable/message_get"
                android:gravity="center_vertical"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:lineSpacingMultiplier="1.2"/>

        </LinearLayout>

        <TextView
            android:id="@+id/message_send_time"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:paddingStart="5dp"
            android:paddingBottom="5dp"
            android:gravity="bottom"
            android:textColor="@color/dark_gray"/>

    </LinearLayout>

</LinearLayout>