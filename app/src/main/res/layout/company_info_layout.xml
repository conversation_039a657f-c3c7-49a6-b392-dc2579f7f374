<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    tools:context="fois.dailyreportsystem.activity.setting.CompanyInfoActivity">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="fill_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:orientation="horizontal" >

        <androidx.appcompat.widget.Toolbar
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/company_info_title"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"/>

        </androidx.appcompat.widget.Toolbar>

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:paddingBottom="10dp"
            android:paddingTop="10dp"
            android:src="@drawable/com_headerarrow"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"/>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/LayoutLine"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/LayoutHeader"
        android:background="@color/gray"
        android:orientation="vertical" />

    <ListView
        android:id="@+id/company_info_listview"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_below="@+id/LayoutLine"
        android:layout_above="@+id/LayoutLine2"
        android:divider="@color/gray"
        android:dividerHeight="1px"/>

    <LinearLayout
        android:id="@+id/LayoutLine2"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_above="@+id/account_change"
        android:background="@color/gray"
        android:orientation="vertical" />

    <FrameLayout
        android:id="@+id/account_change"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:padding="10dp">

        <ImageView
            android:id="@+id/account_change_button"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:src="@drawable/login"
            android:scaleType="fitCenter"/>

    </FrameLayout>

</RelativeLayout>
