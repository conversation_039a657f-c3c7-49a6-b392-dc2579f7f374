<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="50dp"
    android:minHeight="50dp"
    android:descendantFocusability="blocksDescendants"
    android:orientation="horizontal">

    <LinearLayout
        android:id="@+id/photo_shoot_content"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@color/list">

        <TextView
            android:id="@+id/photo_shoot_content_name"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:paddingStart="15dp"
            android:paddingEnd="15dp"
            android:gravity="center_vertical"
            android:textSize="18sp"
            android:textColor="@color/light_blue"/>

        <TextView
            android:id="@+id/photo_shoot_count"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginEnd="10dp"
            android:gravity="center_vertical"
            android:textSize="16sp"
            android:textColor="@color/black"/>

        <ImageView
            android:id="@+id/right_arrow"
            android:layout_width="wrap_content"
            android:layout_height="12dp"
            android:layout_marginEnd="8dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/com_arrow01"
            android:scaleType="fitCenter"/>

    </LinearLayout>

    <View
        android:layout_width="1dp"
        android:layout_height="match_parent"
        android:background="@color/gray" />

    <FrameLayout
        android:id="@+id/photo_shoot_camera_button"
        android:layout_width="55dp"
        android:layout_height="match_parent"
        android:background="@color/list">

        <ImageView
            android:id="@+id/photo_shoot_camera"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingStart="15dp"
            android:paddingEnd="15dp"
            android:src="@drawable/camera"
            android:scaleType="fitCenter"/>

    </FrameLayout>

</LinearLayout>