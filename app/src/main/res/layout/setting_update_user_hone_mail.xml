<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/contents_gray"
    android:focusable="true"
    android:focusableInTouchMode="true"
    tools:context="fois.dailyreportsystem.activity.setting.UpdateUserPhoneMailActivity">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="fill_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:orientation="horizontal">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/setting_title"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

        </androidx.appcompat.widget.Toolbar>

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:src="@drawable/com_headerarrow" />

        <TextView
            android:id="@+id/change"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="15dp"
            android:gravity="center_vertical"
            android:text="@string/change"
            android:textColor="@color/white"
            android:textSize="16sp" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/LayoutLine"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/LayoutHeader"
        android:background="@color/gray"
        android:orientation="vertical" />

    <TextView
        android:id="@+id/mail_info"
        android:layout_width="fill_parent"
        android:layout_height="38dp"
        android:layout_below="@+id/LayoutLine"
        android:background="@color/contents_gray"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:paddingLeft="15dp"
        android:text="@string/mailaddress"
        android:textColor="@color/dark_gray"
        android:textSize="16sp" />

    <LinearLayout
        android:id="@+id/LayoutLine2"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/mail_info"
        android:background="@color/gray"
        android:orientation="vertical" />


    <LinearLayout
        android:id="@+id/phone_mail"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_below="@+id/LayoutLine2"
        android:background="@color/white">

        <TextView
            android:id="@+id/phone_mail_text"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:text="@string/photo_mail"
            android:textColor="@color/light_blue"
            android:textSize="18sp" />

        <EditText
            android:id="@+id/phone_mail_input"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="8"
            android:background="@null"
            android:gravity="end|center_vertical"
            android:inputType="textEmailAddress"
            android:maxLength="60"
            android:paddingRight="15dp"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <ImageView
            android:layout_width="15dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="15dp"
            android:scaleType="fitCenter"
            android:src="@drawable/com_pencil" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/LayoutLine3"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/phone_mail"
        android:background="@color/gray"
        android:orientation="vertical" />


    <LinearLayout
        android:id="@+id/phone_mail_change_area"
        android:layout_width="match_parent"
        android:layout_height="61dp"
        android:layout_alignParentBottom="true"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="1dp"
            android:background="@color/gray"
            android:orientation="vertical" />

        <FrameLayout
            android:id="@+id/confirm_square"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@color/white"
            android:padding="10dp">

            <Button
                android:id="@+id/confirm_button"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/register_button"
                android:scaleType="fitCenter"
                android:text="変更する"
                android:textColor="@color/white"
                android:textSize="18sp" />

        </FrameLayout>

    </LinearLayout>


</RelativeLayout>
