<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="45dp">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/com_header"
        android:theme="@style/AppTheme.AppBarOverlay"
        app:contentInsetStart="0dp">


        <ImageView
            android:id="@+id/add_plan"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_gravity="right"
            android:paddingTop="15dp"
            android:paddingBottom="15dp"
            android:src="@drawable/plus"
            tools:ignore="ContentDescription,RtlHardcoded" />

        <ImageView
            android:id="@+id/menu_cancel"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:contentDescription="@string/app_name"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:src="@drawable/white_line_arrow_r" />

        <TextView
            android:id="@+id/plan_header_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />


    </androidx.appcompat.widget.Toolbar>


</FrameLayout>