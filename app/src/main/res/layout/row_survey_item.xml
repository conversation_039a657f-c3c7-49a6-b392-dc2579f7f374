<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/row_survey"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/first_row"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:layout_marginStart="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="5dp"
        android:layout_marginRight="4dp"
        android:orientation="horizontal"
        android:weightSum="1">

        <TextView
            android:id="@+id/survey_date"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="0.7"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:fontFamily="sans-serif" />

        <TextView
            android:id="@+id/limit_date"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="0.3"
            android:background="@null"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="12sp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/second_row"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/first_row"
        android:layout_marginStart="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginEnd="5dp"
        android:layout_marginRight="5dp"
        android:minHeight="22dp"
        android:orientation="horizontal"
        android:weightSum="1">

        <TextView
            android:id="@+id/area_name"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:background="@drawable/square_frame_gray"
            android:gravity="center"
            android:maxLines="1"
            android:minWidth="60dp"
            android:text="東京都"
            android:textColor="@color/white"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/address"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="left|center_vertical"
            android:layout_marginStart="5dp"
            android:layout_marginLeft="5dp"
            android:layout_weight="1"
            android:background="@null"
            android:gravity="center_vertical"
            android:maxLines="2"
            android:textColor="@color/dark_gray"
            android:textSize="13sp"
            app:fontFamily="sans-serif-light" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/third_row"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/second_row"
        android:layout_marginStart="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginEnd="5dp"
        android:layout_marginRight="5dp"
        android:layout_marginBottom="5dp"
        android:minHeight="22dp"
        android:orientation="horizontal"
        android:weightSum="1">

        <TextView
            android:id="@+id/property_kind"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="left|center_vertical"
            android:layout_weight="0.35"
            android:background="@null"
            android:gravity="left|center"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="12sp"
            app:fontFamily="sans-serif-light" />

        <TextView
            android:id="@+id/property_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="left|center_vertical"
            android:layout_weight="0.4"
            android:background="@null"
            android:gravity="center_vertical"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:fontFamily="sans-serif-medium" />

        <TextView
            android:id="@+id/survey_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="right|center_vertical"
            android:layout_weight="0.25"
            android:background="@null"
            android:gravity="right"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="12sp"
            app:fontFamily="sans-serif-light" />

    </LinearLayout>
</RelativeLayout>