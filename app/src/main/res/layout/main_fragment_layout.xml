<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main_fragment_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:scrollbarStyle="insideOverlay">

    <FrameLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:visibility="gone"
        >

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/title_tei_ichiran"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

        </androidx.appcompat.widget.Toolbar>

    </FrameLayout>

    <RelativeLayout
        android:id="@+id/LayoutMain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/header"
        >

        <RelativeLayout
            android:id="@+id/search_layout"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:background="@color/light_gray">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="6dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="6dp"
                android:background="@drawable/main_search_rectangle">

                <SearchView
                    android:id="@+id/searchViewTask"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:iconifiedByDefault="false"
                    android:paddingEnd="20dp"
                    android:queryHint="@string/survey_property_list_search_hint"
                    android:paddingRight="20dp" />

            </FrameLayout>

        </RelativeLayout>

        <TextView
            android:id="@+id/noData"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/survey_property_list_no_data"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:visibility="gone" />

        <!--List view-->
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/LayoutListView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/search_layout"
            android:orientation="vertical">

            <ListView
                android:id="@+id/list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout >

    </RelativeLayout>

    <View
        android:id="@+id/transparent_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@null"
        android:visibility="gone" />
</RelativeLayout>
