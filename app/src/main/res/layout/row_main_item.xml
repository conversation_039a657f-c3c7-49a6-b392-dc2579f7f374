<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="fill_parent"
    android:layout_height="150dp"
    android:background="@color/light_gray"
    android:descendantFocusability="blocksDescendants" >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="5dp"
        android:background="@color/list">

        <LinearLayout
            android:id="@+id/task_info_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/task_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="7">

                <ImageView
                    android:id="@+id/task_icon"
                    android:layout_width="85dp"
                    android:layout_height="85dp"
                    android:padding="5dp"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentTop="true"
                    android:layout_marginEnd="5dp"
                    android:scaleType="fitCenter"
                    android:layout_alignParentLeft="true"
                    android:layout_marginRight="5dp" />

                <TextView
                    android:id="@+id/task_name"
                    android:layout_width="match_parent"
                    android:layout_height="20dp"
                    android:layout_alignParentTop="true"
                    android:layout_marginTop="10dp"
                    android:layout_toEndOf="@+id/task_icon"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:layout_toRightOf="@+id/task_icon" />

                <TextView
                    android:id="@+id/area_name"
                    android:layout_width="wrap_content"
                    android:layout_height="20dp"
                    android:layout_below="@+id/task_name"
                    android:layout_marginTop="4dp"
                    android:layout_toEndOf="@+id/task_icon"
                    android:background="@color/dark_gray"
                    android:gravity="center"
                    android:paddingEnd="5dp"
                    android:paddingStart="5dp"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:layout_toRightOf="@+id/task_icon" />

                <TextView
                    android:id="@+id/fct_user"
                    android:layout_width="match_parent"
                    android:layout_height="20dp"
                    android:layout_alignTop="@+id/area_name"
                    android:layout_marginStart="5dp"
                    android:layout_toEndOf="@+id/area_name"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:textColor="@color/dark_gray"
                    android:textSize="14sp"
                    android:layout_marginLeft="5dp"
                    android:layout_toRightOf="@+id/area_name" />

                <TextView
                    android:id="@+id/task_plan_date"
                    android:layout_width="match_parent"
                    android:layout_height="20dp"
                    android:layout_below="@+id/area_name"
                    android:layout_marginTop="4dp"
                    android:layout_toEndOf="@+id/task_icon"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:layout_toRightOf="@+id/task_icon" />

            </RelativeLayout>

            <LinearLayout
                android:id="@+id/task_report"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="10dp"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="5dp"
                android:layout_weight="3"
                android:background="@drawable/image_frame"
                android:orientation="horizontal"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="5dp">

                <LinearLayout
                    android:id="@+id/task_report_content"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:paddingStart="5dp"
                    android:paddingEnd="5dp"
                    android:gravity="center_vertical"
                    android:orientation="vertical"/>

                <LinearLayout
                    android:id="@+id/task_report_sub_content"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:paddingStart="5dp"
                    android:paddingEnd="5dp"
                    android:gravity="center_vertical"
                    android:orientation="vertical"/>

            </LinearLayout>

        </LinearLayout>

        <FrameLayout
            android:id="@+id/layoutLine"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_alignParentStart="true"
            android:layout_below="@+id/task_info_layout"
            android:background="@color/gray"
            android:layout_alignParentLeft="true" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true"
            android:layout_below="@+id/layoutLine"
            android:minHeight="50dp"
            android:orientation="horizontal"
            android:id="@+id/linearLayout"
            android:layout_alignParentLeft="true">

            <RelativeLayout
                android:id="@+id/message_area"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@color/list">

                <ImageView
                    android:id="@+id/message_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentTop="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="12dp"
                    android:src="@drawable/message" />

                <TextView
                    android:id="@+id/message_text"
                    android:layout_width="match_parent"
                    android:layout_height="20dp"
                    android:layout_below="@id/message_icon"
                    android:layout_marginTop="5dp"
                    android:gravity="center_horizontal"
                    android:text="@string/main_item_message"
                    android:textColor="@color/light_blue"
                    android:textSize="10sp"
                    tools:ignore="SmallSp" />

                <TextView
                    android:id="@+id/unread_badge"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_above="@id/message_icon"
                    android:layout_marginLeft="-10dp"
                    android:layout_marginBottom="-22dp"
                    android:layout_toRightOf="@id/message_icon"
                    android:background="@drawable/unread_badge"
                    android:gravity="center"
                    android:paddingBottom="1dp"
                    android:textColor="@color/white"
                    app:layout_editor_absoluteX="1dp"
                    app:layout_editor_absoluteY="1dp" />

            </RelativeLayout>

            <FrameLayout
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/gray">

            </FrameLayout>

            <RelativeLayout
                android:id="@+id/filelist_area"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@color/list">

                <ImageView
                    android:id="@+id/filelist_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="12dp"
                    android:src="@drawable/attach_file" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="20dp"
                    android:layout_below="@id/filelist_icon"
                    android:layout_marginTop="5dp"
                    android:gravity="center_horizontal"
                    android:text="@string/main_item_filelist"
                    android:textColor="@color/light_blue"
                    android:textSize="10sp"
                    tools:ignore="SmallSp" />

            </RelativeLayout>

            <FrameLayout
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/gray">

            </FrameLayout>

            <RelativeLayout
                android:id="@+id/workschedule_area"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@color/list">

                <ImageView
                    android:id="@+id/workschedule_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="12dp"
                    android:src="@drawable/work_schedule" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="20dp"
                    android:layout_below="@+id/workschedule_icon"
                    android:layout_marginTop="5dp"
                    android:gravity="center_horizontal"
                    android:text="@string/main_item_workschedule"
                    android:textColor="@color/light_blue"
                    android:textSize="10sp"
                    tools:ignore="SmallSp" />

            </RelativeLayout>

            <FrameLayout
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/gray" />

            <RelativeLayout
                android:id="@+id/process_area"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@color/list">

                <ImageView
                    android:id="@+id/process_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="12dp"
                    android:src="@drawable/camera_main" />

                <TextView
                    android:id="@+id/process_text"
                    android:layout_width="match_parent"
                    android:layout_height="20dp"
                    android:layout_below="@+id/process_icon"
                    android:layout_marginTop="5dp"
                    android:gravity="center_horizontal"
                    android:text="@string/main_item_step"
                    android:textColor="@color/light_blue"
                    android:textSize="10sp"
                    tools:ignore="SmallSp" />

            </RelativeLayout>

            <FrameLayout
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/gray" >

            </FrameLayout>

            <RelativeLayout
                android:id="@+id/report_area"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@color/list">

                <ImageView
                    android:id="@+id/report_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="12dp"
                    android:src="@drawable/report" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="20dp"
                    android:layout_below="@+id/report_icon"
                    android:layout_marginTop="5dp"
                    android:gravity="center_horizontal"
                    android:text="@string/main_item_report"
                    android:textColor="@color/light_blue"
                    android:textSize="10sp"
                    tools:ignore="SmallSp" />

            </RelativeLayout>

        </LinearLayout>

    </RelativeLayout>

</RelativeLayout>