<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="240dp"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/area_menu_close"
        android:layout_width="45dp"
        android:layout_height="45dp"

        android:contentDescription="@string/app_name"

        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:scaleType="fitCenter"

        android:src="@drawable/blue_line_arrow_l"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toRightOf="@id/are_menu_header"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/are_menu_header"
        android:layout_width="wrap_content"
        android:layout_height="0dp"

        android:layout_marginStart="15dp"
        android:layout_marginLeft="15dp"

        android:layout_marginTop="8dp"
        android:orientation="vertical"

        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/area_menu_close"
        app:layout_constraintStart_toStartOf="parent"

        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed">

        <TextView
            android:id="@+id/area_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/area_top_label"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/area_guid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/area_top_guide"
            android:textSize="8sp"

            />

    </LinearLayout>


    <ListView
        android:id="@+id/area_list"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"

        android:layout_marginTop="45dp"
        android:divider="@null"
        android:dividerHeight="1px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

    </ListView>

    <View
        android:id="@+id/vertical_line"

        style="@style/vertical_line"
        android:layout_width="0dp"
        android:dividerHeight="1px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/area_list" />

</androidx.constraintlayout.widget.ConstraintLayout>