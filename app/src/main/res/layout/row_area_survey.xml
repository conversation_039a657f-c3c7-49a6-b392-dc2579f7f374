<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="45dp"
    android:minHeight="45dp"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/area_name"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingLeft="15dp"
        android:textColor="@color/black"
        android:textSize="14sp"
        app:fontFamily="sans-serif-light" />

    <ImageView
        android:id="@+id/check_image"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_gravity="center"
        android:layout_marginEnd="10dp"
        android:contentDescription="@string/app_name"
        android:paddingTop="12.5dp"
        android:paddingBottom="12.5dp"
        android:scaleType="fitCenter"
        android:src="@drawable/circle_blue_check" />

</LinearLayout>