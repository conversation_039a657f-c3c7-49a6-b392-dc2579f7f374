<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:minHeight="40dp"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/check_image"
        android:layout_width="40dp"
        android:layout_height="40dp"

        android:layout_marginStart="10dp"

        android:layout_marginLeft="15dp"

        android:layout_marginRight="15dp"
        android:paddingTop="7dp"
        android:paddingBottom="7dp"
        android:scaleType="fitCenter"
        app:srcCompat="@drawable/circle_non_check" />

    <TextView
        android:id="@+id/area_name"
        android:layout_width="match_parent"
        android:layout_height="match_parent"

        android:gravity="center_vertical"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"

        android:textColor="@color/black"
        android:textSize="18sp" />

</LinearLayout>