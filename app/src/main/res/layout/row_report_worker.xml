<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="fill_parent"
    android:layout_height="50dp"
    android:minHeight="50dp"
    android:background="@color/white"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/report_worker_number"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingStart="15dp"
        android:textColor="@color/light_blue"
        android:textSize="18sp"/>

    <EditText
        android:id="@+id/report_worker"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="4"
        android:background="@null"
        android:focusableInTouchMode="true"
        android:gravity="end|center_vertical"
        android:hint="@string/report_worker_input_hint"
        android:inputType="text"
        android:paddingEnd="15dp"
        android:textColor="@color/black"
        android:textSize="18sp"/>

    <ImageView
        android:id="@+id/report_worker_input_icon"
        android:layout_width="15dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="15dp"
        android:src="@drawable/com_pencil"
        android:scaleType="fitCenter" />

</LinearLayout>