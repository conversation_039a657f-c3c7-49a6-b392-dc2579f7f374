<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_layout"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal" >

        <androidx.appcompat.widget.Toolbar
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/photo_gallery_title"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"/>

        </androidx.appcompat.widget.Toolbar>

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:paddingBottom="10dp"
            android:paddingTop="10dp"
            android:src="@drawable/com_headerarrow"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"/>

        <TextView
            android:id="@+id/complete"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="15dp"
            android:gravity="center_vertical"
            android:text="@string/decide"
            android:textColor="@color/white"
            android:textSize="16sp"/>

    </RelativeLayout>

    <GridView
        android:id="@+id/grid_gallery"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:columnWidth="90dp"
        android:horizontalSpacing="-9dp"
        android:numColumns="auto_fit"
        android:verticalSpacing="-9dp"
        tools:listitem="@layout/photo_gallery_item" />

</LinearLayout>
