<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="45dp">


    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/com_header"
        android:theme="@style/AppTheme.AppBarOverlay"
        app:contentInsetStart="0dp">

        <ImageView
            android:id="@+id/map_icon"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_gravity="start|left"
            android:contentDescription="@string/app_name"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:src="@drawable/map_icon"
            tools:ignore="RtlHardcoded" />

        <TextView
            android:id="@+id/select_city_name"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_margin="0dp"
            android:layout_marginStart="45dp"
            android:foregroundGravity="left"
            android:gravity="left|center_vertical"
            android:maxLines="2"
            android:text="未選択"
            android:textColor="@color/white"
            android:textSize="11sp"
            tools:ignore="HardcodedText,RtlHardcoded,SmallSp" />

        <ImageView
            android:id="@+id/switch_list"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_gravity="end|right"
            android:contentDescription="@string/app_name"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:src="@drawable/availability_status"
            tools:ignore="RtlHardcoded" />

        <ImageView
            android:id="@id/readjustment_icon"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_gravity="end|right"
            android:layout_marginEnd="20dp"
            android:layout_marginRight="20dp"
            android:contentDescription="@string/app_name"
            android:paddingTop="12.5dp"
            android:paddingBottom="12.5dp"
            android:src="@drawable/stop_status"
            android:visibility="gone"
            tools:ignore="RtlHardcoded" />

        <TextView
            android:id="@+id/survey_header_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/free_schedule_title"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

    </androidx.appcompat.widget.Toolbar>

    <TextView
        android:id="@+id/readjustment_count"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_gravity="right"
        android:layout_marginTop="3dp"
        android:layout_marginEnd="65dp"
        android:layout_marginRight="65dp"
        android:background="@drawable/unread_badge"
        android:gravity="center_horizontal|center_vertical"
        android:textColor="@color/white"
        android:visibility="gone"
        tools:ignore="RtlHardcoded" />

</FrameLayout>