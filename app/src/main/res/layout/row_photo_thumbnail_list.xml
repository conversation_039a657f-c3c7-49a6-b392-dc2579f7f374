<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="130dp"
    android:minHeight="130dp"
    android:background="@color/list"
    android:descendantFocusability="blocksDescendants"
    android:orientation="horizontal" >

    <FrameLayout
        android:layout_width="160dp"
        android:layout_height="120dp"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:layout_marginStart="5dp"
        android:background="@drawable/image_frame">

        <ImageView
            android:id="@+id/photo_thumbnail_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitCenter" />

    </FrameLayout>

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:minHeight="24dp"
                android:paddingStart="15dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_marginTop="1dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/date"
                    android:scaleType="fitCenter"/>

                <TextView
                    android:id="@+id/photo_thumbnail_date"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingStart="5dp"
                    android:gravity="center_vertical"
                    android:textSize="18sp"
                    android:textColor="@color/dark_gray"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:minHeight="24dp"
                android:paddingStart="15dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_marginTop="1dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/time"
                    android:scaleType="fitCenter"/>

                <TextView
                    android:id="@+id/photo_thumbnail_time"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingStart="5dp"
                    android:gravity="center_vertical"
                    android:textSize="18sp"
                    android:textColor="@color/dark_gray"/>

            </LinearLayout>

            <TextView
                android:id="@+id/photo_thumbnail_worker_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="24dp"
                android:paddingStart="15dp"
                android:paddingEnd="15dp"
                android:textSize="16sp"
                android:textColor="@color/black"/>

        </LinearLayout>

    </RelativeLayout>

    <ImageView
        android:id="@+id/right_arrow"
        android:layout_width="wrap_content"
        android:layout_height="12dp"
        android:layout_marginEnd="10dp"
        android:layout_gravity="center_vertical"
        android:src="@drawable/com_arrow01"
        android:scaleType="fitCenter"/>

</LinearLayout>