<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/setting_company_name"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:minHeight="60dp"
    android:background="@color/list"
    android:descendantFocusability="blocksDescendants" >

    <RelativeLayout
        android:id="@+id/company_info_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/company_number_output"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:gravity="bottom"
            android:paddingStart="15dp"
            android:paddingEnd="200dp"
            android:textColor="@color/black"
            android:textSize="18sp" />

        <TextView
            android:id="@+id/company_name_output"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="30dp"
            android:layout_below="@+id/company_number_output"
            android:gravity="top"
            android:paddingStart="15dp"
            android:paddingEnd="50dp"
            android:textColor="@color/black"
            android:textSize="18sp" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/company_check"
        android:layout_width="100dp"
        android:layout_height="60dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:gravity="end|center_vertical">

        <ImageView
            android:id="@+id/company_check_image"
            android:layout_width="16dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="15dp"
            android:src="@drawable/check"
            android:scaleType="fitCenter"/>

    </LinearLayout>

</RelativeLayout>