<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.fileviewer.FileViewerActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/transparent_black"
        android:fitsSystemWindows="true">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:contentInsetStart="0dp"
            app:layout_scrollFlags="scroll|enterAlways"
            app:layout_constraintTop_toTopOf="parent">

            <FrameLayout
                android:id="@+id/close_button"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_alignParentStart="true"
                android:layout_alignParentLeft="true"
                android:layout_alignParentTop="true"
                android:layout_marginLeft="0dp"
                android:padding="15dp">

                <ImageView
                    android:id="@+id/close_icon"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    app:srcCompat="@drawable/close_icon" />

            </FrameLayout>

            <FrameLayout
                android:id="@+id/download"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="end"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"
                android:layout_alignParentTop="true"
                android:padding="15dp">

                <ImageView
                    android:id="@+id/download_icon"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    app:srcCompat="@drawable/viewer_download" />

            </FrameLayout>

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/fileview_workschedule"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <WebView
        android:id="@+id/fileview_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/LayoutHeader" />
</RelativeLayout>