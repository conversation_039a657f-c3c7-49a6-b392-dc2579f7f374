<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main_layout"
    android:layout_width="wrap_content"
    android:layout_height="fill_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:scrollbarStyle="insideOverlay"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:orientation="horizontal" >

        <androidx.appcompat.widget.Toolbar
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:maxLines="1"
                android:maxEms="10"
                android:ellipsize="end"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"/>

        </androidx.appcompat.widget.Toolbar>

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:paddingBottom="10dp"
            android:paddingTop="10dp"
            android:src="@drawable/com_headerarrow"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"/>

        <TextView
            android:id="@+id/next"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="15dp"
            android:gravity="center_vertical"
            android:text="@string/next"
            android:textColor="@color/white"
            android:textSize="16sp"/>

    </RelativeLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/gray"
        android:layout_below="@+id/LayoutHeader" />

    <RelativeLayout
        android:id="@+id/LayoutMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/divider"
        android:layout_above="@+id/divider3">

        <LinearLayout
            android:id="@+id/LayoutSelectTitle"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:minHeight="46dp"
            android:background="@color/title_gray"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/schedule1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:focusableInTouchMode="true"
                android:gravity="center_vertical"
                android:paddingLeft="15dp"
                android:text="@string/schedule_title"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:focusableInTouchMode="true"
                android:gravity="end|center_vertical"
                android:paddingEnd="15dp"
                android:text="@string/report_progress1"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

        </LinearLayout>

        <View
            android:id="@+id/divider2"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/gray"
            android:layout_below="@+id/LayoutSelectTitle" />

        <ScrollView
            android:id="@+id/LayoutListView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/divider2">

            <LinearLayout
                android:id="@+id/LayoutMain1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:minHeight="50dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:gravity="center_vertical"
                        android:paddingStart="15dp"
                        android:text="@string/report_task_name"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp"/>

                    <TextView
                        android:id="@+id/report_task_name"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="7"
                        android:gravity="end|center_vertical"
                        android:paddingEnd="15dp"
                        android:textColor="@color/black"
                        android:textSize="18sp"/>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <TextView
                    android:id="@+id/take_photo"
                    android:layout_width="fill_parent"
                    android:layout_height="42dp"
                    android:background="@color/contents_gray"
                    android:focusableInTouchMode="true"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:text="@string/report_work_date_info"
                    android:textColor="@color/dark_gray"
                    android:textSize="16sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <LinearLayout
                    android:id="@+id/report_date_area"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:minHeight="50dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/report_date"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/report_date"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <TextView
                        android:id="@+id/report_date_select"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="7"
                        android:gravity="end|center_vertical"
                        android:paddingRight="15dp"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:id="@+id/report_date_select_icon"
                        android:layout_width="15dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="15dp"
                        android:src="@drawable/com_arrow02"
                        android:scaleType="fitCenter" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <LinearLayout
                    android:id="@+id/report_weather_area"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/report_weather"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/report_weather"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <TextView
                        android:id="@+id/report_weather_select"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="7"
                        android:gravity="end|center_vertical"
                        android:paddingRight="15dp"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:id="@+id/report_weather_select_icon"
                        android:layout_width="15dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="15dp"
                        android:src="@drawable/com_arrow02"
                        android:scaleType="fitCenter" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/report_temperature"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/report_temperature"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <EditText
                        android:id="@+id/report_temperature_input"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="7"
                        android:background="@null"
                        android:gravity="end|center_vertical"
                        android:inputType="number|numberSigned"
                        android:paddingRight="15dp"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:id="@+id/report_temperature_input_icon"
                        android:layout_width="15dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="15dp"
                        android:src="@drawable/com_pencil"
                        android:scaleType="fitCenter" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <LinearLayout
                    android:id="@+id/report_worker_count_area"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/report_worker_count"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/report_worker_count"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <TextView
                        android:id="@+id/report_worker_count_select"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="7"
                        android:gravity="end|center_vertical"
                        android:paddingRight="15dp"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:id="@+id/report_worker_count_select_icon"
                        android:layout_width="15dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="15dp"
                        android:src="@drawable/com_arrow02"
                        android:scaleType="fitCenter" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/report_start_time"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/report_start_time"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <TextView
                        android:id="@+id/report_start_time_select"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="7"
                        android:gravity="end|center_vertical"
                        android:paddingRight="15dp"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:id="@+id/report_start_time_select_icon"
                        android:layout_width="15dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="15dp"
                        android:src="@drawable/com_arrow02"
                        android:scaleType="fitCenter" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/report_end_time"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/report_end_time"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <TextView
                        android:id="@+id/report_end_time_select"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="7"
                        android:gravity="end|center_vertical"
                        android:paddingRight="15dp"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:id="@+id/report_end_time_select_icon"
                        android:layout_width="15dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="15dp"
                        android:src="@drawable/com_arrow02"
                        android:scaleType="fitCenter" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="@color/contents_gray"/>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

                <LinearLayout
                    android:id="@+id/report_work_stop_area"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@color/list"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/report_work_stop"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/report_work_stop"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <CheckBox
                        android:id="@+id/report_work_stop_checkbox"
                        style="@style/CustomCheckbox"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:paddingEnd="15dp"
                        android:scaleX="1.5"
                        android:scaleY="1.5"/>

                </LinearLayout>

                <View
                    android:layout_width="fill_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray" />

            </LinearLayout>

        </ScrollView>

    </RelativeLayout>

    <View
        android:id="@+id/divider3"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:background="@color/gray"
        android:layout_above="@+id/report_next"/>

    <FrameLayout
        android:id="@+id/report_next"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:padding="10dp">

        <ImageView
            android:id="@+id/report_next_button"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:src="@drawable/com_next"
            android:scaleType="fitCenter"/>

    </FrameLayout>

</RelativeLayout>
