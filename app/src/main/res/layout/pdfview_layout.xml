<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/transparent_black"
        android:fitsSystemWindows="true"
        >

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/pdfViewTitle"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:contentInsetStart="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_scrollFlags="scroll|enterAlways">

            <FrameLayout
                android:id="@+id/pdfViewClose"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_alignParentStart="true"
                android:layout_alignParentLeft="true"
                android:layout_alignParentTop="true"
                android:layout_marginLeft="0dp"
                android:padding="15dp"
                android:layout_marginStart="0dp">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    app:srcCompat="@drawable/close_icon" />

            </FrameLayout>

            <FrameLayout
                android:id="@+id/pdfViewDownload"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_alignParentTop="true"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"
                android:layout_gravity="end"
                android:padding="15dp">

                <ImageView
                    android:id="@+id/download_icon"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    app:srcCompat="@drawable/viewer_download" />

            </FrameLayout>

            <TextView
                android:id="@+id/pdfViewTitleText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/fileview_workschedule"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />
        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <com.github.barteksc.pdfviewer.PDFView
        android:id="@+id/pdfViewFile"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#F3F1F7"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <TextView
            android:id="@+id/page_view"
            android:layout_width="60dp"
            android:layout_height="30dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:alpha="0.7"
            android:background="@drawable/radius_root_button"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:visibility="invisible"
            app:layout_behavior="com.example.app.ScrollAwareFABBehavior"
            android:layout_marginLeft="20dp" />

    </com.github.barteksc.pdfviewer.PDFView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
