<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/action_text"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_margin="5dp"
        android:background="@drawable/radius_frame_light_gray"
        android:gravity="center"
        android:padding="5dp"
        android:textSize="18sp"
        app:fontFamily="sans-serif-light" />

    <TextView
        android:id="@+id/popup_cancel"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginLeft="7dp"
        android:layout_marginRight="7dp"
        android:layout_marginBottom="7dp"
        android:background="@drawable/redius_frame_white"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="@color/button_blue"
        android:textSize="18sp"
        app:fontFamily="sans-serif" />

</LinearLayout>