<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:openDrawer="start">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <LinearLayout
            android:id="@+id/contents"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <fois.dailyreportsystem.util.calendarview.CalendarView
                android:id="@+id/main_calendar"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight=".4"
                app:eventsEnabled="true"
                app:headerVisibility="gone"
                app:todayLabelColor="@color/white"
                app:type="one_day_picker"
                app:selectionColor="@color/calendar_select_day"
                app:selectionLabelColor="@color/white"

                />

            <LinearLayout
                style="@style/vertical_line"/>

            <ListView
                android:id="@+id/event_list"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight=".3"
                android:dividerHeight="1px" />

        </LinearLayout>
    </RelativeLayout>
</LinearLayout>
