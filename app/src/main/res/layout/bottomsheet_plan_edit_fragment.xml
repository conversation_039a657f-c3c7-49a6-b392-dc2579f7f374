<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/gray"
    android:id="@+id/delete_confirm_area"
    android:layout_height="wrap_content"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:paddingBottom="10dp"
    android:paddingLeft="10dp"
    android:paddingRight="10dp"
    android:paddingTop="10dp"
    app:behavior_peekHeight="56dp"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

<TextView
    android:background="@drawable/redius10"
    android:gravity="center"
    android:id="@+id/delete_confirm_plan_button"
    android:layout_height="45dp"
    android:layout_weight=".5"
    android:layout_width="match_parent"
    android:paddingRight="10dp"
    android:text="@string/plan_edit_delete"
    android:textColor="@color/red"
    android:textSize="18sp" />

<View
    android:background="@color/gray"
    android:layout_height="10dp"
    android:layout_width="match_parent" />


<TextView
    android:background="@drawable/redius10"
    android:gravity="center"
    android:id="@+id/cancel_plan_button"
    android:layout_height="45dp"
    android:layout_weight=".5"
    android:layout_width="match_parent"
    android:paddingRight="10dp"
    android:text="@string/cancel"
    android:textColor="@color/red"
    android:textSize="18sp"
    android:topRightRadius="10dp"
    android:bottomRightRadius="10dp"
    android:bottomLeftRadius="10dp"
    android:topLeftRadius="10dp"/>

</LinearLayout>
