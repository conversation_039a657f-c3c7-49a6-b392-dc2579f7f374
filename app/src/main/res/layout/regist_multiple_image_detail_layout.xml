<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context="fois.dailyreportsystem.activity.survey.RegistMultipleImageDetailActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="4:3"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0">

            <fois.dailyreportsystem.util.ui.AutoFitTextureView
                android:id="@+id/textureView"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

<!--    <FrameLayout-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_alignParentStart="true"-->
<!--        android:layout_alignParentLeft="true"-->
<!--        android:layout_alignParentTop="true"-->
<!--        android:layout_marginTop="0dp">-->

<!--        <ImageView-->
<!--            android:id="@+id/back_arrow"-->
<!--            android:layout_width="61dp"-->
<!--            android:layout_height="match_parent"-->
<!--            android:paddingTop="20dp"-->
<!--            android:paddingBottom="20dp"-->
<!--            android:rotation="180"-->
<!--            android:src="@drawable/white_arrow_l" />-->
<!--    </FrameLayout>-->

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginLeft="380dp"
        android:layout_marginTop="0dp">
    </FrameLayout>

<!--    <FrameLayout-->
<!--        android:id="@+id/thumbnail"-->
<!--        android:layout_width="104dp"-->
<!--        android:layout_height="79dp"-->
<!--        android:layout_alignParentBottom="true"-->
<!--        android:layout_marginStart="15dp"-->
<!--        android:layout_marginLeft="15dp"-->
<!--        android:layout_marginBottom="15dp"-->
<!--        android:visibility="invisible">-->
<!--        -->

<!--        <ProgressBar-->
<!--            android:id="@+id/image_progressBar"-->
<!--            style="@android:style/Widget.ProgressBar"-->
<!--            android:layout_width="20dp"-->
<!--            android:layout_height="20dp"-->
<!--            android:layout_gravity="center"-->
<!--            android:visibility="gone" />-->

<!--    </FrameLayout>-->

    <FrameLayout
        android:id="@+id/table"
        android:layout_width="200dp"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:background="@null">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:baselineAligned="false"
            android:weightSum="1">

            <ListView
                android:id="@+id/diagnosis_part"
                android:layout_width="62dp"
                android:layout_height="match_parent"
                android:choiceMode="singleChoice"
                android:divider="@android:color/black"
                android:dividerHeight="1px"
                android:listSelector="@color/orange" />

            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <ListView
                    android:id="@+id/diagnosis_comment"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:alpha="1"
                    android:divider="@android:color/black"
                    android:dividerHeight="1px"
                    android:drawSelectorOnTop="true"
                    android:listSelector="@drawable/ripple_transparent" />

            </FrameLayout>

        </LinearLayout>
    </FrameLayout>


    <FrameLayout
        android:id="@+id/list_image_frame"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
        android:layout_marginStart="0dp"
        android:layout_marginLeft="0dp"
        android:layout_marginEnd="22dp"
        android:layout_marginRight="22dp"
        android:layout_toStartOf="@+id/table"
        android:layout_toLeftOf="@+id/table"
        android:background="@null">

<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="center_horizontal|center_vertical"-->
<!--            android:background="@drawable/radius_frame_black"-->
<!--            android:gravity="center|center_horizontal|center_vertical"-->
<!--            android:padding="10dp"-->
<!--            android:text="@string/rotate_message"-->
<!--            android:textColor="@color/white" />-->

        <ImageView
            android:id="@+id/list_image_dipslay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@drawable/com_next"/>

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="61dp"
            android:layout_height="wrap_content"
            android:paddingTop="20dp"
            android:paddingBottom="20dp"
            android:rotation="180"
            android:layout_gravity="top|left"
            android:src="@drawable/white_arrow_l" />

        <ProgressBar
            android:id="@+id/image_progressBar"
            style="@android:style/Widget.ProgressBar"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center"
            android:visibility="gone" />

        <TextView
            android:id="@+id/count_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:gravity="center|center_horizontal|center_vertical"
            android:layout_marginBottom="20dip"
            android:textSize="20sp"
            android:padding="10dp"
            android:text="1/10"
            android:textColor="@color/white"/>

        <TextView
            android:id="@+id/image_datetime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left|bottom"
            android:gravity="center|center_horizontal|center_vertical"
            android:textSize="20sp"
            android:paddingLeft="10dp"
            android:text="2022/06/28 12:00"
            android:textColor="@color/white"/>
    </FrameLayout>

</RelativeLayout>
