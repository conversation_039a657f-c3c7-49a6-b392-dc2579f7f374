<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/report_content_area"
    android:layout_width="fill_parent"
    android:layout_height="50dp"
    android:minHeight="50dp"
    android:background="@color/list"
    android:descendantFocusability="blocksDescendants" >

    <TextView
        android:id="@+id/report_content_name"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_toStartOf="@+id/report_content_check"
        android:layout_centerVertical="true"
        android:gravity="center_vertical"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        android:textSize="18sp"
        android:textColor="@color/light_blue" />

    <CheckBox
        android:id="@+id/report_content_check"
        style="@style/CustomCheckbox"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerVertical="true"
        android:layout_alignParentEnd="true"
        android:paddingEnd="15dp"
        android:scaleX="1.5"
        android:scaleY="1.5" />

</RelativeLayout>