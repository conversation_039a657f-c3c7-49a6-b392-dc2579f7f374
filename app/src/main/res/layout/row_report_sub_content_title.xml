<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="fill_parent"
    android:layout_height="42dp"
    android:minHeight="42dp"
    android:background="@color/sub_contents_title"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/report_sub_content_title"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:paddingStart="15dp"
        android:textSize="16sp"
        android:textColor="@color/black" />

</LinearLayout>