<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/plan_detail_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="@drawable/com_header"
        android:theme="@style/AppTheme.AppBarOverlay"

        app:contentInsetStart="0dp"
        app:layout_constraintLeft_toLeftOf="parent"

        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:src="@drawable/white_arrow_l"
            tools:layout_editor_absoluteX="16dp"
            tools:layout_editor_absoluteY="12dp"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/textView"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="10dp"
            android:layout_marginRight="15dp"
            android:gravity="center_vertical"
            android:textColor="@color/orange_link"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/survey_property_toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/plan_detail"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            tools:layout_editor_absoluteX="154dp"
            tools:layout_editor_absoluteY="9dp" />

        <TextView
            android:id="@+id/edit_text"
            android:layout_width="60dp"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_gravity="right"
            android:gravity="right|center_vertical"
            android:text="@string/edit"
            android:textColor="@color/orange_link"
            android:textSize="16sp"
            tools:layout_editor_absoluteX="365dp"
            android:paddingEnd="10dp"
            android:paddingRight="10dp"
            tools:ignore="RtlHardcoded,RtlSymmetry" />

    </androidx.appcompat.widget.Toolbar>

    <LinearLayout
        android:id="@+id/plan_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:weightSum="1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <LinearLayout
            android:id="@+id/plan_work"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="15dp"
            android:layout_marginRight="15dp"
            android:layout_marginBottom="3dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/plan_title"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_weight=".7"
                android:gravity="center_vertical"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold"
                app:fontFamily="sans-serif-medium" />

            <TextView
                android:id="@+id/plan_staff"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_weight=".3"
                android:gravity="right|center_vertical"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                app:fontFamily="sans-serif-light"
                tools:ignore="RtlHardcoded" />

        </LinearLayout>

        <TextView
            android:id="@+id/plan_location"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:textColor="@color/dark_gray"
            android:textSize="14sp"
            app:fontFamily="sans-serif-light" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/plan_date_info"

        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="15dp"
        android:layout_marginRight="15dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/plan_info">

        <TextView
            android:id="@+id/plan_date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="1dp"
            android:paddingBottom="1dp"
            android:textColor="@android:color/darker_gray"
            android:textSize="16sp"
            app:fontFamily="sans-serif-light" />

        <TextView
            android:id="@+id/plan_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="1dp"
            android:paddingBottom="1dp"
            android:textColor="@android:color/darker_gray"
            android:textSize="16sp"
            app:fontFamily="sans-serif-light" />

        <TextView
            android:id="@+id/loop_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="1dp"
            android:paddingBottom="2dp"
            android:textColor="@android:color/darker_gray"
            android:textSize="16sp"
            app:fontFamily="sans-serif-light" />

        <TextView
            android:id="@+id/travel_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="1dp"
            android:paddingBottom="2dp"
            android:textColor="@android:color/darker_gray"
            android:textSize="16sp"
            app:fontFamily="sans-serif-light" />

    </LinearLayout>


    <FrameLayout
        android:id="@+id/weekview_layout"
        android:layout_width="0dp"
        android:layout_height="200dp"
        android:layout_marginStart="15dp"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="15dp"
        android:layout_marginRight="15dp"
        android:background="@drawable/radius_frame_white"
        android:paddingTop="2dp"
        android:paddingBottom="2dp"
        android:paddingRight="1px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/plan_date_info">


        <fois.dailyreportsystem.util.weekview.FreeWeekView
            android:id="@+id/weekView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:columnGap="0dp"
            app:dayBackgroundColor="@android:color/transparent"
            app:dayNameToLength="length_short"
            app:eventTextColor="@android:color/white"
            app:futureBackgroundColor="@color/contents_background"
            app:futureWeekendBackgroundColor="#E6E5F8"
            app:headerColumnBackground="@android:color/transparent"
            app:headerColumnPadding="20dp"
            app:headerColumnTextColor="#6D6D6D"
            app:headerRowBackgroundColor="@android:color/transparent"
            app:headerRowPadding="15dp"
            app:headerTextSize="18sp"
            app:hourColumnPadding="8dp"
            app:hourHeight="45dp"
            app:noOfVisibleDays="1"
            app:headerShow="true"
            app:nowLineColor="@color/todayColor"
            app:pastBackgroundColor="@color/contents_background"
            app:pastWeekendBackgroundColor="#E6E5F8"
            app:showDistinctPastFutureColor="true"
            app:showDistinctWeekendColor="true"
            app:showNowLine="false"
            app:textSize="12sp"
            app:todayBackgroundColor="@color/contents_background"
            app:todayHeaderTextColor="@color/dark_gray" />
    </FrameLayout>

    <TextView
        android:id="@+id/plan_memo"
        android:layout_width="0dp"
        android:layout_height="100dp"
        android:layout_marginStart="15dp"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="15dp"
        android:layout_marginRight="15dp"
        android:gravity="start|top"
        android:textColor="@android:color/darker_gray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/weekview_layout" />

</androidx.constraintlayout.widget.ConstraintLayout>