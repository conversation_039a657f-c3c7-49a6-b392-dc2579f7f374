<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="46dp"
    android:minHeight="46dp"
    android:background="@color/list"
    android:descendantFocusability="blocksDescendants"
    android:orientation="horizontal" >

    <TextView
        android:id="@+id/process_name"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_alignParentStart="true"
        android:gravity="center_vertical"
        android:paddingStart="15dp"
        android:paddingEnd="100dp"
        android:textColor="@color/light_blue"
        android:textSize="18sp"/>

    <TextView
        android:id="@+id/process_photo_count"
        android:layout_width="wrap_content"
        android:layout_height="46dp"
        android:layout_toStartOf="@+id/process_photo_count_text"
        android:layout_marginEnd="5dp"
        android:gravity="center_vertical"
        android:textSize="16sp"
        android:textColor="@color/black"/>

    <TextView
        android:id="@+id/process_photo_count_text"
        android:layout_width="wrap_content"
        android:layout_height="46dp"
        android:layout_toStartOf="@+id/right_arrow"
        android:layout_marginEnd="20dp"
        android:gravity="center_vertical"
        android:text="@string/photo_count"
        android:textSize="16sp"
        android:textColor="@color/black"/>

    <ImageView
        android:id="@+id/right_arrow"
        android:layout_width="wrap_content"
        android:layout_height="12dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="10dp"
        android:src="@drawable/com_arrow01"
        android:scaleType="fitCenter"/>

</RelativeLayout>