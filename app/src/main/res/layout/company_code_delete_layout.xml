<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    tools:context="fois.dailyreportsystem.activity.setting.CompanyCodeDeleteActivity">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="fill_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:orientation="horizontal" >

        <androidx.appcompat.widget.Toolbar
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/company_delete_title"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"/>

        </androidx.appcompat.widget.Toolbar>

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:paddingBottom="10dp"
            android:paddingTop="10dp"
            android:src="@drawable/com_headerarrow"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"/>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/LayoutLine"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/LayoutHeader"
        android:background="@color/gray"
        android:orientation="vertical" />

    <TextView
        android:id="@+id/mail_info"
        android:layout_width="fill_parent"
        android:layout_height="38dp"
        android:layout_below="@+id/LayoutLine"
        android:background="@color/contents_gray"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:paddingLeft="15dp"
        android:text="@string/company_info"
        android:textColor="@color/dark_gray"
        android:textSize="16sp" />

    <LinearLayout
        android:id="@+id/LayoutLine2"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/mail_info"
        android:background="@color/gray"
        android:orientation="vertical" />

    <LinearLayout
        android:id="@+id/phone_mail"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_below="@+id/LayoutLine2"
        android:background="@color/white">

        <TextView
            android:id="@+id/company_number_text"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:text="@string/company_number"
            android:textColor="@color/light_blue"
            android:textSize="18sp" />

        <TextView
            android:id="@+id/company_number_output"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="end|center_vertical"
            android:paddingRight="15dp"
            android:textColor="@color/black"
            android:textSize="18sp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/LayoutLine3"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/phone_mail"
        android:background="@color/gray"
        android:orientation="vertical" />

    <LinearLayout
        android:id="@+id/company_name"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_below="@+id/LayoutLine3"
        android:background="@color/white">

        <TextView
            android:id="@+id/company_name_text"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:text="@string/company_name"
            android:textColor="@color/light_blue"
            android:textSize="18sp" />

        <TextView
            android:id="@+id/company_name_output"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:gravity="end|center_vertical"
            android:paddingRight="15dp"
            android:textColor="@color/black"
            android:textSize="18sp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/LayoutLine4"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/company_name"
        android:background="@color/gray"
        android:orientation="vertical" />

    <LinearLayout
        android:id="@+id/LayoutLine5"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/LayoutLine4"
        android:layout_marginTop="10dp"
        android:background="@color/gray"
        android:orientation="vertical" />

    <LinearLayout
        android:id="@+id/company_number_delete"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:minHeight="46dp"
        android:layout_below="@+id/LayoutLine5"
        android:background="@color/list"
        android:gravity="center_horizontal"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="match_parent"
            android:scaleType="fitCenter"
            android:src="@drawable/delete"/>

        <TextView
            android:id="@+id/company_number_delete_text"
            android:layout_width="wrap_content"
            android:layout_height="46dp"
            android:layout_marginStart="10dp"
            android:gravity="center"
            android:text="@string/company_number_delete"
            android:textColor="@color/delete"
            android:textSize="18sp"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/LayoutLine6"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/company_number_delete"
        android:background="@color/gray"
        android:orientation="vertical" />

</RelativeLayout>
