<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@null"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:orientation="vertical">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay"
            app:contentInsetStart="0dp">

            <ImageView
                android:id="@+id/back_arrow"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_gravity="left|start"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:src="@drawable/white_arrow_l" />

            <TextView
                android:id="@+id/plan_header_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/readjustment_list_title"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/readjustment_count"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="right|end"
                android:layout_marginRight="15dp"
                android:contentDescription="@string/app_name"
                android:paddingTop="10dp"
                android:paddingBottom="5dp"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </androidx.appcompat.widget.Toolbar>
    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/contents_background">


        <ListView
            android:id="@+id/order_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:divider="@color/gray"
            android:dividerHeight="1px" />

    </LinearLayout>


</LinearLayout>
