<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main_layout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal" >

        <androidx.appcompat.widget.Toolbar
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:maxLines="1"
                android:maxEms="10"
                android:ellipsize="end"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"/>

        </androidx.appcompat.widget.Toolbar>

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:paddingBottom="10dp"
            android:paddingTop="10dp"
            android:src="@drawable/com_headerarrow"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"/>

        <TextView
            android:id="@+id/register"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="15dp"
            android:gravity="center_vertical"
            android:text="@string/report_register"
            android:textColor="@color/white"
            android:textSize="16sp"/>

    </RelativeLayout>

    <View
        android:id="@id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/LayoutHeader"
        android:background="@color/gray"/>

    <LinearLayout
        android:id="@+id/LayoutSelectTitle"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:minHeight="46dp"
        android:layout_below="@+id/divider"
        android:background="@color/title_gray"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/result3"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:focusableInTouchMode="true"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:text="@string/result_title"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:focusableInTouchMode="true"
            android:gravity="end|center_vertical"
            android:paddingEnd="15dp"
            android:text="@string/report_progress3"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

    </LinearLayout>

    <View
        android:id="@+id/divider2"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/LayoutSelectTitle"
        android:background="@color/gray" />

    <!--Main content-->
    <ScrollView
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/divider2"
        android:layout_above="@+id/divider3"
        android:overScrollMode="never">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:id="@+id/lyMainContent">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="42dp"
                android:background="@color/contents_gray"
                android:gravity="center_vertical"
                android:paddingStart="15dp"
                android:text="@string/days_to_end"
                android:textSize="16sp"
                android:textColor="@color/dark_gray" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/gray" />

            <LinearLayout
                android:id="@+id/report_to_complete_area"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:minHeight="50dp"
                android:background="@color/white"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/report_to_complete"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:text="@string/report_to_complete"
                    android:textColor="@color/light_blue"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/report_to_complete_select"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="7"
                    android:gravity="end|center_vertical"
                    android:paddingRight="15dp"
                    android:textColor="@color/black"
                    android:textSize="18sp" />

                <ImageView
                    android:id="@+id/report_to_complete_select_icon"
                    android:layout_width="15dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="15dp"
                    android:src="@drawable/com_arrow02"
                    android:scaleType="fitCenter" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/gray" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="42dp"
                android:background="@color/contents_gray"
                android:gravity="center_vertical"
                android:paddingStart="15dp"
                android:text="@string/report_comment"
                android:textSize="16sp"
                android:textColor="@color/dark_gray" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/gray" />

            <LinearLayout
                android:id="@+id/report_comment_area"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/report_comment_input"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@null"
                    android:inputType="textMultiLine"
                    android:maxLength="1000"
                    android:maxLines="500"
                    android:paddingBottom="5dp"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    android:paddingTop="5dp"
                    android:textSize="18sp"/>

                <ImageView
                    android:id="@+id/report_comment_input_icon"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="bottom"
                    android:layout_marginEnd="15dp"
                    android:layout_marginBottom="10dp"
                    android:src="@drawable/com_pencil"
                    android:scaleType="fitCenter" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/gray" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="42dp"
                android:background="@color/contents_gray"
                android:gravity="center_vertical"
                android:paddingStart="15dp"
                android:text="@string/report_sub_content"
                android:textSize="16sp"
                android:textColor="@color/dark_gray" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/gray" />

            <ListView
                android:id="@+id/report_result3_listview"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:divider="@color/gray"
                android:background="@color/white"
                android:dividerHeight="1px" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/gray" />

        </LinearLayout>

    </ScrollView>

    <View
        android:id="@+id/divider3"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_above="@+id/report_register"
        android:background="@color/gray" />

    <FrameLayout
        android:id="@+id/report_register"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:padding="10dp">

        <ImageView
            android:id="@+id/report_register_button"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:src="@drawable/com_regist"
            android:scaleType="fitCenter"/>

    </FrameLayout>

</RelativeLayout>