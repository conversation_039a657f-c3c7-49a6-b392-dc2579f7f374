<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="50dp"
    android:minHeight="50dp"
    android:background="@color/list"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/message_list_group_name"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingStart="15dp"
        android:paddingEnd="10dp"
        android:textColor="@color/light_blue"
        android:textSize="18sp" />

    <TextView
        android:id="@+id/message_list_unread_badge"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:paddingBottom="1dp"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:background="@drawable/unread_badge"
        android:textColor="@color/white"
        android:layout_marginEnd="10dp"/>

    <ImageView
        android:id="@+id/right_arrow"
        android:layout_width="wrap_content"
        android:layout_height="12dp"
        android:layout_marginEnd="10dp"
        android:layout_gravity="center_vertical"
        android:src="@drawable/com_arrow01"
        android:scaleType="fitCenter"/>

</LinearLayout>