<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="fill_parent"
    android:layout_height="60dp"
    android:background="@null">

    <Button
        android:id="@+id/nDialog"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@null" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:paddingStart="10dp"
        android:paddingTop="5dp"
        android:paddingEnd="10dp"
        android:paddingBottom="5dp"
        android:background="@color/white"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/nDialog_title"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="5"
                android:gravity="center_vertical"
                android:text="@string/app_name"
                android:textColor="@color/black"
                android:textSize="18sp"/>

            <TextView
                android:id="@+id/nDialog_time"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="end"
                android:text=""
                android:textColor="@color/dark_gray" />
        </LinearLayout>

        <TextView
            android:id="@+id/nDialog_message"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:text="@string/dialog_new_message"
            android:textColor="@color/dark_gray"
            android:textSize="16sp"/>

    </LinearLayout>

</RelativeLayout>
