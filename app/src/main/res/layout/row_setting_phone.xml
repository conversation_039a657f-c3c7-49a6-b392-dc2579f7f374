<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/setting_company_name"
    android:layout_width="fill_parent"
    android:layout_height="46dp"
    android:minHeight="46dp"
    android:background="@color/white"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/phone_number_text"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="3"
        android:gravity="center_vertical"
        android:paddingLeft="15dp"
        android:text="@string/phone_number"
        android:textColor="@color/light_blue"
        android:textSize="18sp" />

    <EditText
        android:id="@+id/phone_number_input"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="7"
        android:background="@null"
        android:gravity="end|center_vertical"
        android:inputType="number"
        android:maxLength="12"
        android:paddingRight="15dp"
        android:textColor="@color/black"
        android:textSize="18sp" />

    <ImageView
        android:layout_width="15dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="15dp"
        android:src="@drawable/com_pencil"
        android:scaleType="fitCenter"/>

</LinearLayout>