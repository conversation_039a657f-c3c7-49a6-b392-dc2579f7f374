<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:id="@+id/main_layout"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:orientation="vertical"
              android:scrollbarStyle="insideOverlay"
              android:focusable="true"
              android:focusableInTouchMode="true">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal" >

        <androidx.appcompat.widget.Toolbar
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:maxLines="1"
                android:maxEms="10"
                android:ellipsize="end"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"/>

        </androidx.appcompat.widget.Toolbar>

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:paddingBottom="10dp"
            android:paddingTop="10dp"
            android:src="@drawable/com_headerarrow"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"/>

        <TextView
            android:id="@+id/next"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="15dp"
            android:gravity="center_vertical"
            android:text="@string/next"
            android:textColor="@color/white"
            android:textSize="16sp"/>

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/gray" />

    <LinearLayout
        android:id="@+id/LayoutSelectTitle"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:minHeight="46dp"
        android:background="@color/title_gray"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/result2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:focusableInTouchMode="true"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:text="@string/result_title"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:focusableInTouchMode="true"
            android:gravity="end|center_vertical"
            android:paddingEnd="15dp"
            android:text="@string/report_progress2"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/gray" />

    <ListView
        android:id="@+id/report_result2_listview"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:divider="@color/gray"
        android:dividerHeight="1px"/>

    <View
        android:id="@+id/divider3"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:background="@color/gray" />

    <FrameLayout
        android:id="@+id/report_next"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@color/white"
        android:padding="10dp">

        <ImageView
            android:id="@+id/report_next_button"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:src="@drawable/com_next"
            android:scaleType="fitCenter"/>

    </FrameLayout>

</LinearLayout>
