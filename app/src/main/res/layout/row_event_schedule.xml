<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:descendantFocusability="blocksDescendants"
    android:minHeight="50dp"
    android:orientation="horizontal">

    <LinearLayout
        android:id="@+id/time_contents"
        android:layout_width="60dp"
        android:layout_height="0dp"

        android:orientation="vertical"
        android:paddingRight="4dp"

        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/horizon_line_contents"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/travel_time"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="10dp"
            android:layout_weight="0.9"
            android:gravity="right|center_vertical"
            android:textColor="@android:color/darker_gray"
            android:textSize="11sp"
            app:fontFamily="sans-serif-light"
            tools:text="14:00"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/start_date_time"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="10dp"
            android:layout_weight="1.1"
            android:gravity="right|center_vertical"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:fontFamily="sans-serif-medium"
            tools:text="15:00" />

        <TextView
            android:id="@+id/end_date_time"
            android:layout_width="match_parent"
            android:layout_height="16dp"
            android:layout_marginStart="10dp"
            android:gravity="right|center_vertical"
            android:textColor="@android:color/darker_gray"
            android:textSize="12sp"
            app:fontFamily="sans-serif-light"
            tools:text="17:00" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/horizon_line_contents"
        android:layout_width="3dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"

        app:layout_constraintEnd_toStartOf="@+id/contents"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/time_contents"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:id="@+id/vertical_line"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:background="@color/orange">

        </View>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contents"
        android:layout_width="0dp"
        android:layout_height="0dp"

        android:orientation="vertical"

        android:paddingLeft="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/horizon_line_contents"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/travel_title"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center_vertical"
            android:layout_weight="0.9"
            android:text="移動時間：1時間"
            android:textColor="@android:color/darker_gray"
            android:textSize="11sp"
            app:fontFamily="sans-serif-light"
            tools:visibility="visible" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1.1"
            android:orientation="horizontal"
            android:weightSum="2">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1.5"
                android:gravity="left|center_vertical"
                android:maxLines="1"
                android:text="TextView"
                android:textColor="@color/black"
                android:textFontWeight="300"
                android:textSize="14sp"
                app:fontFamily="sans-serif-medium" />

            <TextView
                android:id="@+id/user_name"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="0.5"
                android:gravity="right|center_vertical"
                android:maxLines="1"
                android:paddingRight="3dp"
                android:text="TextView"
                android:textColor="@android:color/black"
                android:textFontWeight="300"
                android:textSize="14sp"
                app:fontFamily="sans-serif-medium" />
        </LinearLayout>

        <TextView
            android:id="@+id/location"
            android:layout_width="match_parent"
            android:layout_height="16dp"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:text="TextView"
            android:textColor="@android:color/darker_gray"
            android:textSize="10sp"
            app:fontFamily="sans-serif-light" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>