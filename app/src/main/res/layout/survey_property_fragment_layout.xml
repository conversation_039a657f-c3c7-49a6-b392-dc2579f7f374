<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/survey_fragment"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:scrollbarStyle="insideOverlay">

    <RelativeLayout
        android:id="@+id/LayoutMain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        >

        <RelativeLayout
            android:id="@+id/search_layout"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:background="@color/light_gray">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="6dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="6dp"
                android:background="@drawable/main_search_rectangle">

                <SearchView
                    android:id="@+id/searchViewTask"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:iconifiedByDefault="false"
                    android:queryHint="@string/survey_property_list_search_hint"
                    android:paddingRight="20dp"
                    android:paddingEnd="20dp"
                    tools:ignore="RtlSymmetry" />

            </FrameLayout>

        </RelativeLayout>

        <TextView
            android:id="@+id/noData"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="@string/survey_property_list_no_data"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:visibility="gone" />

        <!--List view-->
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipeRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/search_layout">

            <ListView
                android:id="@+id/survey_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:divider="@color/gray"
                android:dividerHeight="1px"
                android:listSelector="@drawable/ripple_transparent"
                android:drawSelectorOnTop="true"
                />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </RelativeLayout>

    <View
        android:id="@+id/transparent_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@null"
        android:visibility="gone" />
</RelativeLayout>
