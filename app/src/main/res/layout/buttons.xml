<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent" >

    <ViewAnimator
        android:id="@+id/switcher"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true" >

        <RelativeLayout
            android:id="@+id/topBar0Main"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/mp_toolbar" >

            <TextView
                android:id="@+id/docNameText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:layout_toLeftOf="@+id/imgBtnClose"
                android:ellipsize="middle"
                android:gravity="center_vertical|center_horizontal"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:singleLine="true"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="#FFFFFF"
                android:textSize="15sp" />

            <ImageButton
                android:id="@+id/imgBtnClose"
                android:layout_width="44dp"
                android:layout_height="40dp"
                android:layout_alignParentBottom="true"
                android:layout_alignParentLeft="false"
                android:layout_alignParentRight="true"
                android:layout_alignParentTop="true"
                android:background="@null"
                android:padding="10dp"
                android:scaleType="fitXY"
                android:src="@drawable/close_icon" />
        </RelativeLayout>
    </ViewAnimator>

    <RelativeLayout
        android:id="@+id/lowerButtons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_below="@+id/switcher"
        android:layout_centerHorizontal="true" >

        <SeekBar
            android:id="@+id/pageSlider"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_margin="0dp"
            android:background="@color/mp_toolbar"
            android:paddingBottom="8dp"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:paddingTop="12dp"
            android:progressDrawable="@drawable/seek_progress"
            android:thumb="@drawable/seek_thumb" />

        <TextView
            android:id="@+id/pageNumber"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/pageSlider"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="16dp"
            android:background="@drawable/page_num"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:textColor="#FFFFFF" />
    </RelativeLayout>

    <TextView
        android:id="@+id/info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/page_num"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textColor="#FFFFFF" />

</RelativeLayout>