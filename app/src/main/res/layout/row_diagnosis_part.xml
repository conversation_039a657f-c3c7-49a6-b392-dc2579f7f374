<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:background="@null"
    android:minHeight="40dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/view"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:alpha=".7"
            android:background="@color/light_gray"
            android:orientation="horizontal" />

        <TextView
            android:id="@+id/category_name"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@null"
            android:gravity="center"
            android:maxLines="2"
            android:textColor="@color/black"
            app:fontFamily="sans-serif-medium" />

        <LinearLayout
            android:id="@+id/bg_photo"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:background="@color/bg_category"
            android:gravity="center"
            android:minWidth="30dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/sum_reserve"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:background="@null"
                android:gravity="center"
                android:maxLines="2"
                android:text="2"
                android:textSize="12dp"
                android:textColor="@color/white"
                app:fontFamily="sans-serif-medium" />

            <TextView
                android:id="@+id/slash"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:background="@null"
                android:gravity="center"
                android:maxLines="2"
                android:text="/"
                android:textSize="12dp"
                android:textColor="@color/white"
                app:fontFamily="sans-serif-medium" />

            <TextView
                android:id="@+id/sum_report"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:background="@null"
                android:gravity="center"
                android:maxLines="2"
                android:text="0"
                android:textSize="12dp"
                android:textColor="@color/white"
                app:fontFamily="sans-serif-medium" />

        </LinearLayout>

    </FrameLayout>

</RelativeLayout>