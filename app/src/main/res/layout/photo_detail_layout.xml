<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main_layout"
    android:layout_width="wrap_content"
    android:layout_height="fill_parent"
    android:orientation="vertical"
    android:scrollbarStyle="insideOverlay">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="fill_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:orientation="horizontal" >

        <androidx.appcompat.widget.Toolbar
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:maxLines="1"
                android:maxEms="10"
                android:ellipsize="end"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"/>

        </androidx.appcompat.widget.Toolbar>

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:paddingBottom="10dp"
            android:paddingTop="10dp"
            android:src="@drawable/com_headerarrow"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"/>

        <TextView
            android:id="@+id/photo_delete"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="15dp"
            android:gravity="center_vertical"
            android:text="@string/photo_delete"
            android:textColor="@color/half_transparent"
            android:textSize="16sp" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/LayoutMain"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_below="@+id/LayoutHeader"
        android:orientation="vertical">

        <View
            android:layout_width="fill_parent"
            android:layout_height="1dp"
            android:background="@color/gray" />

        <TextView
            android:id="@+id/photo_detail_region_content_name"
            android:layout_width="fill_parent"
            android:layout_height="46dp"
            android:background="@color/title_gray"
            android:focusableInTouchMode="true"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <View
            android:layout_width="fill_parent"
            android:layout_height="1dp"
            android:background="@color/gray" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:minHeight="50dp"
            android:background="@color/white"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3"
                android:gravity="center_vertical"
                android:paddingStart="15dp"
                android:text="@string/photo_date"
                android:textColor="@color/light_blue"
                android:textSize="18sp"/>

            <TextView
                android:id="@+id/photo_detail_date"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="7"
                android:gravity="end|center_vertical"
                android:paddingEnd="15dp"
                android:textColor="@color/black"
                android:textSize="18sp"/>

        </LinearLayout>

        <View
            android:layout_width="fill_parent"
            android:layout_height="1dp"
            android:background="@color/gray" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:minHeight="50dp"
            android:background="@color/white"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3"
                android:gravity="center_vertical"
                android:paddingStart="15dp"
                android:text="@string/photo_take_user"
                android:textColor="@color/light_blue"
                android:textSize="18sp"/>

            <TextView
                android:id="@+id/photo_detail_take_user"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="7"
                android:gravity="end|center_vertical"
                android:paddingEnd="15dp"
                android:textColor="@color/black"
                android:textSize="18sp"/>

        </LinearLayout>

        <View
            android:layout_width="fill_parent"
            android:layout_height="1dp"
            android:background="@color/gray" />

        <View
            android:layout_width="fill_parent"
            android:layout_height="10dp"
            android:background="@color/contents_gray" />

        <View
            android:layout_width="fill_parent"
            android:layout_height="1dp"
            android:background="@color/gray" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@color/white"
            android:orientation="vertical">

            <androidx.viewpager.widget.ViewPager
                android:id="@+id/view_pager"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </LinearLayout>

        <View
            android:layout_width="fill_parent"
            android:layout_height="1dp"
            android:background="@color/gray"/>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="35dp"
            android:gravity="center"
            android:background="@color/contents_gray">

            <TextView
                android:id="@+id/photo_detail_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/black"
                android:textSize="16sp" />

        </LinearLayout>

        <View
            android:layout_width="fill_parent"
            android:layout_height="1dp"
            android:background="@color/gray"/>

        <LinearLayout
            android:id="@+id/photo_detail_footer"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@drawable/com_footer_btbk"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/photo_detail_footer_gallery"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/photo_detail_footer_gallery_icon"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_gravity="center_vertical"
                    android:scaleType="fitCenter"
                    android:src="@drawable/album"/>

                <TextView
                    android:id="@+id/photo_detail_footer_gallery_text"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="6dp"
                    android:gravity="center"
                    android:text="@string/photo_detail_gallery_text"
                    android:textColor="@color/white"/>

            </LinearLayout>

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/white" />

            <LinearLayout
                android:id="@+id/photo_detail_footer_camera"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/photo_detail_footer_camera_icon"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_gravity="center_vertical"
                    android:scaleType="fitCenter"
                    android:src="@drawable/camera_white"/>

                <TextView
                    android:id="@+id/photo_detail_footer_camera_text"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="6dp"
                    android:gravity="center"
                    android:text="@string/photo_detail_camera_text"
                    android:textColor="@color/white"/>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>
