<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="45dp">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="@drawable/com_header"
        android:theme="@style/AppTheme.AppBarOverlay"
        app:contentInsetStart="0dp">

        <TextView
            android:id="@+id/search_survey"
            android:layout_width="wrap_content"
            android:layout_height="45dp"
            android:layout_alignParentLeft="true"
            android:contentDescription="@string/app_name"
            android:gravity="left|center_vertical"
            android:minWidth="60dp"
            android:paddingLeft="15dp"
            android:text="@string/shooting_all"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/survey_header_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/survey_property_list_title"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

    </androidx.appcompat.widget.Toolbar>


</RelativeLayout>