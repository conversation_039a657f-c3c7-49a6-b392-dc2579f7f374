<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:padding="5dp">

    <FrameLayout
        android:id="@+id/grid_item_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/grid_item_image"
                android:layout_width="90dp"
                android:layout_height="90dp"
                android:contentDescription="@string/app_name"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:scaleType="centerCrop"
                android:src="@drawable/no_media" />

        </FrameLayout>

        <ImageView
            android:id="@+id/grid_item_check"
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:layout_gravity="end"
            android:background="@drawable/checkbox_selected"
            android:contentDescription="@string/app_name"
            android:gravity="center"
            android:scaleType="center"
            android:visibility="gone" />

    </FrameLayout>

</LinearLayout>