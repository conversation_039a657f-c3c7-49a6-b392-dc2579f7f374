<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/order_registration_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/contents_background"
    android:orientation="vertical"
    tools:context="fois.dailyreportsystem.activity.request.MakerListActivity">

    <FrameLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:orientation="vertical">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay"
            app:contentInsetStart="0dp">

            <ImageView
                android:id="@+id/back_arrow"
                android:layout_width="45dp"
                android:layout_height="match_parent"
                android:layout_gravity="left|start"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:src="@drawable/white_arrow_l" />

            <TextView
                android:id="@+id/plan_header_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/research_maker_user_list_title"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />
        </androidx.appcompat.widget.Toolbar>

    </FrameLayout>

    <LinearLayout
        android:id="@+id/contents"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/header"
        android:orientation="vertical">

        <ListView
            android:id="@+id/research_maker_user_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:divider="@color/gray"
            android:dividerHeight="1px"
            android:background="@color/contents_background" />

    </LinearLayout>

</RelativeLayout>
