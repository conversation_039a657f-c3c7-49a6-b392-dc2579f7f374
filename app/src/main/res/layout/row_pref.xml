<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:descendantFocusability="blocksDescendants"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/pref_name"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingStart="20dp"
        android:paddingLeft="15dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:fontFamily="sans-serif-light"
        tools:ignore="RtlSymmetry" />

    <ImageView
        android:layout_width="15dp"
        android:layout_height="match_parent"
        android:layout_marginRight="60dp"
        android:layout_marginEnd="60dp"
        android:paddingTop="16.5dp"
        android:paddingBottom="16.5dp"
        android:scaleType="center"
        android:src="@drawable/com_arrow01"
        tools:ignore="ContentDescription" />

</LinearLayout>