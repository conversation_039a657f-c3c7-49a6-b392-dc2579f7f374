<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/readjustment_registration_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/contents_gray"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_marginEnd="0dp"
            android:layout_marginRight="0dp"
            android:layout_marginBottom="0dp"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay"
            app:contentInsetStart="0dp">

            <TextView
                android:id="@+id/back_button"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="10dp"
                android:contentDescription="@string/app_name"
                android:gravity="center"
                android:paddingLeft="10dp"
                android:text="@string/cancel"
                android:textColor="@color/orange_link"
                android:textSize="17sp" />

            <TextView
                android:id="@+id/readjustment_title"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_marginBottom="0dp"
                android:gravity="center"
                android:text="@string/readjustment_title"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/readjustment_registration"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="end|right"
                android:gravity="center"
                android:paddingEnd="10dp"
                android:paddingRight="10dp"
                android:text="@string/readjustment"
                android:textColor="@color/half_transparent"
                android:textSize="17sp" />

        </androidx.appcompat.widget.Toolbar>

    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/footer"
        android:layout_below="@+id/header">

        <ScrollView
            android:id="@+id/scroll_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:id="@+id/contents"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/order_user_info_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/order_user_info_title"
                        style="@style/section_sub_title"
                        android:layout_width="match_parent"
                        android:layout_height="20sp"
                        android:text="@string/order_info" />

                    <LinearLayout style="@style/vertical_line" />

                    <LinearLayout
                        android:id="@+id/order_user_colmun"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/order_maker"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:layout_weight=".5"
                            android:gravity="center_vertical"
                            android:textColor="@color/black"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif-light" />

                        <TextView
                            android:id="@+id/order_maker_user"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:layout_weight=".3"
                            android:gravity="right|end|center_vertical"
                            android:paddingEnd="5dp"
                            android:paddingRight="5dp"
                            android:textColor="@color/black"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif-light" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:layout_weight=".1"
                            android:gravity="center"
                            android:text="様"
                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif" />
                    </LinearLayout>

                    <LinearLayout style="@style/vertical_line" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/order_info_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/order_info_title"
                        style="@style/section_sub_title"
                        android:layout_width="match_parent"
                        android:layout_height="25sp"
                        android:text="@string/order_detail_section" />

                    <LinearLayout style="@style/vertical_line">

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/taks_name_column"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/task_name"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:layout_weight="1"
                            android:background="#00000000"
                            android:hint="@string/task_name"
                            android:textColor="@color/black"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif-light" />

                        <TextView
                            android:layout_width="60dp"
                            android:layout_height="match_parent"
                            android:gravity="right|center_vertical"
                            android:paddingRight="15dp"
                            android:text="@string/honorific"
                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif" />

                    </LinearLayout>

                    <LinearLayout style="@style/vertical_line">

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/area_column"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="60dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/survey_address"
                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/area_name"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight=".8"
                            android:gravity="center_vertical"
                            android:hint="@string/area_name"
                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif-light" />

                    </LinearLayout>

                    <LinearLayout
                        style="@style/vertical_line"
                        android:layout_width="match_parent">

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/taskaddress_column"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/address"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:background="@color/white"
                            android:hint="@string/address"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif-light" />

                    </LinearLayout>

                    <LinearLayout style="@style/vertical_line">

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/date_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        style="@style/section_sub_title"
                        android:layout_height="15dp">

                    </LinearLayout>

                    <LinearLayout
                        style="@style/vertical_line"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                    <LinearLayout
                        android:id="@+id/date1_column"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="100dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/research_date1"
                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/research_date1"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical|right|end"
                            android:layout_weight="1"
                            android:background="#00000000"
                            android:gravity="center_vertical|right|end"
                            android:paddingRight="15dp"
                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif-light"
                            tools:ignore="RtlHardcoded,RtlSymmetry" />

                    </LinearLayout>

                    <LinearLayout style="@style/vertical_line" />


                    <LinearLayout
                        android:id="@+id/date3_column"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="100dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/research_date2"
                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/research_date2"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical|right|end"
                            android:layout_weight="1"
                            android:background="#00000000"
                            android:gravity="center_vertical|right|end"
                            android:hint="日時を選択"
                            android:paddingRight="15dp"
                            android:textColor="@color/black"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif-light"
                            tools:ignore="RtlHardcoded,RtlSymmetry" />


                    </LinearLayout>

                    <LinearLayout style="@style/vertical_line" />

                    <LinearLayout
                        android:id="@+id/date3_column"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="100dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/research_date3"
                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/research_date3"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical|right|end"
                            android:layout_weight="1"
                            android:background="#00000000"
                            android:gravity="center_vertical|right|end"
                            android:hint="日時を選択"
                            android:paddingRight="15dp"
                            android:textColor="@color/black"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif-light"
                            tools:ignore="RtlHardcoded,RtlSymmetry" />

                    </LinearLayout>

                    <LinearLayout style="@style/vertical_line" />

                </LinearLayout>


                <LinearLayout
                    android:id="@+id/remarks_layout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <LinearLayout
                        style="@style/section_sub_title"
                        android:layout_height="15dp">

                    </LinearLayout>

                    <LinearLayout style="@style/vertical_line" />

                    <LinearLayout
                        android:id="@+id/remarks_column"
                        android:layout_width="match_parent"
                        android:layout_height="80dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/remarks"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="start|left"
                            android:autofillHints=""
                            android:background="#00000000"
                            android:gravity="start|left"
                            android:hint="@string/remarks"
                            android:inputType="textMultiLine"
                            android:minLines="1"
                            android:paddingLeft="15dp"
                            android:paddingTop="5dp"
                            android:paddingRight="10dp"
                            android:paddingBottom="5dp"
                            android:singleLine="false"
                            android:textSize="15sp"
                            app:fontFamily="sans-serif-light"
                            tools:ignore="RtlHardcoded" />

                    </LinearLayout>

                    <LinearLayout style="@style/vertical_line" />

                    <LinearLayout
                        style="@style/section_sub_title"
                        android:layout_height="15dp">

                    </LinearLayout>
                </LinearLayout>

            </LinearLayout>
        </ScrollView>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/white">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout style="@style/vertical_line_dark" />

            <RelativeLayout
                android:id="@+id/readjustment"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:gravity="center">

                <TextView
                    android:id="@+id/order_readjustment"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="5dp"
                    android:layout_marginLeft="5dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginEnd="5dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/radius_frame_gray"
                    android:gravity="center"
                    android:text="@string/do_readjustment"
                    android:textColor="@color/white"
                    android:textSize="18sp" />
            </RelativeLayout>


            <LinearLayout style="@style/vertical_line_dark" />

            <RelativeLayout
                android:id="@+id/cancel_text"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@color/order_detail_button_back"
                android:gravity="center">

                <ImageView
                    android:id="@+id/order_cancel_icon"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginTop="15dp"

                    android:src="@drawable/delete" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="10dp"
                    android:layout_marginLeft="10dp"
                    android:layout_toRightOf="@+id/order_cancel_icon"
                    android:gravity="center"
                    android:text="@string/order_cancel"
                    android:textColor="@color/red"
                    android:textSize="18sp" />
            </RelativeLayout>
        </LinearLayout>
    </FrameLayout>

</RelativeLayout>
