<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_width="match_parent"
	android:layout_height="wrap_content" >

	<TextView
		android:id="@+id/title"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:layout_alignParentLeft="true"
		android:layout_toLeftOf="@+id/page"
		android:singleLine="true"
		android:layout_centerVertical="true"
		android:paddingLeft="8dp"
		android:textAppearance="?android:attr/textAppearanceMedium" />

	<TextView
		android:id="@+id/page"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:layout_alignBaseline="@+id/title"
		android:layout_alignBottom="@+id/title"
		android:layout_alignParentRight="true"
		android:paddingRight="8dp"
		android:textAppearance="?android:attr/textAppearanceMedium" />

</RelativeLayout>
