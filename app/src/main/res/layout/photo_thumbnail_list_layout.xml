<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main_layout"
    android:layout_width="wrap_content"
    android:layout_height="fill_parent"
    android:orientation="vertical"
    android:scrollbarStyle="insideOverlay">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:orientation="horizontal" >

        <androidx.appcompat.widget.Toolbar
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:maxLines="1"
                android:maxEms="10"
                android:ellipsize="end"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"/>

        </androidx.appcompat.widget.Toolbar>

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:paddingBottom="10dp"
            android:paddingTop="10dp"
            android:src="@drawable/com_headerarrow"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"/>

        <LinearLayout
            android:id="@+id/list_button"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:paddingStart="15dp"
            android:paddingEnd="15dp">

            <ImageView
                android:id="@+id/list_image"
                android:layout_width="15dp"
                android:layout_height="match_parent"
                android:layout_marginTop="1dp"
                android:src="@drawable/list"
                android:scaleType="fitCenter"/>

            <TextView
                android:id="@+id/list"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingStart="5dp"
                android:gravity="center_vertical"
                android:text="@string/list_button"
                android:textColor="@color/white"
                android:textSize="16sp"/>

        </LinearLayout>

    </RelativeLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/LayoutHeader"
        android:background="@color/gray" />

    <TextView
        android:id="@+id/photo_thumbnail_region"
        android:layout_width="fill_parent"
        android:layout_height="46dp"
        android:layout_below="@+id/divider"
        android:background="@color/title_gray"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:paddingLeft="15dp"
        android:textColor="@color/black"
        android:textSize="18sp"
        android:textStyle="bold" />

    <View
        android:id="@+id/divider2"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/photo_thumbnail_region"
        android:background="@color/gray" />

    <ListView
        android:id="@+id/photo_thumbnail_list_listview"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:cacheColorHint="@android:color/background_light"
        android:divider="@color/gray"
        android:dividerHeight="1px"
        android:layout_below="@id/divider2" />

    <View
        android:id="@+id/divider3"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/photo_thumbnail_list_listview"
        android:background="@color/gray" />

</RelativeLayout>