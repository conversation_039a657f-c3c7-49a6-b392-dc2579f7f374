<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="45dp"
    android:background="@drawable/ripple_white"
    android:descendantFocusability="blocksDescendants"
    android:minHeight="45dp"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/check_image"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_marginLeft="3dp"
        android:layout_marginRight="3dp"
        android:paddingTop="12.5dp"
        android:paddingBottom="12.5dp"
        android:scaleType="fitCenter" />

    <TextView
        android:id="@+id/search_person_name"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:fontFamily="sans-serif-light" />

    <TextView
        android:id="@+id/request_date"
        android:layout_width="100dp"
        android:layout_height="match_parent"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="13dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="13dp"
        android:background="@drawable/radius_frame_blue"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:fontFamily="sans-serif" />

</LinearLayout>