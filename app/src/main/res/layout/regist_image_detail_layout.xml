<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/image_detail_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/contents_background"
    android:orientation="vertical"
    tools:context="fois.dailyreportsystem.activity.survey.ShootingImageDetailActivity">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay"
            app:contentInsetStart="0dp">

            <ImageView
                android:id="@+id/back_arrow"
                android:layout_width="45dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:src="@drawable/white_arrow_l" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_marginBottom="0dp"
                android:gravity="center"
                android:text="@string/regist_image_detail_title"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/head_complete"
                android:layout_width="60dp"
                android:layout_height="match_parent"
                android:layout_alignParentEnd="true"
                android:layout_gravity="right|end"
                android:layout_marginEnd="15dp"
                android:gravity="center|right"
                android:paddingEnd="10dp"
                android:text="@string/registration"
                android:textColor="@color/white"
                android:textSize="16sp"
                tools:ignore="RtlHardcoded,RtlSymmetry"
                android:layout_alignParentRight="true"
                android:layout_marginRight="15dp"
                android:paddingRight="10dp" />

        </androidx.appcompat.widget.Toolbar>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/image_detail_main_contents"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <fois.dailyreportsystem.util.ui.FixedAspectFrameLayout
            android:id="@+id/image_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:aspectRate="1.3333" >

            <ImageView
                android:id="@+id/regist_image"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:scaleType="fitCenter"
                android:src="@drawable/blank_image"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </fois.dailyreportsystem.util.ui.FixedAspectFrameLayout>

        <ScrollView
            android:id="@+id/diagnosis_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/sub_section"
                    style="@style/section_sub_title"
                    android:text="@string/shooting_image_detail_diagnosis"
                    app:fontFamily="sans-serif" />

                <View style="@style/vertical_line" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:background="@color/white"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/out_point_category_select"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="200"
                        android:gravity="start|center_vertical"
                        android:paddingLeft="15dp"
                        android:paddingEnd="15dp"
                        android:paddingRight="15dp"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:id="@+id/out_point_category_select_icon"
                        android:layout_width="12dp"
                        android:layout_height="8dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:layout_weight="1"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_arrow02" />
                </LinearLayout>

                <View style="@style/vertical_line" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:background="@color/white"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/out_point_item_select"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="200"
                        android:gravity="start|center_vertical"
                        android:paddingLeft="15dp"
                        android:paddingRight="15dp"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:id="@+id/out_point_item_select_icon"
                        android:layout_width="12dp"
                        android:layout_height="8dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:layout_weight="1"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_arrow02" />
                </LinearLayout>

                <View style="@style/vertical_line" />

                <LinearLayout
                    android:id="@+id/image_select"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:background="@color/white"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/for_spare_area"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight=".5">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:layout_weight=".4"
                            android:gravity="right">

                            <ImageView
                                android:id="@+id/for_spare_check"
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:paddingTop="10dp"
                                android:paddingBottom="10dp"
                                android:scaleType="fitCenter"
                                android:src="@drawable/circle_non_check" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/for_spare"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight=".6"
                            android:background="@color/white"
                            android:gravity="center_vertical"
                            android:paddingLeft="3dp"
                            android:text="@string/image_for_spare"
                            android:textColor="@color/button_gray"
                            app:fontFamily="sans-serif" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/for_report_area"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight=".5">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:layout_weight=".4"
                            android:gravity="right">

                            <ImageView
                                android:id="@+id/for_report_check"
                                android:layout_width="40dp"
                                android:layout_height="match_parent"
                                android:paddingTop="10dp"
                                android:paddingBottom="10dp"
                                android:scaleType="center"
                                android:src="@drawable/circle_non_check" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/for_report"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight=".6"
                            android:background="@color/white"
                            android:gravity="center_vertical"
                            android:paddingLeft="3dp"
                            android:text="@string/image_for_report"
                            android:textColor="@color/button_gray"
                            app:fontFamily="sans-serif" />
                    </LinearLayout>
                </LinearLayout>

                <View style="@style/vertical_line" />

                <LinearLayout
                    style="@style/section_sub_title"
                    android:layout_height="15dp" />

                <View style="@style/vertical_line" />

                <LinearLayout
                    android:id="@+id/complete_button"
                    android:layout_width="match_parent"
                    android:layout_height="40dp">

                    <Button
                        android:id="@+id/complete_text"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/light_blue"
                        android:gravity="center"
                        android:text="@string/do_registration"
                        android:textColor="@color/white" />
                </LinearLayout>

                <View style="@style/vertical_line" />

                <LinearLayout
                    style="@style/section_sub_title"
                    android:layout_height="15dp" />

            </LinearLayout>

        </RelativeLayout>
        </ScrollView>
    </LinearLayout>

    <View style="@style/vertical_line" />

    <TextView
        android:id="@+id/comment_subtitle"
        style="@style/section_sub_title"
        android:text="@string/shooting_image_detail_comment"
        app:fontFamily="sans-serif" />

    <View style="@style/vertical_line" />

    <LinearLayout
        android:id="@+id/comment_area"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:background="@color/dark_gray">

        <EditText
            android:id="@+id/diagnosis_comment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:ems="10"
            android:gravity="top|left"
            android:inputType="textMultiLine"
            android:paddingLeft="15dp"
            android:paddingTop="5dp"
            android:paddingRight="10dp"
            android:paddingBottom="5dp"
            android:textSize="13dp"
            app:fontFamily="sans-serif-light" />
    </LinearLayout>

</LinearLayout>
