<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    tools:context="fois.dailyreportsystem.activity.setting.CompanyCodeRegisterActivity">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="fill_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:orientation="horizontal" >

        <androidx.appcompat.widget.Toolbar
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/company_number_title"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"/>

        </androidx.appcompat.widget.Toolbar>

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:paddingBottom="10dp"
            android:paddingTop="10dp"
            android:src="@drawable/com_headerarrow"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"/>

        <TextView
            android:id="@+id/register"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="15dp"
            android:gravity="center_vertical"
            android:text="@string/first_setting_register"
            android:textColor="@color/white"
            android:textSize="16sp"/>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/LayoutLine"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/LayoutHeader"
        android:background="@color/gray"
        android:orientation="vertical" />

    <TextView
        android:id="@+id/mail_info"
        android:layout_width="fill_parent"
        android:layout_height="38dp"
        android:layout_below="@+id/LayoutLine"
        android:background="@color/contents_gray"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:paddingLeft="15dp"
        android:text="@string/company_info"
        android:textColor="@color/dark_gray"
        android:textSize="16sp" />

    <LinearLayout
        android:id="@+id/LayoutLine2"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/mail_info"
        android:background="@color/gray"
        android:orientation="vertical" />

    <LinearLayout
        android:id="@+id/phone_mail"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_below="@+id/LayoutLine2"
        android:background="@color/white">

        <TextView
            android:id="@+id/company_number_text"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:text="@string/company_number"
            android:textColor="@color/light_blue"
            android:textSize="18sp" />

        <EditText
            android:id="@+id/company_number_input"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:background="@null"
            android:gravity="end|center_vertical"
            android:inputType="number"
            android:maxLength="6"
            android:paddingRight="15dp"
            android:textColor="@color/black"
            android:textSize="18sp" />

        <ImageView
            android:layout_width="15dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="15dp"
            android:src="@drawable/com_pencil"
            android:scaleType="fitCenter"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/LayoutLine3"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/phone_mail"
        android:background="@color/gray"
        android:orientation="vertical" />

    <TextView
        android:id="@+id/user_info"
        android:layout_width="fill_parent"
        android:layout_height="38dp"
        android:layout_below="@+id/LayoutLine3"
        android:background="@color/contents_gray"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:paddingLeft="15dp"
        android:text="@string/user_info"
        android:textColor="@color/dark_gray"
        android:textSize="16sp" />

    <LinearLayout
        android:id="@+id/LayoutLine4"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/user_info"
        android:background="@color/gray"
        android:orientation="vertical" />

    <LinearLayout
        android:id="@+id/phone_number"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_below="@+id/LayoutLine4"
        android:background="@color/white">

        <TextView
            android:id="@+id/phone_number_text"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:text="@string/phone_number"
            android:textColor="@color/light_blue"
            android:textSize="18sp" />

        <EditText
            android:id="@+id/phone_number_input"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="7"
            android:background="@null"
            android:gravity="end|center_vertical"
            android:inputType="number"
            android:maxLength="12"
            android:paddingRight="15dp"
            android:textColor="@color/black"
            android:textSize="18sp" />

        <ImageView
            android:layout_width="15dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="15dp"
            android:src="@drawable/com_pencil"
            android:scaleType="fitCenter"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/LayoutLine5"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/phone_number"
        android:background="@color/gray"
        android:orientation="vertical" />

    <LinearLayout
        android:id="@+id/password"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_below="@+id/LayoutLine5"
        android:background="@color/white">

        <TextView
            android:id="@+id/password_text"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:text="@string/password"
            android:textColor="@color/light_blue"
            android:textSize="18sp" />

        <EditText
            android:id="@+id/password_input"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:background="@null"
            android:gravity="end|center_vertical"
            android:inputType="textPassword"
            android:maxLength="8"
            android:paddingRight="15dp"
            android:textColor="@color/black" />

        <ImageView
            android:layout_width="15dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="15dp"
            android:src="@drawable/com_pencil"
            android:scaleType="fitCenter"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/LayoutLine6"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/password"
        android:background="@color/gray"
        android:orientation="vertical" />

    <LinearLayout
        android:id="@+id/company_number_register_area"
        android:layout_width="match_parent"
        android:layout_height="61dp"
        android:layout_alignParentBottom="true"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="1dp"
            android:background="@color/gray"
            android:orientation="vertical" />

        <FrameLayout
            android:id="@+id/company_number_register"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@color/white"
            android:padding="10dp">

            <ImageView
                android:id="@+id/company_number_register_button"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:src="@drawable/com_regist"
                android:scaleType="fitCenter"/>

        </FrameLayout>

    </LinearLayout>

</RelativeLayout>
