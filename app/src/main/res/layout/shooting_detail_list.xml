<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context="fois.dailyreportsystem.activity.survey.WebViewActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/diagnosis_part"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/gray"
            android:layout_margin="1dp"
            android:layout_weight="3"
            android:gravity="center"
            android:text="シーリング">

        </TextView>

        <TextView
            android:id="@+id/dia_extra"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="1dp"
            android:layout_weight="1"
            android:background="@color/gray"
            android:gravity="center"
            android:text="４">

        </TextView>

        <TextView
            android:id="@+id/dia_report"
            android:layout_width="0dp"
            android:background="@color/sky_blue"
            android:layout_height="wrap_content"
            android:layout_margin="1dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="６">

        </TextView>

        <TextView
            android:id="@+id/observation"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="1dp"
            android:background="@color/gray"
            android:layout_weight="3"
            android:gravity="center"
            android:text="所見">

        </TextView>

        <TextView
            android:id="@+id/obs_extra"
            android:background="@color/gray"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="1dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="１">

        </TextView>

        <TextView
            android:id="@+id/obs_report"
            android:layout_width="0dp"
            android:background="@color/sky_blue"
            android:layout_height="wrap_content"
            android:layout_margin="1dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="１">

        </TextView>

    </LinearLayout>


</RelativeLayout>
