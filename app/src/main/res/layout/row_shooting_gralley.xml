<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/setting_company_name"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:descendantFocusability="blocksDescendants"
    android:orientation="horizontal">


    <ImageView
        android:id="@+id/shooting_image"

        android:layout_width="112dp"

        android:layout_height="85dp"
        android:layout_margin="5dp" />

    <LinearLayout
        android:id="@+id/topPanel"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:orientation="horizontal">

            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/category_name"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="top|left"
                    android:layout_weight="1"
                    android:background="@drawable/square_frame_gray"
                    android:gravity="center|fill_vertical"
                    android:maxLines="1"
                    android:paddingLeft="7dp"
                    android:paddingRight="7dp"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    app:fontFamily="sans-serif" />
            </FrameLayout>

            <ImageView
                android:id="@+id/spare_check"
                android:layout_width="26dp"
                android:layout_height="26dp"
                android:contentDescription="@string/app_name"
                android:scaleType="center"
                android:src="@drawable/circle_blue_check" />

            <TextView
                android:id="@+id/spare_text"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"

                android:gravity="center_vertical"
                android:paddingRight="8dp"
                android:text="@string/image_for_spare"
                app:fontFamily="sans-serif-light" />

            <ImageView
                android:id="@+id/report_check"
                android:layout_width="26dp"
                android:layout_height="26dp"

                android:scaleType="center"
                android:src="@drawable/circle_blue_check" />

            <TextView
                android:id="@+id/report_text"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"

                android:gravity="center_vertical"
                android:paddingRight="8dp"
                android:text="@string/image_for_report"
                app:fontFamily="sans-serif-light" />

        </LinearLayout>

        <TextView
            android:id="@+id/item_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lines="1"
            android:maxLines="1"
            android:paddingTop="3dp"
            android:paddingRight="5dp"
            android:paddingBottom="3dp"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:fontFamily="sans-serif-medium" />

        <TextView
            android:id="@+id/comment"
            android:layout_width="match_parent"
            android:layout_height="0dp"

            android:layout_weight=".4"
            android:lines="2"
            android:maxLines="2"
            android:paddingRight="5dp"
            android:textSize="11sp"
            app:fontFamily="sans-serif-light" />


    </LinearLayout>


</LinearLayout>