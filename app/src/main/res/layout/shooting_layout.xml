<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context="fois.dailyreportsystem.activity.survey.ShootingActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="4:3"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0">

            <fois.dailyreportsystem.util.ui.AutoFitTextureView
                android:id="@+id/textureView"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginTop="0dp">

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="61dp"
            android:layout_height="match_parent"
            android:paddingTop="20dp"
            android:paddingBottom="20dp"
            android:rotation="180"
            android:src="@drawable/white_arrow_l" />
    </FrameLayout>

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginLeft="380dp"
        android:layout_marginTop="0dp">
    </FrameLayout>

    <FrameLayout
        android:id="@+id/thumbnail"
        android:layout_width="104dp"
        android:layout_height="79dp"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="15dp"
        android:layout_marginLeft="15dp"
        android:layout_marginBottom="15dp"
        android:visibility="invisible">

        <LinearLayout
            android:id="@+id/thumbnail_background"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            tools:visibility="visible">

            <com.rishabhharit.roundedimageview.RoundedImageView
                android:id="@+id/latest_photo_thumbnail"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center|center_horizontal|center_vertical"
                android:layout_margin="2dp"
                android:padding="0dp"
                android:scaleType="centerCrop"
                app:cornerRadius="6dp"
                app:roundedCorners="all" />

        </LinearLayout>

        <ProgressBar
            android:id="@+id/image_progressBar"
            style="@android:style/Widget.ProgressBar"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center"
            android:visibility="gone" />

    </FrameLayout>

    <FrameLayout
        android:id="@+id/table"
        android:layout_width="200dp"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:background="@null">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:baselineAligned="false"
            android:weightSum="1">

            <ListView
                android:id="@+id/diagnosis_part"
                android:layout_width="62dp"
                android:layout_height="match_parent"
                android:choiceMode="singleChoice"
                android:divider="@android:color/black"
                android:dividerHeight="1px"
                android:listSelector="@color/orange" />

            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

            <ListView
                android:id="@+id/diagnosis_comment"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:alpha="1"
                android:divider="@android:color/black"
                android:dividerHeight="1px"
                android:drawSelectorOnTop="true"
                android:listSelector="@drawable/ripple_transparent" />

            </FrameLayout>

        </LinearLayout>
    </FrameLayout>


    <FrameLayout
        android:id="@+id/warning_message_frame"
        android:layout_width="400dp"
        android:layout_height="40dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
        android:layout_marginStart="0dp"
        android:layout_marginLeft="0dp"
        android:layout_marginEnd="22dp"
        android:layout_marginRight="22dp"
        android:layout_toStartOf="@+id/table"
        android:layout_toLeftOf="@+id/table"
        android:background="@null"
        android:rotation="-90">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|center_vertical"
            android:background="@drawable/radius_frame_black"
            android:gravity="center|center_horizontal|center_vertical"
            android:padding="10dp"
            android:text="@string/rotate_message"
            android:textColor="@color/white" />
    </FrameLayout>

</RelativeLayout>
