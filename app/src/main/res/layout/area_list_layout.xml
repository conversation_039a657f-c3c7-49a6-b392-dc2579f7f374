<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="@drawable/com_header"
        android:theme="@style/AppTheme.AppBarOverlay"

        app:contentInsetStart="0dp"
        app:layout_constraintBottom_toTopOf="@id/area_list"

        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"

        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/back_title"
            android:layout_width="wrap_content"
            android:layout_height="45dp"
            android:layout_alignParentLeft="true"
            android:contentDescription="@string/app_name"
            android:gravity="left|center_vertical"
            android:minWidth="60dp"
            android:paddingLeft="15dp"
            android:text="@string/shooting_all"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/survey_property_list_title"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"/>

    </androidx.appcompat.widget.Toolbar>

    <ListView
        android:id="@+id/area_list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:divider="@color/gray"
        android:dividerHeight="1px"

        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintStart_toStartOf="@id/toolbar"

        app:layout_constraintTop_toBottomOf="@id/toolbar" />

</androidx.constraintlayout.widget.ConstraintLayout>