<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:background="@color/contents_gray"
    tools:context="fois.dailyreportsystem.activity.login.LoginActivity">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="fill_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:orientation="horizontal" >

        <androidx.appcompat.widget.Toolbar
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"/>

        </androidx.appcompat.widget.Toolbar>

        <TextView
            android:id="@+id/login"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="15dp"
            android:gravity="center_vertical"
            android:text="@string/login"
            android:textColor="@color/white"
            android:textSize="16sp"/>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/LayoutLine"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/LayoutHeader"
        android:background="@color/gray"
        android:orientation="vertical" />

    <ListView
        android:id="@+id/login_listview"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_below="@+id/LayoutLine"
        android:layout_above="@+id/background"
        android:divider="@color/gray"
        android:dividerHeight="1px"/>

    <TextView
        android:id="@+id/background"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:background="@color/contents_gray"
        android:gravity="center"
        android:textColor="@color/dark_gray"
        android:textSize="14sp"
        android:layout_above="@+id/divider" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_above="@+id/login_button_area"
        android:background="@color/gray" />

    <FrameLayout
        android:id="@+id/login_button_area"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:padding="10dp">

        <TextView
            android:id="@+id/login_button"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/login_button"
            android:gravity="center"
            android:text="@string/login"
            android:textColor="@color/white"
            android:textSize="18sp" />

    </FrameLayout>

</RelativeLayout>
