<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="50dp"
    android:minHeight="50dp"
    android:background="@color/white"
    android:descendantFocusability="blocksDescendants">

    <LinearLayout
        android:id="@+id/work_process_area"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/list"
        android:orientation="horizontal">

        <CheckBox
            android:id="@+id/work_process_checkbox"
            style="@style/CustomCheckbox"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:scaleX="1.5"
            android:scaleY="1.5"
            android:layout_marginLeft="15dp"
            android:clickable="false"/>

        <TextView
            android:id="@+id/work_process_content"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:text="@string/acceptance_request_content"
            android:textColor="@color/black"
            android:textSize="18sp" />


    </LinearLayout>

</RelativeLayout>