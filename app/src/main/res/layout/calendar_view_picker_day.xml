<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/day_item_background"
    android:orientation="vertical">

    <TextView
        android:id="@+id/dayLabel"
        android:layout_width="@dimen/day_label_size_events_enabled"
        android:layout_height="@dimen/day_label_size_events_enabled"
        android:layout_gravity="center_horizontal"
        android:textColor="@drawable/yellow_arrow_l"
        android:layout_marginTop="3dp"
        android:gravity="center"
        android:textSize="20sp"
        android:focusable="true"
        android:clickable="true" />

    <TextView
        android:id="@+id/dayIcon"
        android:layout_width="6dp"
        android:layout_height="7dp"
        android:layout_gravity="center"
        android:layout_marginTop="0dp"
        android:layout_marginBottom="3dp"
        android:gravity="center|top"
        android:textColor="@color/calendar_event_dot"
        android:textSize="5sp" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/nextMonthDayColor" />

</LinearLayout>

