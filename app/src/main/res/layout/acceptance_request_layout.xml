<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main_layout"
    android:layout_width="wrap_content"
    android:layout_height="fill_parent"
    android:orientation="vertical"
    android:scrollbarStyle="insideOverlay"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:orientation="horizontal" >

        <androidx.appcompat.widget.Toolbar
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:maxLines="1"
                android:maxEms="10"
                android:ellipsize="end"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"/>

        </androidx.appcompat.widget.Toolbar>

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:paddingBottom="10dp"
            android:paddingTop="10dp"
            android:src="@drawable/com_headerarrow"
            android:layout_centerVertical="true"
            android:layout_alignParentLeft="true" />

    </RelativeLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/gray"
        android:layout_below="@+id/LayoutHeader" />

    <RelativeLayout
        android:id="@+id/LayoutMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/divider"
        android:layout_above="@+id/divider3">

        <LinearLayout
            android:id="@+id/LayoutSelectTitle"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:minHeight="46dp"
            android:background="@color/title_gray"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/schedule1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:focusableInTouchMode="true"
                android:gravity="center_vertical"
                android:paddingLeft="15dp"
                android:text="@string/acceptance_request_title"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/layoutDate"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:minHeight="50dp"
            android:orientation="horizontal"
            android:layout_below="@+id/LayoutSelectTitle">

            <TextView
                android:id="@+id/reportDate"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="4"
                android:gravity="center_vertical"
                android:text="@string/acceptance_request_date"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:paddingLeft="15dp" />

            <TextView
                android:id="@+id/report_task_name"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="6"
                android:gravity="end|center_vertical"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:paddingRight="15dp" />

        </LinearLayout>

        <View
            android:id="@+id/divider2"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/gray"
            android:layout_below="@+id/layoutDate" />


        <LinearLayout
            android:id="@+id/LayoutMain1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical"
            android:layout_below="@+id/divider2">

            <ListView
                android:id="@+id/work_process_list_listview"
                android:layout_width="fill_parent"
                android:layout_height="fill_parent"
                android:layout_below="@+id/LayoutLine2"
                android:divider="@color/gray"
                android:dividerHeight="1px"/>

            <View
                android:layout_width="fill_parent"
                android:layout_height="1dp"
                android:background="@color/gray" />

        </LinearLayout>

    </RelativeLayout>

    <View
        android:id="@+id/divider3"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:background="@color/gray"
        android:layout_above="@+id/report_next"/>

    <FrameLayout
        android:id="@+id/report_next"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:padding="10dp">

        <ImageView
            android:id="@+id/register_button"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:src="@drawable/gray_next"
            android:scaleType="fitCenter"/>

    </FrameLayout>

</RelativeLayout>
