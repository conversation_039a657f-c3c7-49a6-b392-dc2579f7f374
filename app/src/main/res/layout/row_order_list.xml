<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="42dp"
    android:background="@drawable/ripple_white"
    android:minHeight="50dp"
    android:paddingTop="2dp"
    android:paddingBottom="2dp">

    <TextView
        android:id="@+id/research_date"
        android:layout_width="65dp"
        android:layout_height="match_parent"

        android:gravity="right|top"

        android:paddingTop="3dp"
        android:textColor="@color/light_blue"
        android:textSize="16sp"
        app:fontFamily="sans-serif" />

    <View
        android:id="@+id/status_bar"
        android:layout_width="3dp"
        android:layout_height="match_parent"
        android:layout_marginLeft="4dp"
        android:layout_marginRight="4dp"
        android:background="@color/orange">

    </View>

    <LinearLayout
        android:id="@+id/task_info"
        android:layout_width="0dp"
        android:layout_height="match_parent"

        android:layout_weight="1"

        android:orientation="vertical">

        <TextView
            android:id="@+id/task_name"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight=".55"
            android:paddingTop="3dp"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:fontFamily="sans-serif-light" />

        <TextView
            android:id="@+id/address"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight=".45"
            android:gravity="center_vertical"
            android:textColor="@android:color/darker_gray"
            android:textSize="12sp"
            app:fontFamily="sans-serif-light" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/status_bar_info"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"

        android:layout_marginLeft="3dp"
        android:layout_marginRight="3dp"

        >

        <FrameLayout
            android:id="@+id/order_status_info"
            android:layout_width="80dp"
            android:layout_height="match_parent"

            >

            <TextView
                android:id="@+id/order_status"
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:gravity="center"

                android:textColor="@color/white"
                android:textSize="12sp"
                app:fontFamily="sans-serif-light" />
        </FrameLayout>

    </LinearLayout>

</LinearLayout>