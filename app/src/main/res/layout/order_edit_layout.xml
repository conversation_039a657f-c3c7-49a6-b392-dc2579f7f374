<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/order_edit_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/contents_gray"
    android:orientation="vertical"
    tools:context="fois.dailyreportsystem.activity.request.OrderEditActivity">

    <FrameLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentTop="true">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginEnd="0dp"
            android:layout_marginRight="0dp"
            android:layout_marginBottom="0dp"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

        </androidx.appcompat.widget.Toolbar>

        <TextView
            android:id="@+id/menu_cancel"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="left"
            android:layout_marginStart="5dp"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:text="@string/cancel"
            android:textColor="@color/orange_link"
            android:textSize="16sp"
            tools:ignore="RtlCompat" />

        <TextView
            android:id="@+id/plan_header_title"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginBottom="0dp"
            android:gravity="center"
            android:text="@string/order_registration_title"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/update_header"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="end|right"
            android:gravity="center"
            android:paddingEnd="10dp"
            android:paddingRight="10dp"
            android:text="@string/update"
            android:textColor="@color/half_transparent"
            android:textSize="16sp" />

    </FrameLayout>

    <LinearLayout
        android:id="@+id/contents"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/header"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/maker_user_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="visible">

            <TextView
                android:id="@+id/maker_user_info_title"
                style="@style/section_sub_title"
                android:layout_width="match_parent"
                android:layout_height="25dp"
                android:text="@string/request_info"
                app:fontFamily="sans-serif-light" />

            <LinearLayout
                android:id="@+id/plan_info_line1"
                style="@style/vertical_line"
                android:layout_height="match_parent"
                android:orientation="horizontal" />

            <LinearLayout
                android:id="@+id/maker_user_column"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:orientation="horizontal">

                <RelativeLayout
                    android:id="@+id/no_choice"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/white">

                    <ImageView
                        android:id="@+id/maker_user_arrow"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentRight="true"
                        android:layout_alignParentBottom="true"
                        android:paddingTop="16.5dp"
                        android:paddingBottom="16.5dp"
                        app:srcCompat="@drawable/com_arrow01" />

                    <LinearLayout
                        android:id="@+id/maker_name_layout"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_alignParentLeft="true"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentBottom="true"
                        android:layout_toLeftOf="@id/maker_honor"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/maker_company_name"
                            android:layout_width="0dp"

                            android:layout_height="match_parent"

                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:layout_weight=".6"
                            android:gravity="left|center_vertical"
                            android:textColor="@color/black"
                            android:textSize="15sp"
                            app:fontFamily="sans-serif-light" />

                        <TextView
                            android:id="@+id/maker_user_name"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight=".4"
                            android:gravity="right|center_vertical"
                            android:textColor="@color/black"
                            android:textSize="15sp"
                            app:fontFamily="sans-serif-light"
                            tools:ignore="RtlHardcoded,RtlSymmetry" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/do_maker_staff_list"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center_vertical"
                        android:gravity="center"
                        android:text="@string/order_do_staff_list"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp"
                        android:visibility="gone"
                        app:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/maker_honor"
                        android:layout_width="33dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:layout_toLeftOf="@id/maker_user_arrow"
                        android:gravity="right|center_vertical"
                        android:text="@string/order_honor"
                        android:textColor="@color/light_blue"
                        android:textSize="17sp"
                        android:visibility="gone"
                        app:fontFamily="sans-serif"
                        tools:ignore="RtlHardcoded,RtlSymmetry" />

                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/maker_user_info_line2"
                style="@style/vertical_line"
                android:layout_height="match_parent"
                android:orientation="horizontal" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/survery_info_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/survery_info_title2"
                style="@style/section_sub_title"
                android:layout_width="match_parent"
                android:layout_height="25dp"
                android:text="@string/order_detail_section"
                app:fontFamily="sans-serif-light" />

            <LinearLayout
                android:id="@+id/line1"
                style="@style/vertical_line"
                android:orientation="horizontal" />

            <LinearLayout
                android:id="@+id/task_name_column"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/white"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/task_name"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="15dp"
                    android:layout_marginLeft="15dp"
                    android:layout_weight="1"
                    android:autofillHints="@string/task_name"
                    android:background="#00000000"
                    android:hint="@string/task_name"
                    android:inputType="text"
                    android:singleLine="true"
                    android:textColorHint="@color/gray"
                    android:textSize="17sp"
                    app:fontFamily="sans-serif-light"
                    tools:ignore="UnusedAttribute" />

                <TextView
                    android:layout_width="60dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="right|center_vertical"
                    android:paddingRight="15dp"
                    android:text="@string/honorific"
                    android:textColor="@color/light_blue"
                    android:textSize="17sp"
                    app:fontFamily="sans-serif"
                    tools:ignore="RtlHardcoded,RtlSymmetry" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/line2"
                style="@style/vertical_line"
                android:orientation="horizontal" />

            <LinearLayout
                android:id="@+id/area_name_column"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/white"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textView4"
                    android:layout_width="60dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="15dp"
                    android:layout_marginLeft="15dp"
                    android:gravity="center_vertical"
                    android:text="@string/survey_address"
                    android:textColor="@color/light_blue"
                    android:textSize="17sp"
                    app:fontFamily="sans-serif" />

                <TextView
                    android:id="@+id/area_name"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginRight="15dp"
                    android:layout_weight="1"
                    android:background="#00000000"
                    android:gravity="center_vertical"
                    android:hint="@string/area_name"
                    android:textColor="@color/light_blue"
                    android:textColorHint="@color/gray"
                    android:textSize="17sp"
                    app:fontFamily="sans-serif"
                    tools:ignore="RtlHardcoded" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/line3"
                style="@style/vertical_line"
                android:orientation="horizontal" />

            <LinearLayout
                android:id="@+id/task_address_column"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/white"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/address"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="15dp"
                    android:layout_marginLeft="15dp"
                    android:background="#00000000"
                    android:hint="@string/address"
                    android:importantForAutofill="no"
                    android:lines="1"
                    android:singleLine="true"
                    android:textColorHint="@color/gray"
                    android:textSize="17sp"
                    app:fontFamily="sans-serif-light"
                    tools:ignore="TextFields" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/line4"
                style="@style/vertical_line"
                android:orientation="horizontal" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/date_info_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/survey_date_title"
                style="@style/section_sub_title"
                android:layout_height="15dp"
                android:orientation="vertical">

            </LinearLayout>

            <LinearLayout
                android:id="@+id/date_line4"
                style="@style/vertical_line"
                android:layout_height="match_parent"
                android:orientation="horizontal" />

            <LinearLayout
                android:id="@+id/date1_column"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/white"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="100dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="15dp"
                    android:layout_marginLeft="15dp"
                    android:gravity="center_vertical"
                    android:text="@string/research_date1"
                    android:textColor="@color/light_blue"
                    android:textSize="17sp"
                    app:fontFamily="sans-serif" />

                <TextView
                    android:id="@+id/research_date1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical|right|end"
                    android:layout_weight="1"
                    android:background="#00000000"
                    android:gravity="center_vertical|right|end"
                    android:paddingRight="15dp"
                    android:textColor="@color/light_blue"
                    android:textSize="17sp"
                    app:fontFamily="sans-serif-light"
                    tools:ignore="RtlHardcoded,RtlSymmetry" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/date_line1"
                style="@style/vertical_line"
                android:orientation="horizontal">

            </LinearLayout>

            <LinearLayout
                android:id="@+id/date2_column"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/white"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="100dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="15dp"
                    android:layout_marginLeft="15dp"
                    android:gravity="center_vertical"
                    android:text="@string/research_date2"
                    android:textColor="@color/light_blue"
                    android:textSize="17sp"
                    app:fontFamily="sans-serif" />

                <TextView
                    android:id="@+id/research_date2"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical|right|end"
                    android:layout_weight="1"
                    android:autofillHints="日付を選択"
                    android:background="#00000000"
                    android:gravity="center_vertical|right|end"
                    android:hint="日時を選択"
                    android:paddingRight="15dp"
                    android:textColor="@color/black"
                    android:textColorHint="@color/gray"
                    android:textSize="17sp"
                    app:fontFamily="sans-serif-light"
                    tools:ignore="HardcodedText,RtlHardcoded,RtlSymmetry,UnusedAttribute" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/date_line2"
                style="@style/vertical_line"
                android:orientation="horizontal" />

            <LinearLayout
                android:id="@+id/date3_column"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/white"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="100dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="15dp"
                    android:layout_marginLeft="15dp"
                    android:gravity="center_vertical"
                    android:text="@string/research_date3"
                    android:textColor="@color/light_blue"
                    android:textSize="17sp"
                    app:fontFamily="sans-serif" />

                <TextView
                    android:id="@+id/research_date3"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical|right|end"
                    android:layout_weight="1"
                    android:background="#00000000"
                    android:gravity="center_vertical|right|end"
                    android:hint="日時を選択"
                    android:paddingRight="15dp"
                    android:textColor="@color/black"
                    android:textColorHint="@color/gray"
                    android:textSize="17sp"
                    app:fontFamily="sans-serif-light"
                    tools:ignore="RtlHardcoded,RtlSymmetry" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/date_line3"
                style="@style/vertical_line"
                android:orientation="horizontal" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/remarks_info_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/remarks_title1"
                style="@style/section_sub_title"
                android:layout_height="15dp"
                android:orientation="horizontal" />

            <LinearLayout
                android:id="@+id/remarks_line1"
                style="@style/vertical_line"
                android:orientation="horizontal" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:background="@color/white"
                android:orientation="horizontal">


                <EditText
                    android:id="@+id/remarks"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="start|left"
                    android:autofillHints=""
                    android:background="#00000000"
                    android:gravity="start|left"
                    android:hint="@string/remarks"
                    android:inputType="textMultiLine"
                    android:minLines="1"
                    android:paddingLeft="15dp"
                    android:paddingTop="5dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="5dp"
                    android:singleLine="false"
                    android:textColorHint="@color/gray"
                    android:textSize="15sp"
                    app:fontFamily="sans-serif-light"
                    tools:ignore="RtlHardcoded" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/remarks_line2"
                style="@style/vertical_line"
                android:orientation="horizontal" />
            <LinearLayout
                android:id="@+id/remarks_title2"
                style="@style/section_sub_title"
                android:layout_height="15dp"
                android:orientation="horizontal" />

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@android:color/transparent"
        android:gravity="center">

        <LinearLayout
            android:id="@+id/confirm_layout"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:orientation="vertical"
            android:visibility="visible">

            <LinearLayout
                android:id="@+id/confirm_line"
                style="@style/vertical_line_dark"
                android:orientation="horizontal">

            </LinearLayout>

            <FrameLayout
                android:id="@+id/confirm_square"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@android:color/transparent"
                android:padding="10dp">

                <Button
                    android:id="@+id/footer_update"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/radius_frame_gray"
                    android:scaleType="fitCenter"
                    android:text="@string/do_update"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

            </FrameLayout>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>
