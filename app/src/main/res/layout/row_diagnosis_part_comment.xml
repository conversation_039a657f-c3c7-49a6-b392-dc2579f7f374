<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:background="@null"
    android:minHeight="40dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@null">

            <LinearLayout
                android:id="@+id/for_spare_shutter"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="0.5"
                android:alpha=".7"
                android:background="@color/gray"
                android:gravity="center"
                android:orientation="horizontal" />

            <LinearLayout
                android:id="@+id/for_report_shutter"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="0.5"
                android:alpha=".7"
                android:background="@color/light_blue"
                android:gravity="center"
                android:orientation="horizontal" />

        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@null"
            android:minHeight="40dp">

            <TextView
                android:id="@+id/diagnosis_part_comment_text"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_gravity="center"
                android:background="@null"
                android:gravity="center"
                android:maxLines="2"
                android:textColor="@color/white"
                android:textSize="16sp"
                app:fontFamily="sans-serif-medium" />

        </FrameLayout>

        <LinearLayout
            android:id="@+id/bg_reserve"
            android:layout_width="30dp"
            android:layout_height="20dp"
            android:layout_gravity="left|top"
            android:alpha=".7"
            android:background="@color/black"
            android:orientation="horizontal" />

        <LinearLayout
            android:id="@+id/bg_report"
            android:layout_width="30dp"
            android:layout_height="20dp"
            android:layout_gravity="right|top"
            android:alpha=".7"
            android:background="@color/black"
            android:orientation="horizontal" />

        <TextView
            android:id="@+id/text_reserve_item"
            android:layout_width="30dp"
            android:layout_height="20dp"
            android:layout_gravity="left|top"
            android:gravity="center"
            android:textColor="@color/white"
            android:text="1"
            android:textSize="12dp" />

        <TextView
            android:id="@+id/text_report_item"
            android:layout_width="30dp"
            android:layout_height="20dp"
            android:layout_gravity="right|top"
            android:gravity="center"
            android:textColor="@color/white"
            android:text="1"
            android:textSize="12dp" />


    </FrameLayout>

</RelativeLayout>