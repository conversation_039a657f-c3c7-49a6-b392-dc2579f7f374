<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/workschedule_fragment_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <LinearLayout
        android:id="@+id/LayoutLine"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/LayoutHeader"
        android:background="@color/gray"
        android:orientation="vertical" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/search_area"
        android:layout_below="@id/LayoutLine">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/search_condition_label"
                    android:layout_width="fill_parent"
                    android:layout_height="38dp"
                    android:background="@color/contents_gray"
                    android:focusableInTouchMode="true"
                    android:gravity="center_vertical"
                    android:paddingStart="15dp"
                    android:paddingLeft="15dp"
                    android:paddingTop="5dp"
                    android:text="@string/search_condition"
                    android:textColor="@color/dark_gray"
                    android:textSize="16sp" />

                <LinearLayout
                    android:id="@+id/search_condition_line"
                    style="@style/vertical_line"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/task_name_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/task_name_column"
                    android:layout_width="match_parent"
                    android:layout_height="46dp"
                    android:background="@color/white">

                    <TextView
                        android:id="@+id/task_name_label"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="2"
                        android:gravity="center_vertical"
                        android:paddingStart="15dp"
                        android:paddingLeft="15dp"
                        android:text="@string/workschedule_task_name"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <EditText
                        android:id="@+id/task_name_input"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:background="@null"
                        android:gravity="end|center_vertical"
                        android:inputType="text"
                        android:paddingEnd="15dp"
                        android:paddingRight="15dp"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:layout_width="15dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="15dp"
                        android:layout_marginRight="15dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_pencil" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/task_name_line"
                    style="@style/vertical_line"
                    android:layout_width="match_parent"
                    android:orientation="horizontal">

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/task_work_no_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/task_work_no_column"
                    android:layout_width="match_parent"
                    android:layout_height="46dp"
                    android:background="@color/white">

                    <TextView
                        android:id="@+id/task_work_no_label"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:gravity="center_vertical"
                        android:paddingStart="15dp"
                        android:paddingLeft="15dp"
                        android:paddingTop="5dp"
                        android:text="@string/workschedule_task_work_no"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <EditText
                        android:id="@+id/task_work_no_input"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="7"
                        android:background="@null"
                        android:gravity="end|center_vertical"
                        android:inputType="number"
                        android:paddingRight="15dp"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:layout_width="15dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="15dp"
                        android:layout_marginRight="15dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_pencil" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/taskeorkno_line"
                    style="@style/vertical_line"
                    android:layout_width="match_parent"
                    android:orientation="horizontal">

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/construction_start1_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/construction_start1_column"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/white"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/construction_start1_label"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="2"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/workschedule_construction_start1"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <TextView
                        android:id="@+id/construction_start1_date"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:background="@drawable/ripple_white"
                        android:gravity="right|center_vertical"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:id="@+id/construction_start1_arrow"
                        android:layout_width="12dp"
                        android:layout_height="8dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_arrow02" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/construction_start1_line"
                    style="@style/vertical_line"
                    android:orientation="horizontal">

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/construction_start2_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/construction_start2_column"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/white"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/construction_start2_label"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="2"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/workschedule_construction_start2"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <TextView
                        android:id="@+id/construction_start2_date"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:background="@drawable/ripple_white"
                        android:gravity="right|center_vertical"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:id="@+id/construction_start2_arrow"
                        android:layout_width="12dp"
                        android:layout_height="8dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_arrow02" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/construction_start2_line"
                    style="@style/vertical_line"
                    android:orientation="horizontal">

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/construction_comp1_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/construction_comp1_column"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/white"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/construction_comp1_label"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="2"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/workschedule_construction_comp1"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <TextView
                        android:id="@+id/construction_comp1_date"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:background="@drawable/ripple_white"
                        android:gravity="right|center_vertical"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:id="@+id/construction_comp1_arrow"
                        android:layout_width="12dp"
                        android:layout_height="8dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_arrow02" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/construction_comp1_line"
                    style="@style/vertical_line"
                    android:orientation="horizontal">

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/construction_comp2_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/construction_comp2_column"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/white"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/construction_comp2_label"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="2"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/workschedule_construction_comp2"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <TextView
                        android:id="@+id/construction_comp2_date"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:background="@drawable/ripple_white"
                        android:gravity="right|center_vertical"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:id="@+id/construction_comp2_arrow"
                        android:layout_width="12dp"
                        android:layout_height="8dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_arrow02" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/construction_comp2_line"
                    style="@style/vertical_line"
                    android:orientation="horizontal">

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/construction_status_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/construction_status_column"
                    android:layout_width="match_parent"
                    android:layout_height="46dp"
                    android:background="@color/list">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_centerHorizontal="true"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:orientation="horizontal">

                            <CheckBox
                                android:id="@+id/not_started_checkbox"
                                style="@style/CustomCheckbox"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:width="40dp"
                                android:checked="true"
                                android:scaleX="1.5"
                                android:scaleY="1.5"
                                android:translationX="20dp" />

                            <TextView
                                android:id="@+id/not_started_label"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginLeft="5dp"
                                android:layout_marginRight="5dp"
                                android:gravity="center_vertical"
                                android:text="@string/workschedule_not_stated"
                                android:textColor="@color/light_blue"
                                android:textSize="18sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:orientation="horizontal">

                            <CheckBox
                                android:id="@+id/under_construction_checkbox"
                                style="@style/CustomCheckbox"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:width="40dp"
                                android:checked="true"
                                android:scaleX="1.5"
                                android:scaleY="1.5"
                                android:translationX="20dp" />

                            <TextView
                                android:id="@+id/under_construction_label"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginLeft="5dp"
                                android:layout_marginRight="5dp"
                                android:gravity="center_vertical"
                                android:text="@string/workschedule_under_construction"
                                android:textColor="@color/light_blue"
                                android:textSize="18sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:orientation="horizontal">

                            <CheckBox
                                android:id="@+id/completion_checkbox"
                                style="@style/CustomCheckbox"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:width="40dp"
                                android:checked="true"
                                android:scaleX="1.5"
                                android:scaleY="1.5"
                                android:translationX="20dp" />

                            <TextView
                                android:id="@+id/completion_label"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginLeft="5dp"
                                android:layout_marginRight="5dp"
                                android:gravity="center_vertical"
                                android:text="@string/workschedule_completion"
                                android:textColor="@color/light_blue"
                                android:textSize="18sp" />

                        </LinearLayout>
                    </LinearLayout>

                </RelativeLayout>

                <LinearLayout
                    android:id="@+id/construction_status_line"
                    style="@style/vertical_line"
                    android:layout_width="match_parent"
                    android:orientation="horizontal">

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/pref_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/pref_column"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/white"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/pref_label"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="2"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/workschedule_pref"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <TextView
                        android:id="@+id/pref_text"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:background="@drawable/ripple_white"
                        android:gravity="right|center_vertical"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:id="@+id/pref_arrow"
                        android:layout_width="12dp"
                        android:layout_height="8dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_arrow02" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/pref_line"
                    style="@style/vertical_line"
                    android:orientation="horizontal">

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/maker1_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/maker1_column"
                    android:layout_width="match_parent"
                    android:layout_height="46dp"
                    android:background="@color/white">

                    <TextView
                        android:id="@+id/maker1_label"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="2"
                        android:gravity="center_vertical"
                        android:paddingStart="15dp"
                        android:paddingLeft="15dp"
                        android:text="@string/workschedule_maker_name1"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <EditText
                        android:id="@+id/maker1_input"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:background="@null"
                        android:gravity="end|center_vertical"
                        android:inputType="text"
                        android:paddingEnd="15dp"
                        android:paddingRight="15dp"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:layout_width="15dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="15dp"
                        android:layout_marginRight="15dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_pencil" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/maker1_line"
                    style="@style/vertical_line"
                    android:layout_width="match_parent"
                    android:orientation="horizontal">

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/maker2_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/maker2_column"
                    android:layout_width="match_parent"
                    android:layout_height="46dp"
                    android:background="@color/white">

                    <TextView
                        android:id="@+id/maker2_label"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="2"
                        android:gravity="center_vertical"
                        android:paddingStart="15dp"
                        android:paddingLeft="15dp"
                        android:text="@string/workschedule_maker_name2"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <EditText
                        android:id="@+id/maker2_input"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:background="@null"
                        android:gravity="end|center_vertical"
                        android:inputType="text"
                        android:paddingEnd="15dp"
                        android:paddingRight="15dp"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:layout_width="15dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="15dp"
                        android:layout_marginRight="15dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_pencil" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/maker2_line"
                    style="@style/vertical_line"
                    android:layout_width="match_parent"
                    android:orientation="horizontal">

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/fct_user_name_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/fct_user_name_column"
                    android:layout_width="match_parent"
                    android:layout_height="46dp"
                    android:background="@color/white">

                    <TextView
                        android:id="@+id/fct_user_name_label"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="2"
                        android:gravity="center_vertical"
                        android:paddingStart="15dp"
                        android:paddingLeft="15dp"
                        android:text="@string/workschedule_fct_user_name"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <EditText
                        android:id="@+id/fct_user_name_input"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:background="@null"
                        android:gravity="end|center_vertical"
                        android:inputType="text"
                        android:paddingEnd="15dp"
                        android:paddingRight="15dp"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:layout_width="15dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="15dp"
                        android:layout_marginRight="15dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_pencil" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/fct_user_name_line"
                    style="@style/vertical_line"
                    android:layout_width="match_parent"
                    android:orientation="horizontal">

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/construction_company_name_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/construction_company_name_column"
                    android:layout_width="match_parent"
                    android:layout_height="46dp"
                    android:background="@color/white">

                    <TextView
                        android:id="@+id/construction_company_name_label"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="2"
                        android:gravity="center_vertical"
                        android:paddingStart="15dp"
                        android:paddingLeft="15dp"
                        android:text="@string/workschedule_construction_company_name"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />

                    <EditText
                        android:id="@+id/construction_company_name_input"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:background="@null"
                        android:gravity="end|center_vertical"
                        android:inputType="text"
                        android:paddingEnd="15dp"
                        android:paddingRight="15dp"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <ImageView
                        android:layout_width="15dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="15dp"
                        android:layout_marginRight="15dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_pencil" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/construction_company_name_line"
                    style="@style/vertical_line"
                    android:layout_width="match_parent"
                    android:orientation="horizontal">

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </ScrollView>

    <LinearLayout
        android:id="@+id/search_area"
        android:layout_width="match_parent"
        android:layout_height="61dp"
        android:layout_alignParentBottom="true"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="1dp"
            android:background="@color/gray"
            android:orientation="vertical" />

        <FrameLayout
            android:id="@+id/search_layout"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@color/white"
            android:padding="10dp">

            <TextView
                android:id="@+id/search_button"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/search_button"
                android:gravity="center"
                android:text="@string/search"
                android:textColor="@color/white"
                android:textSize="18sp" />
        </FrameLayout>

    </LinearLayout>

    <View
        android:id="@+id/transparent_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@null"
        android:visibility="gone" />

</RelativeLayout>