<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="10dp"
    android:paddingTop="10dp"
    android:background="@color/contents_gray"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/message_send_time"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:paddingEnd="5dp"
        android:paddingBottom="5dp"
        android:gravity="end|bottom"
        android:textColor="@color/dark_gray"/>

    <TextView
        android:id="@+id/message_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="3"
        android:padding="15dp"
        android:layout_marginEnd="10dp"
        android:background="@drawable/message_send"
        android:gravity="center_vertical"
        android:textColor="@color/black"
        android:textSize="14sp"
        android:lineSpacingMultiplier="1.2"/>

</LinearLayout>