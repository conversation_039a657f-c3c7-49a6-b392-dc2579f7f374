<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/hc_layoutContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:background="?selectableItemBackground"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@+id/hc_selector"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="spread_inside">

        <TextView
            android:id="@+id/hc_text_bottom"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="8sp"
            tools:text="Mon" />

        <LinearLayout
            android:id="@+id/head_background"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            >

            <TextView
                android:id="@+id/hc_text_top"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textSize="10sp"
                android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                tools:text="Jan" />

            <TextView
                android:id="@+id/hc_text_middle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:textAppearance="@style/TextAppearance.AppCompat.Headline"
                android:textSize="14sp"
                tools:text="2" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/hc_events_recyclerView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="4dp"
            android:gravity="center_horizontal" />

    </LinearLayout>

    <View
        android:id="@+id/hc_selector"
        android:layout_width="0dp"
        android:layout_height="5dp"
        android:layout_marginTop="5dp"
        android:background="#f00f0f"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/hc_layoutContent" />

</androidx.constraintlayout.widget.ConstraintLayout>

