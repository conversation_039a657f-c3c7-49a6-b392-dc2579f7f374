<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="@drawable/ripple_white"
    android:descendantFocusability="blocksDescendants"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/extension_image"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="9dp"
        android:layout_marginRight="9dp"
        android:scaleType="fitCenter" />

    <TextView
        android:id="@+id/file_name"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingStart="20dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:fontFamily="sans-serif-light"
        tools:ignore="RtlSymmetry" />

    <ImageView
        android:id="@+id/download_image"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="9dp"
        android:layout_marginRight="9dp"
        android:scaleType="fitCenter"
        android:src="@drawable/file_list_download" />

</LinearLayout>