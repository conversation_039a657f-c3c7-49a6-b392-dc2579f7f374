<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="10dp"
    android:paddingTop="10dp"
    android:gravity="center"
    android:background="@color/contents_gray"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/message_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="3dp"
        android:paddingBottom="3dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:gravity="center"
        android:background="@drawable/message_date"
        android:textSize="14sp"
        android:textColor="@color/white"/>

</LinearLayout>