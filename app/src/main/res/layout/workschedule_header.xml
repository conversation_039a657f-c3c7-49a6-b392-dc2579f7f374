<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="45dp">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="@drawable/com_header"
        android:theme="@style/AppTheme.AppBarOverlay"
        app:contentInsetStart="0dp">

        <TextView
            android:id="@+id/toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/workschedule_title"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/search_workschedule"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_gravity="right"
            android:layout_marginEnd="15dp"
            android:layout_marginRight="15dp"
            android:gravity="center_vertical"
            android:text="@string/workschedule_toolbar_search"
            android:textColor="@color/white"
            android:textSize="16sp" />

    </androidx.appcompat.widget.Toolbar>


</RelativeLayout>