<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:scrollbarStyle="insideOverlay"
    tools:context="fois.dailyreportsystem.activity.MainActivity"
    tools:openDrawer="start">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/header"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/header_contents"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@color/white">

                <LinearLayout
                    android:id="@+id/main_contents"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal" />

            </LinearLayout>

            <RelativeLayout
                android:id="@+id/LayoutMain"
                android:layout_width="match_parent"
                android:layout_height="50dp">

                <LinearLayout
                    android:id="@+id/main_footer_menu"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@drawable/com_footer_btbk"
                    android:orientation="horizontal">

                    <include layout="@layout/main_footer"/>

                </LinearLayout>

            </RelativeLayout>
        </LinearLayout>
    </FrameLayout>

    <FrameLayout
        android:id="@+id/select_prefs"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:background="@color/white"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/drawer_layout"
        android:layout_width="240dp"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/white"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/drawer_contents"
            android:layout_width="240dp"
            android:layout_height="match_parent"
            android:orientation="horizontal" />

    </LinearLayout>

</androidx.drawerlayout.widget.DrawerLayout>
