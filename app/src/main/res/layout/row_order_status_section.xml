<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="fill_parent"
    android:layout_height="35dp"
    android:descendantFocusability="blocksDescendants"
    android:focusable="true"
    android:minHeight="35dp"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/section"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/contents_background"
        android:gravity="bottom"
        android:paddingStart="20dp"
        android:paddingBottom="4dp"
        android:textColor="@color/dark_gray"
        android:textSize="15sp"
        app:fontFamily="sans-serif"
        tools:ignore="RtlSymmetry" />


</LinearLayout>