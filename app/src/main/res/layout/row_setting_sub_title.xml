<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/setting_company_name"
    android:layout_width="fill_parent"
    android:layout_height="38dp"
    android:background="@color/contents_gray"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/setting_sub_title"
        android:layout_width="fill_parent"
        android:layout_height="match_parent"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:minHeight="38dp"
        android:paddingLeft="15dp"
        android:paddingTop="5dp"
        android:textColor="@color/dark_gray"
        android:textSize="16sp" />

</LinearLayout>