<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="@drawable/ripple_white"
    android:descendantFocusability="blocksDescendants"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/user_name"
            android:layout_width="match_parent"
            android:layout_height="29dp"
            android:gravity="bottom"
            android:paddingStart="20dp"
            android:paddingLeft="15dp"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:fontFamily="sans-serif-light"
            tools:ignore="RtlSymmetry" />

        <TextView
            android:id="@+id/company_name"
            android:layout_width="match_parent"
            android:layout_height="21dp"
            android:gravity="top"
            android:paddingStart="20dp"
            android:paddingLeft="15dp"
            android:textColor="@color/gray"
            android:textSize="10sp"
            app:fontFamily="sans-serif-light"
            tools:ignore="RtlSymmetry" />
    </LinearLayout>

    <ImageView
        android:id="@+id/check_image"
        android:layout_width="50dp"
        android:layout_height="match_parent"
        android:paddingTop="23dp"
        android:paddingBottom="23dp"
        android:scaleType="center"
        android:src="@drawable/blue_check"
        tools:visibility="gone" />

</LinearLayout>