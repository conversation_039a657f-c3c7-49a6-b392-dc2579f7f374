<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_layout"
    android:layout_width="wrap_content"
    android:layout_height="fill_parent"
    android:background="@color/contents_gray"
    android:scrollbarStyle="insideOverlay"
    tools:context="fois.dailyreportsystem.activity.survey.SurveyPropertyActivity">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:orientation="horizontal">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay"
            app:contentInsetStart="0dp">

            <ImageView
                android:id="@+id/back_arrow"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_centerVertical="true"
                android:layout_gravity="left|center"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:src="@drawable/white_arrow_l"
                tools:ignore="RtlHardcoded" />

            <TextView
                android:id="@+id/survey_property_toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                app:fontFamily="sans-serif-medium" />

        </androidx.appcompat.widget.Toolbar>

    </RelativeLayout>

    <ScrollView
        android:id="@+id/scroll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/LayoutHeader">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/LayoutMain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:orientation="vertical"
        android:paddingBottom="50dp">

        <View style="@style/vertical_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/white"
            android:orientation="horizontal"
            android:weightSum="3"
            android:baselineAligned="false">

            <TextView
                android:id="@+id/textView5"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:paddingStart="10dp"
                android:paddingLeft="10dp"
                android:text="@string/survey_rotate_direction"
                android:textColor="@color/font_color_blue"
                android:textSize="17sp"
                app:fontFamily="sans-serif" />

            <LinearLayout
                android:id="@+id/check_left"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:weightSum="5">

                <ImageView
                    android:id="@+id/left_check_image"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1.7"
                    android:scaleType="center" />

                <TextView
                    android:id="@+id/left_text"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1.8"
                    android:gravity="center"
                    android:text="@string/shooting_left"
                    app:fontFamily="sans-serif-light" />

                <ImageView
                    android:id="@+id/left_icon"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1.5"
                    android:scaleType="center" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/check_right"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:weightSum="5">

                <ImageView
                    android:id="@+id/right_check_image"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1.7"
                    android:scaleType="center" />

                <TextView
                    android:id="@+id/right_text"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1.8"
                    android:gravity="center"
                    android:text="@string/shooting_right"
                    app:fontFamily="sans-serif-light" />

                <ImageView
                    android:id="@+id/right_icon"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1.5"
                    android:scaleType="center" />
            </LinearLayout>

        </LinearLayout>

        <View style="@style/vertical_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/white">

            <LinearLayout
                android:id="@+id/image_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/image_list_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginRight="10dp"
                    android:scaleType="center" />

                <TextView
                    android:id="@+id/image_list_link"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:text="@string/shooting_image_list"
                    android:textColor="@color/font_color_blue"
                    android:textSize="18sp"
                    app:fontFamily="sans-serif" />

            </LinearLayout>

        </LinearLayout>

        <View style="@style/vertical_line" />

        <TextView
            style="@style/section_sub_title"
            android:text="@string/survey_info" />

        <View style="@style/vertical_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_weight="1"
            android:background="@color/white">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight=".3"
                android:gravity="center_vertical"
                android:paddingStart="10dp"
                android:paddingLeft="10dp"
                android:text="@string/survey_staff"
                android:textColor="@color/font_color_blue"
                android:textSize="17sp"
                app:fontFamily="sans-serif" />

            <TextView
                android:id="@+id/survey_staff"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight=".7"
                android:gravity="right|center_vertical"
                android:paddingEnd="10dp"
                android:paddingRight="10dp"
                android:textColor="@color/black"
                android:textSize="17sp"
                app:fontFamily="sans-serif-light" />

        </LinearLayout>

        <View style="@style/vertical_line" />

        <TextView
            style="@style/section_sub_title"
            android:text="@string/request_info" />

        <View style="@style/vertical_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_weight="1"
            android:background="@color/white">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight=".3"
                android:gravity="center_vertical"
                android:paddingStart="10dp"
                android:paddingLeft="10dp"
                android:text="@string/request_company"
                android:textColor="@color/font_color_blue"
                android:textSize="17sp"
                app:fontFamily="sans-serif" />

            <TextView
                android:id="@+id/request_company"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight=".7"
                android:gravity="right|center_vertical"
                android:paddingEnd="10dp"
                android:paddingRight="10dp"
                android:textColor="@color/black"
                android:textSize="17sp"
                app:fontFamily="sans-serif-light" />
        </LinearLayout>

        <View style="@style/vertical_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_weight="1"
            android:background="@color/white">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight=".3"
                android:gravity="center_vertical"
                android:paddingStart="10dp"
                android:paddingLeft="10dp"
                android:text="@string/request_staff"
                android:textColor="@color/font_color_blue"
                android:textSize="17sp"
                app:fontFamily="sans-serif" />

            <TextView
                android:id="@+id/request_staff"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight=".7"
                android:gravity="right|center_vertical"
                android:paddingEnd="10dp"
                android:paddingRight="10dp"
                android:textColor="@color/black"
                android:textSize="17sp"
                app:fontFamily="sans-serif-light" />

        </LinearLayout>

        <View style="@style/vertical_line" />
        <TextView
            style="@style/section_sub_title"
            android:text="@string/request_detail" />

        <View style="@style/vertical_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_weight="1"
            android:background="@color/white">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight=".3"
                android:gravity="center_vertical"
                android:paddingStart="10dp"
                android:paddingLeft="10dp"
                android:text="@string/survey_date"
                android:textColor="@color/font_color_blue"
                android:textSize="17sp"
                app:fontFamily="sans-serif" />

            <TextView
                android:id="@+id/survey_date"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight=".7"
                android:gravity="right|center_vertical"
                android:paddingEnd="10dp"
                android:paddingRight="10dp"
                android:textColor="@color/black"
                android:textSize="17sp"
                app:fontFamily="sans-serif-light" />


        </LinearLayout>
        <View style="@style/vertical_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_weight="1"
            android:background="@color/white">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight=".3"
                android:gravity="center_vertical"
                android:paddingStart="10dp"
                android:paddingLeft="10dp"
                android:text="@string/survey_area"
                android:textColor="@color/font_color_blue"
                android:textSize="17sp"
                app:fontFamily="sans-serif" />

            <TextView
                android:id="@+id/survey_area"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight=".7"
                android:gravity="right|center_vertical"
                android:paddingEnd="10dp"
                android:paddingRight="10dp"
                android:textColor="@color/black"
                android:textSize="17sp"
                app:fontFamily="sans-serif-light" />

        </LinearLayout>

        <View style="@style/vertical_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_weight="1"
            android:background="@color/white">

            <TextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight=".3"
                android:gravity="center_vertical"
                android:paddingStart="10dp"
                android:paddingLeft="10dp"
                android:text="@string/survey_address"
                android:textColor="@color/font_color_blue"
                android:textSize="17sp"
                app:fontFamily="sans-serif" />

            <TextView
                android:id="@+id/survey_address"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight=".7"
                android:gravity="right|center_vertical"
                android:paddingEnd="10dp"
                android:paddingRight="10dp"
                android:textColor="@color/black"
                android:textSize="17sp"
                app:fontFamily="sans-serif-light" />

        </LinearLayout>

        <View style="@style/vertical_line" />

    </LinearLayout>

        </RelativeLayout>
    </ScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center|bottom"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/photo_detail_footer"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@drawable/com_footer_btbk"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/photo_detail_footer_gallery"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/photo_detail_footer_gallery_icon"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_gravity="center_vertical"
                    android:scaleType="fitCenter"
                    android:src="@drawable/album" />

                <TextView
                    android:id="@+id/photo_detail_footer_gallery_text"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="6dp"
                    android:gravity="center"
                    android:text="@string/photo_detail_gallery_text"
                    android:textColor="@color/white" />

            </LinearLayout>

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/white" />

            <LinearLayout
                android:id="@+id/photo_detail_footer_camera"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/photo_detail_footer_camera_icon"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_gravity="center_vertical"
                    android:scaleType="fitCenter"
                    android:src="@drawable/camera_white" />

                <TextView
                    android:id="@+id/photo_detail_footer_camera_text"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="6dp"
                    android:gravity="center"
                    android:text="@string/shooting_start"
                    android:textColor="@color/white" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:layout_width="fill_parent"
            android:layout_height="1dp"
            android:background="@color/gray" />


    </LinearLayout>

</RelativeLayout>
