<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/plan_regist_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/contents_gray"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    tools:context="fois.dailyreportsystem.activity.plan.PlanRegistrationActivity">

    <FrameLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay"
            app:contentInsetStart="0dp">

            <TextView
                android:id="@+id/add_cancel"
                android:layout_width="100dp"
                android:layout_height="match_parent"
                android:layout_gravity="left"
                android:layout_marginEnd="15dp"
                android:layout_marginRight="15dp"
                android:gravity="center"
                android:paddingStart="10dp"
                android:paddingLeft="10dp"
                android:text="@string/cancel"
                android:textColor="@color/orange_link"
                android:textSize="16sp"
                tools:ignore="RtlSymmetry" />

            <TextView
                android:id="@+id/survey_property_toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_marginBottom="0dp"
                android:gravity="center"
                android:text="@string/plan_new_plan"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/add_text"
                android:layout_width="60dp"
                android:layout_height="match_parent"
                android:layout_alignParentEnd="true"
                android:layout_gravity="right|end"
                android:layout_marginEnd="15dp"
                android:gravity="center|right"
                android:paddingEnd="10dp"
                android:text="@string/add"
                android:textColor="@color/half_transparent"
                android:textSize="16sp"
                tools:ignore="RtlHardcoded,RtlSymmetry" />

        </androidx.appcompat.widget.Toolbar>



    </FrameLayout>

    <ScrollView
        android:id="@+id/scroll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/footer"
        android:layout_below="@+id/header">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/contents"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:background="@color/contents_gray">

            </FrameLayout>

            <LinearLayout
                android:id="@+id/plan_info_line1"
                style="@style/vertical_line"
                android:orientation="horizontal">

            </LinearLayout>

            <EditText
                android:id="@+id/plan_title_edit_text"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/white"
                android:hint="@string/plan_regist_title_placeholder"
                android:inputType="textPersonName"
                android:paddingStart="15dp"
                android:paddingLeft="15dp"
                tools:ignore="RtlSymmetry" />

            <LinearLayout
                android:id="@+id/plan_info_line2"
                style="@style/vertical_line"
                android:orientation="horizontal" />

            <EditText
                android:id="@+id/plan_place_edit_text"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:autofillHints=""
                android:background="@color/white"
                android:hint="@string/plan_regist_place_placeholder"
                android:inputType="textPersonName"
                android:paddingStart="15dp"
                android:paddingLeft="15dp"
                tools:ignore="RtlSymmetry" />

            <LinearLayout
                android:id="@+id/plan_info_line3"
                style="@style/vertical_line"
                android:orientation="horizontal" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">


            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:background="@color/contents_gray" />

            <LinearLayout
                android:id="@+id/allday_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/plan_info_line4"
                    style="@style/vertical_line"
                    android:orientation="horizontal">

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/allday_column"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/white"
                    android:orientation="horizontal">

                    <Switch
                        android:id="@+id/end_switch"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:paddingRight="15dp"
                        android:text="@string/plan_regist_end_date"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/start_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/plan_info_line5"
                    style="@style/vertical_line"
                    android:orientation="horizontal">

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/start_column"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/white"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/start_label"
                        android:layout_width="60dp"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/plan_regist_start"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp"
                        app:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/start_date"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/ripple_white"
                        android:gravity="right|center_vertical"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        app:fontFamily="sans-serif-light"
                        tools:ignore="InefficientWeight,RtlHardcoded" />

                    <ImageView
                        android:id="@+id/loop_label_right_arrow4"
                        android:layout_width="12dp"
                        android:layout_height="8dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_arrow02" />

                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/end_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/plan_info_line6"
                    style="@style/vertical_line"
                    android:orientation="horizontal">

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/end_column"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/white"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/end_label"
                        android:layout_width="60dp"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/plan_regist_end"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp"
                        app:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/end_date"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/ripple_white"
                        android:gravity="right|center_vertical"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:visibility="visible"
                        app:fontFamily="sans-serif-light" />

                    <ImageView
                        android:id="@+id/loop_label_right_arrow3"
                        android:layout_width="12dp"
                        android:layout_height="8dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_arrow02" />

                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/loop_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/plan_info_line7"
                    style="@style/vertical_line"
                    android:orientation="horizontal">

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/loop_column"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/white"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/loop_label"
                        android:layout_width="100dp"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/plan_regist_loop"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp"
                        app:fontFamily="sans-serif"
                        android:paddingStart="15dp" />

                    <TextView
                        android:id="@+id/loop_select_label"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/ripple_white"
                        android:gravity="right|center_vertical"
                        android:text="しない"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        app:fontFamily="sans-serif-light" />

                    <ImageView
                        android:id="@+id/loop_label_right_arrow"
                        android:layout_width="12dp"
                        android:layout_height="8dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_arrow02" />

                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/end_loop_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:id="@+id/plan_info_line9"
                    style="@style/vertical_line"
                    android:orientation="horizontal">

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/end_loop_column"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/white"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/end_loop_label"
                        android:layout_width="150dp"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:paddingLeft="15dp"
                        android:text="@string/plan_regist_loop_end"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp"
                        app:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/end_loop_select_label"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/ripple_white"
                        android:gravity="right|center_vertical"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        app:fontFamily="sans-serif-light" />

                    <ImageView
                        android:id="@+id/loop_label_right_arrow6"
                        android:layout_width="12dp"
                        android:layout_height="8dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_arrow02" />


                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/travel_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/plan_info_line8"
                    style="@style/vertical_line"
                    android:orientation="horizontal">

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/travel_column"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/white"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/move_label"
                        android:layout_width="100dp"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:paddingLeft="10dp"
                        android:paddingRight="10dp"
                        android:text="@string/plan_regist_move_hour"
                        android:textColor="@color/light_blue"
                        android:textSize="18sp"
                        app:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/move_select_label"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/ripple_white"
                        android:gravity="right|center_vertical"
                        android:text="なし"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        app:fontFamily="sans-serif-light" />

                    <ImageView
                        android:id="@+id/loop_label_right_arrow2"
                        android:layout_width="12dp"
                        android:layout_height="8dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/com_arrow02" />


                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/plan_info_line13"
                style="@style/vertical_line"
                android:orientation="horizontal">

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:background="@color/contents_gray">

            </FrameLayout>

            <LinearLayout
                android:id="@+id/plan_info_line11"
                style="@style/vertical_line"
                android:orientation="horizontal">

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/plan_memo_multi_text"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_weight="3"
                    android:background="@color/white"
                    android:ems="10"
                    android:gravity="start|top"
                    android:hint="@string/plan_regist_memo_placeholder"
                    android:inputType="textMultiLine"
                    android:paddingLeft="15dp"
                    android:paddingTop="5dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="5dp"
                    app:fontFamily="sans-serif-light" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/plan_info_line12"
                style="@style/vertical_line"
                android:orientation="horizontal">

            </LinearLayout>

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:background="@color/contents_gray">

            </FrameLayout>

        </LinearLayout>

    </LinearLayout>

        </RelativeLayout>

    </ScrollView>

    <LinearLayout
        android:id="@+id/footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:background="@android:color/transparent"
        android:gravity="center">

        <LinearLayout
            android:id="@+id/confirm_layout"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@android:color/transparent"
            android:orientation="vertical"
            android:visibility="visible">

            <LinearLayout
                android:id="@+id/confirm_line"
                style="@style/vertical_line_dark"
                android:orientation="horizontal">

            </LinearLayout>

            <FrameLayout
                android:id="@+id/confirm_square"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@android:color/transparent"
                android:padding="10dp">

                <Button
                    android:id="@+id/do_add_button"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/radius_frame_gray"
                    android:scaleType="fitCenter"
                    android:text="@string/do_registration"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

            </FrameLayout>

        </LinearLayout>

    </LinearLayout>
</RelativeLayout>