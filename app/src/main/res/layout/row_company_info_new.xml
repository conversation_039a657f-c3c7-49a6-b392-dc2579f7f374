<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/setting_company_name"
    android:layout_width="fill_parent"
    android:layout_height="46dp"
    android:background="@color/list"
    android:gravity="center_horizontal"
    android:minHeight="46dp"
    android:orientation="horizontal">

    <ImageView
        android:layout_width="20dp"
        android:layout_height="match_parent"
        android:scaleType="fitCenter"
        android:src="@drawable/com_plus"/>

    <TextView
        android:id="@+id/company_number_new"
        android:layout_width="wrap_content"
        android:layout_height="46dp"
        android:layout_marginStart="10dp"
        android:gravity="center"
        android:text="@string/company_number_new"
        android:textColor="@color/light_blue"
        android:textSize="18sp"/>

</LinearLayout>