<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/white"
    android:orientation="vertical">

    <!-- date toolbar -->

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/calendarHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible"
        android:background="@color/defaultColor">

        <ImageButton
            android:id="@+id/previousButton"
            style="@style/Widget.AppCompat.Button.Borderless.Colored"
            android:layout_width="85dp"
            android:layout_height="0dp"
            android:src="@drawable/ic_arrow_left"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageButton
            android:id="@+id/forwardButton"
            style="@style/Widget.AppCompat.Button.Borderless.Colored"
            android:layout_width="85dp"
            android:layout_height="0dp"
            android:src="@drawable/ic_arrow_right"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/currentDateLabel"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:gravity="center"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            app:layout_constraintLeft_toRightOf="@id/previousButton"
            app:layout_constraintRight_toLeftOf="@id/forwardButton"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- eventDays header -->

    <LinearLayout
        android:id="@+id/abbreviationsBar"
        android:layout_width="match_parent"
        android:layout_height="25dp"
        android:background="@color/calendar_background"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/mondayLabel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="@string/material_calendar_sunday"
            android:textColor="@color/red" />

        <TextView
            android:id="@+id/tuesdayLabel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="@string/material_calendar_monday"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/wednesdayLabel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="@string/material_calendar_tuesday"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/thursdayLabel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="@string/material_calendar_wednesday"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/fridayLabel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="@string/material_calendar_thursday"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/saturdayLabel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="@string/material_calendar_friday"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/sundayLabel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="@string/material_calendar_saturday"
            android:textColor="@color/blue" />
    </LinearLayout>

    <View
        android:id="@+id/line"
        style="@style/vertical_line" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <fois.dailyreportsystem.util.calendarview.extensions.CalendarViewPager
            android:id="@+id/calendarViewPager"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </ScrollView>

</LinearLayout>