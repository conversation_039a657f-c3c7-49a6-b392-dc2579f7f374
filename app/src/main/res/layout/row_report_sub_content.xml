<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/report_sub_content_area"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:background="@color/list"
    android:orientation="horizontal"
    android:descendantFocusability="blocksDescendants">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/report_sub_content_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="30dp"
            android:paddingTop="15dp"
            android:paddingBottom="15dp"
            android:paddingStart="15dp"
            android:paddingEnd="15dp"
            android:textSize="18sp"
            android:textColor="@color/light_blue"/>

        <LinearLayout
            android:id="@+id/report_used_amount"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginEnd="25dp"
            android:paddingBottom="15dp"
            android:orientation="horizontal"
            android:visibility="visible">

            <LinearLayout
                android:id="@+id/report_used_large_area"
                android:layout_width="0dp"
                android:layout_height="35dp"
                android:layout_weight="1"
                android:layout_marginStart="15dp"
                android:background="@drawable/report_used_amount_rectangle"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="50dp"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="@string/report_used_amount_large"
                    android:textSize="16sp"
                    android:textColor="@color/dark_gray"/>

                <TextView
                    android:id="@+id/report_used_large_select"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:layout_marginEnd="15dp"
                    android:gravity="end|center_vertical"
                    android:textSize="18sp"
                    android:textColor="@color/black"/>

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="10dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/com_arrow02"/>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/report_used_small_area"
                android:layout_width="0dp"
                android:layout_height="35dp"
                android:layout_weight="1"
                android:layout_marginStart="15dp"
                android:background="@drawable/report_used_amount_rectangle"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="50dp"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="@string/report_used_amount_small"
                    android:textSize="16sp"
                    android:textColor="@color/dark_gray"/>

                <TextView
                    android:id="@+id/report_used_small_select"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:layout_marginEnd="15dp"
                    android:gravity="end|center_vertical"
                    android:textSize="18sp"
                    android:textColor="@color/black"/>

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="10dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/com_arrow02"/>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <CheckBox
        android:id="@+id/report_sub_content_check"
        style="@style/CustomCheckbox"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingEnd="15dp"
        android:scaleX="1.5"
        android:scaleY="1.5"/>

</LinearLayout>