<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:focusable="true"
    android:focusableInTouchMode="true"
    tools:context="fois.dailyreportsystem.activity.message.MessageListActivity">

    <RelativeLayout
        android:id="@+id/LayoutHeader"
        android:layout_width="fill_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:orientation="horizontal" >

        <androidx.appcompat.widget.Toolbar
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:maxLines="1"
                android:maxEms="10"
                android:ellipsize="end"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"/>

        </androidx.appcompat.widget.Toolbar>

        <ImageView
            android:id="@+id/back_arrow"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:paddingBottom="10dp"
            android:paddingTop="10dp"
            android:src="@drawable/com_headerarrow"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"/>

    </RelativeLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/LayoutHeader"
        android:background="@color/gray" />

    <ListView
        android:id="@+id/message_detail_listview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/divider"
        android:layout_above="@+id/divider2"
        android:background="@color/contents_gray"
        android:divider="@null"
        android:dividerHeight="1px" />

    <ImageView
        android:id="@+id/scroll_arrow"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_above="@+id/divider2"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="10dp"
        android:src="@drawable/pagebottom"
        android:scaleType="fitCenter" />

    <View
        android:id="@+id/divider2"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_above="@+id/message"
        android:background="@color/gray" />

    <LinearLayout
        android:id="@+id/message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:padding="10dp"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/message_input"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:minHeight="40dp"
            android:maxHeight="100dp"
            android:padding="10dp"
            android:layout_marginEnd="5dp"
            android:layout_weight="1"
            android:background="@drawable/message_input_area"
            android:hint="@string/message_hint"
            android:inputType="textMultiLine"
            android:textSize="14sp"
            android:textColor="@color/black"
            android:lineSpacingMultiplier="1.2"/>

        <ImageView
            android:id="@+id/message_send_button"
            android:layout_width="60dp"
            android:layout_height="40dp"
            android:layout_gravity="bottom"
            android:src="@drawable/send_off"
            android:scaleType="fitCenter"/>

    </LinearLayout>

</RelativeLayout>