<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/order_registration_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/contents_background"
    tools:context="fois.dailyreportsystem.activity.request.OrderDetailActivity">

    <FrameLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@drawable/com_header"
            android:theme="@style/AppTheme.AppBarOverlay">

            <TextView
                android:id="@+id/plan_header_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/research_person_back_title"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/order_edit"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="end|right"
                android:gravity="center"
                android:paddingTop="5dp"
                android:paddingRight="10dp"
                android:paddingBottom="5dp"
                android:text="@string/edit"
                android:textColor="@color/half_transparent"
                android:textSize="16sp"
                android:visibility="gone"
                tools:ignore="RtlHardcoded,RtlSymmetry" />

        </androidx.appcompat.widget.Toolbar>

        <ImageView
            android:id="@+id/back_title"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:src="@drawable/white_arrow_l"
            tools:ignore="ContentDescription" />

    </FrameLayout>

    <ScrollView
        android:id="@+id/scroll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/footer"
        android:layout_below="@+id/header">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/contents"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                tools:ignore="UselessParent">

                <LinearLayout
                    android:id="@+id/survey_statsu_layout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/white"
                    android:orientation="horizontal">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="35dp"
                        android:background="@color/contents_background">

                        <TextView
                            android:id="@+id/survey_status"
                            android:layout_width="90dp"
                            android:layout_height="match_parent"
                            android:layout_alignParentEnd="true"
                            android:layout_alignParentRight="true"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:layout_marginTop="5dp"
                            android:layout_marginEnd="10dp"
                            android:layout_marginRight="10dp"
                            android:layout_marginBottom="5dp"
                            android:background="@android:color/transparent"
                            android:gravity="center"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            app:fontFamily="sans-serif-light" />

                    </RelativeLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/survey_info_layout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/survey_info_title"
                        style="@style/section_sub_title"
                        android:layout_width="match_parent"
                        android:layout_height="20dp"
                        android:text="@string/survey_info"
                        app:fontFamily="sans-serif-light" />

                    <LinearLayout
                        android:id="@+id/survey_user_info_line1"
                        style="@style/vertical_line"
                        android:orientation="horizontal"/>

                    <LinearLayout
                        android:id="@+id/survey_user_column"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <RelativeLayout
                            android:id="@+id/no_choice"
                            android:layout_width="match_parent"
                            android:layout_height="45dp"
                            android:clickable="true"
                            android:focusable="true">

                            <ImageView
                                android:id="@+id/survey_user_arrow"
                                android:layout_width="45dp"
                                android:layout_height="45dp"
                                android:layout_alignParentTop="true"
                                android:layout_alignParentEnd="true"
                                android:layout_alignParentRight="true"
                                android:layout_alignParentBottom="true"
                                android:paddingTop="16.5dp"
                                android:paddingBottom="16.5dp"
                                app:srcCompat="@drawable/com_arrow01" />

                            <TextView
                                android:id="@+id/survey_user_title"
                                android:layout_width="60dp"
                                android:layout_height="match_parent"
                                android:layout_alignParentStart="true"
                                android:layout_alignParentLeft="true"
                                android:layout_alignParentTop="true"

                                android:layout_alignParentBottom="true"
                                android:layout_marginStart="15dp"

                                android:layout_marginLeft="15dp"
                                android:gravity="center_vertical"
                                android:text="@string/order_user"
                                android:textColor="@color/light_blue"
                                android:textSize="18sp"
                                android:visibility="gone"
                                app:fontFamily="sans-serif" />

                            <TextView
                                android:id="@+id/survey_user_name"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_alignParentTop="true"
                                android:layout_alignParentBottom="true"
                                android:layout_toLeftOf="@id/survey_user_arrow"
                                android:layout_toRightOf="@id/survey_user_title"
                                android:gravity="center_vertical|right|end"
                                android:textColor="@color/black"
                                android:textSize="18sp"
                                android:visibility="gone"
                                app:fontFamily="sans-serif-light"
                                tools:ignore="RtlHardcoded,RtlSymmetry" />

                            <TextView
                                android:id="@+id/do_survey_staff_list"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_centerInParent="true"
                                android:layout_gravity="center_vertical"
                                android:gravity="center"
                                android:text="@string/order_do_staff_list"
                                android:textColor="@color/light_blue"
                                android:textSize="18sp"
                                android:visibility="gone"
                                app:fontFamily="sans-serif" />

                        </RelativeLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/survey_user_info_line2"
                        style="@style/vertical_line"
                        android:orientation="horizontal"/>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/order_user_info_layout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/order_user_info_title"
                        style="@style/section_sub_title"
                        android:layout_height="30dp"
                        android:text="@string/order_info"
                        app:fontFamily="sans-serif-light" />

                    <LinearLayout
                        android:id="@+id/order_user_info_line3"
                        style="@style/vertical_line"
                        android:orientation="horizontal" />

                    <LinearLayout
                        android:id="@+id/order_company_column"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:layout_weight=".25"
                            android:gravity="center_vertical"
                            android:text="@string/order_company"

                            android:textColor="@color/light_blue"
                            android:textSize="17sp"

                            app:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/order_company"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight=".75"
                            android:gravity="center_vertical|right|end"
                            android:paddingRight="15dp"
                            android:textColor="@color/black"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif-light"
                            tools:ignore="RtlHardcoded,RtlSymmetry" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/order_user_info_line1"
                        style="@style/vertical_line"
                        android:orientation="horizontal"/>

                    <LinearLayout
                        android:id="@+id/order_user_column"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:layout_weight=".2"
                            android:gravity="center_vertical"
                            android:text="@string/order_staff"

                            android:textColor="@color/light_blue"
                            android:textSize="17sp"

                            app:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/order_staff"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight=".7"
                            android:gravity="center_vertical|right|end"
                            android:paddingRight="15dp"
                            android:textColor="@color/black"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif-light"
                            tools:ignore="RtlHardcoded,RtlSymmetry" />

                        <TextView
                            android:id="@+id/textView2"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:layout_weight="0.1"

                            android:gravity="center_vertical"
                            android:text="@string/order_honor"

                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/order_user_info_line2"
                        style="@style/vertical_line"
                        android:orientation="horizontal" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/order_info_layout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/order_info_title"
                        style="@style/section_sub_title"
                        android:layout_height="30dp"
                        android:text="@string/order_detail_section"
                        app:fontFamily="sans-serif-light" />

                    <LinearLayout
                        android:id="@+id/order_info_line2"
                        style="@style/vertical_line"
                        android:orientation="horizontal"/>

                    <LinearLayout
                        android:id="@+id/task_name_column"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:layout_weight=".2"
                            android:gravity="center_vertical"

                            android:text="@string/order_task_name"
                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/task_name"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight=".7"
                            android:gravity="center_vertical|right"
                            android:paddingRight="15dp"
                            android:textColor="@color/black"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif-light"
                            tools:ignore="RtlHardcoded,RtlSymmetry" />

                        <TextView
                            android:id="@+id/textView3"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:layout_weight="0.15"

                            android:gravity="center_vertical"
                            android:text="@string/order_honorific"
                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/order_info_line1"
                        style="@style/vertical_line"
                        android:orientation="horizontal"/>

                    <LinearLayout
                        android:id="@+id/task_address_column"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:layout_weight=".15"
                            android:gravity="center_vertical"
                            android:text="@string/order_address"
                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/address"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight=".85"
                            android:gravity="center_vertical|right"
                            android:paddingRight="15dp"
                            android:textColor="@color/black"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif-light"
                            tools:ignore="RtlHardcoded,RtlSymmetry" />


                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/order_info_line3"
                        style="@style/vertical_line"
                        android:orientation="horizontal" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/survey_date_info_layout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/survey_date_title"
                        style="@style/section_sub_title"
                        android:layout_height="15dp"
                        android:orientation="horizontal"/>

                    <LinearLayout
                        android:id="@+id/survey_date_info_line3"
                        style="@style/vertical_line"
                        android:orientation="horizontal" />

                    <LinearLayout
                        android:id="@+id/date1_column"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:layout_weight=".3"
                            android:gravity="center_vertical"
                            android:text="@string/research_date1"
                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/research_date1"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical|right|end"
                            android:layout_weight=".7"
                            android:gravity="center_vertical|right|end"
                            android:paddingRight="15dp"
                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif-light"
                            tools:ignore="RtlHardcoded,RtlSymmetry" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/survey_date_info_line4"
                        style="@style/vertical_line"
                        android:orientation="horizontal" />

                    <LinearLayout
                        android:id="@+id/date_line1"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:layout_weight=".3"
                            android:gravity="center_vertical"
                            android:text="@string/research_date2"
                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/research_date2"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical|right|end"
                            android:layout_weight=".7"
                            android:gravity="center_vertical|right|end"
                            android:paddingRight="15dp"
                            android:textColor="@color/black"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif-light"
                            tools:ignore="RtlHardcoded,RtlSymmetry" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/survey_date_info_line2"
                        style="@style/vertical_line"
                        android:orientation="horizontal" />

                    <LinearLayout
                        android:id="@+id/date3_column"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@color/white"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginLeft="15dp"
                            android:layout_weight=".3"
                            android:gravity="center_vertical"
                            android:text="@string/research_date3"
                            android:textColor="@color/light_blue"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/research_date3"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical|right|end"
                            android:layout_weight=".7"
                            android:gravity="center_vertical|right|end"
                            android:paddingRight="15dp"
                            android:textColor="@color/black"
                            android:textSize="17sp"
                            app:fontFamily="sans-serif-light"
                            tools:ignore="RtlHardcoded,RtlSymmetry" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/survey_date_info_line1"
                        style="@style/vertical_line"
                        android:orientation="horizontal">

                    </LinearLayout>

                </LinearLayout>


                <LinearLayout
                    android:id="@+id/remaks_info_layout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/remarks_title"
                        style="@style/section_sub_title"
                        android:layout_height="15dp"
                        android:orientation="horizontal"/>

                    <LinearLayout
                        android:id="@+id/remarks_info_line1"
                        style="@style/vertical_line"
                        android:orientation="horizontal" />

                    <LinearLayout
                        android:id="@+id/remarks_column"
                        android:layout_width="match_parent"
                        android:layout_height="80dp"
                        android:background="@color/white"
                        android:orientation="horizontal">


                        <TextView
                            android:id="@+id/remarks"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="start|left"
                            android:background="#00000000"
                            android:gravity="start|left"
                            android:hint="@string/remarks"
                            android:paddingLeft="15dp"
                            android:paddingTop="5dp"
                            android:paddingRight="10dp"
                            android:paddingBottom="5dp"
                            android:textColor="@color/black"
                            android:textSize="16sp"
                            app:fontFamily="sans-serif-light"
                            tools:ignore="RtlHardcoded,RtlSymmetry" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/remarks_info_line2"
                        style="@style/vertical_line"
                        android:orientation="horizontal" />

                    <LinearLayout
                        android:id="@+id/remarks_footer"
                        style="@style/section_sub_title"
                        android:layout_height="15dp"
                        android:orientation="horizontal"/>

                </LinearLayout>


            </LinearLayout>

        </RelativeLayout>
    </ScrollView>

    <LinearLayout
        android:id="@+id/footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:orientation="vertical"
        tools:ignore="RtlHardcoded">

        <LinearLayout
            android:id="@+id/confirm_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="visible">

            <LinearLayout
                android:id="@+id/confirm_line"
                style="@style/vertical_line_dark"
                android:orientation="horizontal">

            </LinearLayout>

            <TextView
                android:id="@+id/confirm_do_research"
                android:layout_width="match_parent"
                android:layout_height="42dp"
                android:layout_margin="8dp"
                android:background="@drawable/radius_frame_blue"
                android:gravity="center"
                android:text="@string/order_confirm_do_research"
                android:textColor="@color/white"
                android:textSize="16sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/readjustment_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="gone">

            <LinearLayout
                android:id="@+id/readjustment_line"
                style="@style/vertical_line_dark"
                android:orientation="horizontal" />

            <RelativeLayout
                android:id="@+id/readjustment"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@color/order_detail_button_back"
                android:gravity="center">

                <TextView
                    android:id="@+id/order_readjustment"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="@string/order_readjustment"
                    android:textColor="@color/light_blue"
                    android:textSize="18sp" />
            </RelativeLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/cancel_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="gone">

            <LinearLayout
                android:id="@+id/cancel_line"
                style="@style/vertical_line_dark"
                android:orientation="horizontal" />

            <RelativeLayout
                android:id="@+id/cancel_text"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@color/order_detail_button_back"
                android:gravity="center">

                <ImageView
                    android:id="@+id/order_cancel_icon"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:paddingTop="15dp"
                    android:paddingBottom="15dp"
                    android:src="@drawable/delete"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="0dp"
                    android:layout_toRightOf="@+id/order_cancel_icon"
                    android:gravity="center"
                    android:text="@string/order_cancel"
                    android:textColor="@color/red"
                    android:textSize="18sp" />
            </RelativeLayout>

        </LinearLayout>

    </LinearLayout>
</RelativeLayout>
