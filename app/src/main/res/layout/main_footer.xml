<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/com_footer_btbk"
        android:orientation="horizontal"
        tools:ignore="MissingConstraints">

        <LinearLayout
            android:id="@+id/footer_daily_report"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight=".6"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/footer_daily_report_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center"
                android:scaleType="fitCenter"
                android:src="@drawable/menu_report_hover" />

            <TextView
                android:id="@+id/footer_daily_report_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/main_footer_daily_report"
                android:textColor="@color/half_transparent"
                android:textSize="8sp"
                android:autoSizeMaxTextSize="8sp"
                android:autoSizeMinTextSize="4sp"
                android:textStyle="bold"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/footer_schedule"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1.2"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/footer_schedule_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center"
                android:scaleType="fitCenter"
                android:src="@drawable/menu_schedule_hover" />

            <TextView
                android:id="@+id/footer_schedule_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/main_footer_schedule"
                android:textColor="@color/half_transparent"
                android:textSize="8sp"
                android:autoSizeMaxTextSize="8sp"
                android:autoSizeMinTextSize="4sp"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/footer_survey_request"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/footer_survey_request_icon"
                android:layout_width="19dp"
                android:layout_height="21dp"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="5dp"
                android:gravity="center"
                android:scaleType="fitCenter"
                android:src="@drawable/menu_request_hover" />

            <TextView
                android:id="@+id/footer_survey_request_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/main_footer_survey_request"
                android:textColor="@color/half_transparent"
                android:textSize="8sp"
                android:autoSizeMaxTextSize="8sp"
                android:autoSizeMinTextSize="4sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/footer_actual_place_survey"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/footer_actual_place_survey_icon"
                android:layout_width="21dp"
                android:layout_height="17dp"
                android:layout_marginTop="6dp"
                android:layout_marginBottom="7dp"
                android:gravity="center"
                android:scaleType="fitCenter"
                android:src="@drawable/menu_research_hover" />

            <TextView
                android:id="@+id/footer_actual_place_survey_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/main_footer_actual_place_survey"
                android:textColor="@color/half_transparent"
                android:textSize="8sp"
                android:autoSizeMaxTextSize="8sp"
                android:autoSizeMinTextSize="4sp"
                android:textStyle="bold" />
        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/footer_message"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1.1">

            <LinearLayout
                android:id="@+id/linearLayout2"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/footer_message_icon"
                    android:layout_width="23dp"
                    android:layout_height="21dp"
                    android:layout_gravity="center"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="5dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/menu_message_hover" />

                <TextView
                    android:id="@+id/footer_message_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:text="@string/main_footer_message"
                    android:textColor="@color/half_transparent"
                    android:textSize="8sp"
                    android:autoSizeMaxTextSize="8sp"
                    android:autoSizeMinTextSize="4sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <TextView
                android:id="@+id/main_unread_badge_all"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginLeft="25dp"
                android:layout_marginTop="1dp"
                android:background="@drawable/unread_badge"
                android:gravity="center"
                android:paddingBottom="1dp"
                android:textColor="@color/white"
                android:visibility="visible"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/footer_workschedule"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/footer_workschedule_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center"
                android:scaleType="fitCenter"
                android:src="@drawable/menu_work_schedule_hover" />

            <TextView
                android:id="@+id/footer_workschedule_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/main_footer_workschedule"
                android:textColor="@color/half_transparent"
                android:textSize="8sp"
                android:autoSizeMaxTextSize="8sp"
                android:autoSizeMinTextSize="4sp"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/footer_config"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight=".9"
            android:gravity="center"
            android:orientation="vertical"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/footer_config_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center"
                android:scaleType="fitCenter"
                android:src="@drawable/menu_config_hover" />

            <TextView
                android:id="@+id/footer_config_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/main_footer_config"
                android:textColor="@color/half_transparent"
                android:textSize="8sp"
                android:autoSizeMaxTextSize="8sp"
                android:autoSizeMinTextSize="4sp"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/footer_movie"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1.2"
            android:gravity="center"
            android:orientation="vertical"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/footer_movie_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center"
                android:scaleType="fitCenter"
                android:src="@drawable/fuji_channel" />

            <TextView
                android:id="@+id/footer_movie_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/main_footer_movie"
                android:textColor="@color/half_transparent"
                android:textSize="8sp"
                android:autoSizeMaxTextSize="8sp"
                android:autoSizeMinTextSize="4sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>