<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!--<item android:state_pressed="true">-->
        <!--<shape android:shape="rectangle">-->
            <!--<solid android:color="#F08000"></solid>-->
        <!--</shape>-->
    <!--</item>-->
    <!--<item android:state_focused="true">-->
        <!--<shape android:shape="rectangle">-->
            <!--<solid android:color="#44F08000"></solid>-->
            <!--<stroke android:color="#F08000" android:width="2dp"/>-->
        <!--</shape>-->
    <!--</item>-->
    <item
        android:state_focused="true"
        android:state_enabled="true"
        android:state_pressed="true"
        android:drawable="@drawable/balloon"
        >

    </item>
</selector>