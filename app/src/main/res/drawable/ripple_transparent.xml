<?xml version="1.0" encoding="utf-8"?>
<!-- タップ時にoval型の#59FFFFFF色の波紋を出すレイアウト(デフォルト透明) -->
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:color="#33000000"
    tools:ignore="NewApi">
    <!-- デフォルト色がtransparentの場合はmask指定が必要 -->
    <item android:id="@android:id/mask"
        tools:ignore="NewApi">
        <shape android:shape="rectangle">
            <!-- ここのcolorは指定が必要だが反映はされない -->
            <solid android:color="@android:color/white" />
        </shape>
    </item>
    <!-- デフォルト状態の背景 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
</ripple>